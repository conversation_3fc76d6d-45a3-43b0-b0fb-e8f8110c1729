import {ref, computed, Ref} from 'vue';
import {AnyObj} from '../types';
import * as validators from './validators';
import {undoRedo, UndoRedoOptions, _get, _set, _isequal, _pick} from 'symbol-syntax-utils';
// type VDator = Array<string|number>;

export type VStaticOptions = { ur?: UndoRedoOptions, formFn?: (defs?:AnyObj) => AnyObj};
const VStatic = (options?: VStaticOptions) => {

    const vErrors = ref({});
    const vDirty:Ref<AnyObj> = ref({});
    const isVDirty = ref(false);
    const getFormFn = options?.formFn ? options.formFn : () => {
        return {}
    };
    const form:Ref<any> = ref(getFormFn());
    let change = (val:any, key?:string) => {
        if(key) form.value[key] = val;
        else form.value = Object.assign({}, val);
    };
    const oldForm = ref({});
    const hasVErrors = ref(false);

    if (options?.ur) {
        const ur = undoRedo(options.ur)
        form.value = ur.form;
        change = ur.change;
    }

    const getErrorMessage = computed(() => {
        return (prop:any) => {
            const dirty = vDirty.value || {};
            if (_get(dirty, prop)) {
                return _get(vErrors.value, prop, '');
            } else return '';
        };
    });

    const checkRule = computed(() => {
        return (prop:any) => {
            const dirty = vDirty.value || {};
            if (_get(dirty, prop)) {
                const err = _get(vErrors.value, prop, false);
                return err ? [err] : true;
            } else return true;
        };
    });

    const checkError = computed(() => {
        return (prop:any) => {
            const dirty = vDirty.value || {};
            let err = false
            if (_get(dirty, prop)) {
                err = !!_get(vErrors.value, prop, false);
            }
            hasVErrors.value = err;
            return err;
        };
    });

    const getErrorList = computed(() => {
        return (errs?: AnyObj): string[] => {
            const errors: AnyObj = errs ? errs : vErrors.value;
            const errorList: string[] = [];
            const list = errors ? Object.keys(errors) : [];

            const pushErr = (key:string) => {
                if (_get(errors, key)) {
                    if (_get(errors, key) && typeof _get(errors, key) === 'object') {
                        Object.keys(_get(errors, key) as object).forEach(k => {
                            pushErr(`${key}.${k}`);
                        });
                    } else {
                        errorList.push(_get(errors, key) as string);
                    }
                }
            };

            list.forEach(key => {
                if (errors[key]) {
                    pushErr(key);
                }
            });
            return errorList;
        };
    });

    const setForm = (payload:any, key?:string) => {
        change(payload, key);
    };
    const vErrorMessage = (prop:any): string => {
        const dirty = vDirty.value ? Object.assign({}, vDirty.value) : {};

        if (_get(dirty, prop)) {
            return _get(vErrors.value, prop, '') as string;
        } else return '';
    };

    type CheckFn = (fieldObj:AnyObj, arg:any, format?:any) => boolean;
    type ErrFn = (field:any, val:any, def:any) => string;
    // valid = { name: string, v: Array<string> | object }

    /**
     *
     * @param form - object being validated
     * @param valid - { name: string, v: Array<string> | object }
     */
    const vCheck = (form:AnyObj, valid:AnyObj): string[] => {
        //You can pass in a custom validCheck such as in needing to use this for two objects in one component

        let errors = {};
        const key_list = valid ? Object.keys(valid) : [];

        const checkFn = (path:string|string[], v:AnyObj) => {
            const keyProp = _get(v, path);
            const methods = _get(keyProp, 'v');
            // console.log('checkfn', path, keyProp, methods);
            if (methods) {
                if (Array.isArray(methods)) {
                    methods.forEach(method => {
                        // console.log('checking method', path, form, keyProp);
                        const fieldObj = {
                            value: _get(form, path),
                            name: _get(keyProp, 'name', path[path.length - 1])
                        };
                        const v = method.split(':');
                        // console.log('v', v, methods, method);
                        const prop = v[0];
                        const arg = v[1];
                        // console.log('prop', prop, _get(validators, [prop]));
                        const validator = _get(validators, prop) as AnyObj;
                        const check = validator && validator['method'] ? (_get(validator, 'method') as CheckFn)(fieldObj, arg) : null;
                        // console.log('check', check, fieldObj, arg);
                        if (!check && validator['err']) errors = _set(errors, path, (_get(validator, 'err') as ErrFn)(fieldObj, arg, ''));
                        else {
                            // console.log('check success - removing error for ', path);
                           errors = _set(errors, path, null);
                        }
                    });
                } else {

                    //structure as { check: string, arg: string, format: CustomCheckFn, err: string }
                    /**
                     * Example structure v: { check: 'format', arg: 'type'|undefined, format: (field) => some validation function for field}
                     */

                    const fieldObj = {value: _get(form, path), name: _get(keyProp, 'name', path[path.length - 1])};
                    // console.log('got field obj', fieldObj, key, form);

                    const prop = _get(methods, 'check') as keyof typeof validators;
                    const arg = _get(methods, 'arg');

                    //For format functions remember that the field value is at .value
                    const validator = _get(validators, prop);

                    const format = _get(methods, 'format');
                    const error = validator ? (_get(validator, 'err') as ErrFn)(fieldObj, arg, _get(methods, 'err')) : _get(methods, 'err');
                    // console.log('prop', methods, key, prop, validators[prop]);

                    // console.log('validator', fieldObj, arg, format)
                    const check = validator ? (_get(validator, 'method') as CheckFn)(fieldObj, arg, format) : true;
                    // console.log('not array', 'key', key, 'prop', prop, 'arg', arg, 'format', format, 'error', error, 'check', check);
                    if (!check) {
                        // console.log('ggot err', validators[prop].err(fieldObj, arg, error), fieldObj, arg);
                        errors = _set(errors, path, (_get(validator, ['err']) as ErrFn)(fieldObj, arg, error));
                    } else {
                        // console.log('check success - removing error for ', key);
                        errors = _set(errors, path, null);
                    }
                }
            } else {
                const newObj = _get(v, path) as AnyObj;
                if (newObj && typeof newObj === 'object') {
                    Object.keys(newObj).map(a => checkFn([...path, a], newObj))
                }
            }
        }

        // console.log('mapping check', key_list, valid);
        key_list.map(a => checkFn([a], valid));
        // console.log('set errors', key_list, errors);
        if (errors) {
            hasVErrors.value = true;
            vErrors.value = Object.assign({}, errors);
        } else {
            hasVErrors.value = false;
            vErrors.value = {};
        }
        return getErrorList.value(errors);
    };
    const clearForm = (val = {}) => {
        form.value = val;
    };
    const refresh = (nv:any, oV:any, vDators:AnyObj) => {
        if (oV && typeof oV === 'object' && Object.keys(oV).length) {
            const ov = oldForm.value ? oldForm : oV;
            const equal = _isequal(nv, ov);
            if (ov && !equal && nv) {
                if(nv && oV) isVDirty.value = true;
                // const layerDeep = (val:any, oVal:any, path?: string) => {
                //     Object.keys(val).forEach(key => {
                //         const newPath = path ? `${path}.${key}` : key;
                //         const getVal = _get(val, key, '');
                //         const getOVal = _get(oVal, key, '');
                //         console.log('check equal', _isequal(getVal, getOVal));
                //         if (!!(getVal || getOVal) && !_isequal(getVal, getOVal)) {
                //             if (getVal && typeof getVal === 'object' && !Array.isArray(getVal)) {
                //                 layerDeep(getVal, _get(oVal, key), newPath);
                //             } else {
                //                vDirty.value[newPath] = true;
                //                 // console.log('set dirty', vDirty);
                //             }
                //         }
                //     });
                // };
                // layerDeep(nv, ov, undefined);
                // let errs = $vCheck(nv);
                vCheck(nv, vDators);
            } else oldForm.value = Object.assign({}, oV);
        }
        // console.log('pick', vErrors.value, vDirty.value);
        return _pick<AnyObj, string>(vErrors.value, Object.keys(vDirty.value));
    }

    return {
        vErrors,
        vDirty,
        isVDirty,
        form,
        oldForm,
        hasVErrors,
        getErrorMessage,
        checkRule,
        checkError,
        getErrorList,
        setForm,
        vErrorMessage,
        vCheck,
        clearForm,
        refresh
    }

};

export default VStatic;
