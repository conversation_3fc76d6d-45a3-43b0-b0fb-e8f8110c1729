// import validators from './validators';
import {AnyObj} from 'src/utils/types';
import {Notify} from 'quasar';
import {_get as lget, _isempty as lisEmpty, _flattendeep as lflattenDeep} from 'symbol-syntax-utils';


// declare interface vCheckOpts {
//     value?: AnyObj,
//     touchy?: boolean
// }

export const vErrNotify = (err:any) => {
    Notify.create({
        message: err,
        position: 'bottom',
        color: 'negative',
        timeout: 10000,
        actions: [{icon: 'mdi-close', color: 'white'}],
        icon: 'mdi-information'
    });
}

export const successNotify = (message = 'Success!') => {
    Notify.create({
        message,
        position: 'bottom',
        color: 'positive',
        timeout: 2000,
        actions: [{icon: 'mdi-close', color: 'white'}],
        icon: 'mci-check'
    })
}


export const flatObjKeyList = (obj: AnyObj, path?: string):string[] => {
    const list: string[] = [];
    if (!lisEmpty(obj)) {
        Object.keys(obj).forEach(key => {
            const newPath = path ? `${path}.${key}` : key;
            const getVal = lget(obj, key) as AnyObj;
            // console.log('see change');
            const pathToAdd = typeof getVal === 'object' && !Array.isArray(getVal) ? flatObjKeyList(getVal, newPath) : [newPath];
            list.concat(pathToAdd);
        });
    }
    return lflattenDeep(list);
};


//
// watch(value, (nv, ov) => {
//     if(touchy) refresh(nv, ov);
// }, { immediate: true, deep: true })


export default {
    vErrNotify,
    flatObjKeyList
}
