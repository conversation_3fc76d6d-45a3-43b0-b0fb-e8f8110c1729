import {_get} from 'symbol-syntax-utils';

type Field = { value: any, name: string, [key:string]: any };
type FormatFn<T> = ((val:T) => boolean);
type Check<T, F> = (field:Field, check:T, format?:F) => boolean
type V<T, F> = {
    label?: string,
    description?: string,
    args?: boolean,
    method: Check<T, F>,
    err: (field:Field, check:T, error?:string) => string
}

export const passwordStrength = (val:string): string => {

    const strongRegex = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})');
    const mediumRegex = new RegExp('^(((?=.*[a-z])(?=.*[A-Z]))|((?=.*[a-z,A-Z])(?=.*[0-9]))|((?=.*[a-z,A-Z])(?=.*[!@#$%^&*]))|((?=.*[A-Z])(?=.*[0-9])))(?=.{6,})');
    if (typeof val !== 'string') {
        throw new TypeError(`path: plugins/validators.js  --  password strength validation error: expected type 'string' - got ${typeof val}`);
    } else if (!val.length) {
        return 'empty';
    } else if (strongRegex.test(val)) {
        return 'strong';
    } else if (mediumRegex.test(val)) {
        return 'medium';
    } else {
        return 'weak';
    }
};

export const isEmailRule = (val: string): boolean => {
    const reg = /^(([^<>()[\]\\.,;:\s@']+(\.[^<>()[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/;
    return reg.test(val);
};

export const notEmpty:V<string, FormatFn<Field>> = {
    label: 'Not Empty',
    description: 'Make sure field is not empty',
    args: false,
    method: (field:Field, type, format) => {
        const fieldCheck = field && (field.value || field.value === 0) && typeof field.value !== 'undefined';
        const typeCheck = type ? typeof field.value === type : true;
        const formatCheck = format ? format(field) : true;
        return fieldCheck && typeCheck && formatCheck;
    },
    err: (field, type, error) => {
        return error ? error : `${field.name} cannot be empty.`;
    }
};
export const eq:V<any, FormatFn<Field>> = {
    label: 'Equals',
    description: 'Field must equal',
    args: true,
    method: (field, val, format) => {
        const fieldCheck = field && field.value === val;
        const formatCheck = format ? format(field) : true;
        return fieldCheck && formatCheck;
    },
    err: (field, val, error) => {
        return error ? error : `${field.name} must equal ${val}`;
    }
};
export const ne:V<any, FormatFn<Field>> = {
    label: 'Does not equal',
    description: 'Field must not equal',
    args: true,
    method: (field, val, format) => {
        const fieldCheck = field && JSON.stringify(field.value) !== JSON.stringify(val);
        const formatCheck = format ? format(field) : true;
        return fieldCheck && formatCheck;
    },
    err: (field, val, error) => {
        return error ? error : `${field.name} cannot equal ${val}.`;
    }
};

export const exactLength:V<string|Array<any>, FormatFn<Field>> = {
    label: 'Length',
    description: 'Length exactly',
    args: true,
    method: (field, val, format) => {
        const fieldCheck = field && field.value && String(String(field.value).length) === val;
        const formatCheck = format ? format(field) : true;
        return fieldCheck && formatCheck;
    },
    err: (field, val, error) => {
        return error ? error : `${field.name} must have a length of ${val}.`;
    }
};
const type:V<any, FormatFn<Field>> = {
    label: 'Type',
    description: 'Validate field type (String, Number, List)',
    args: true,
    method: (field, type, format) => {
        const typeCheck = type ? typeof field.value === type : true;
        const formatCheck = format ? format(field) : true;
        return typeCheck && formatCheck;
    },
    err: (field, type, error) => {
        return error ? error : `${field.name} must be type ${type}.`;
    }
};
export const format:V<any, FormatFn<Field>> = {
    method: (field, type, format) => {
        const typeCheck = type ? typeof field.value === type : true;
        // console.log('fmt here', type, typeCheck, field, format(field));
        const formatCheck = format ? format(field) : true;
        return typeCheck && formatCheck;
    },
    err: (field, type, error) => {
        return error ? error : `${field.name} - error`;
    }
};
export const sameAs:V<string|number, string|number> = {
    method: (field, type, format) => {
        const typeCheck = type ? typeof field.value === type : true;
        const formatCheck = format ? field.value === format : true;
        return typeCheck && formatCheck;
    },
    err: (field, type, error) => {
        return error ? error : `${field.name} - error`;
    }
};
export const arrayLength:V<number|string, undefined> = {
    label: 'List Length',
    description: 'Check that list is at least some length',
    args: true,
    method: (field, number) => {
        return Array.isArray(field.value) && field.value.length >= Number(number);
    },
    err: (field, number, error) => {
        return `${field.name} requires at least ${number} selection${number === '1' ? '' : 's'}. ${error ? error : ''}`;
    }
};
export const gt:V<number|string, undefined> = {
    label: 'Greater than',
    description: 'Check that number is greater than or string has more characters than',
    args: true,
    method: (field, number) => {
        if (typeof field.value === 'number') return field.value > Number(number);
        else if (['array', 'string'].includes(typeof field.value)) {
            const length = (_get(field, 'value', []) || []).length;
            return length > Number(number);
        } else return false;
    },
    err: (field, number, error) => {
        return `${field.name} ${typeof field.value === 'number' ? '' : 'length'} must be greater than ${number}. ${error ? error : ''}`;
    }
};
export const lt:V<number|string, undefined> = {
    label: 'Less than',
    description: 'Check that number is less than or string has less characters than',
    args: true,
    method: (field, number) => {
        if (typeof field.value === 'number') return field.value < Number(number);
        else if (['array', 'string'].includes(typeof field.value)) {
            const length = (_get(field, 'value', [])||[]).length;
            return length < Number(number);
        } else return false
    },
    err: (field, number, error) => {
        return `${field.name} ${typeof field.value === 'number' ? '' : 'length'} cannot exceed ${number}. ${error ? error : ''}`;
    }
};
export const password:V<{length:number, uppercase:number, lowercase:number, special:number}, undefined> = {
    label: 'Password',
    description: 'Check for password strength',
    args: true,
    // TODO: functions dont actually check for count of special, uppercase lowercase - just qthat there is one
    method: (field, type = {length: 8, uppercase: 1, lowercase: 1, special: 1}) => {
        const v = (_get(field, 'value', 'a') || 'a') as string;
        const vLen = v?.length || 0;
        const len = _get(type, 'length') ? vLen >= length : true;
        const upper = _get(type, 'uppercase') ? v.toLowerCase() !== v : true;
        const lower = _get(type, 'lowercase') ? v.toUpperCase() !== v : true;
        // eslint-disable-next-line no-useless-escape
        const spec = _get(type, 'special') ? /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(v) : true;
        // console.log('password check', field, type, v, len, upper, lower, spec);
        return (len && upper && lower && spec);
    },
    err: (field, type = {length: 8, uppercase: 1, lowercase: 1, special: 1}, error) => {
        // console.log('run err', field, type, error);
        if (error) return error;
        else {
            const v = (_get(field, 'value', 'a') || 'a') as string;
            const len = _get(type, 'length') ? v.length >= length : true;
            const upper = _get(type, 'uppercase') ? v.toLowerCase() !== v : true;
            const lower = _get(type, 'lowercase') ? v.toUpperCase() !== v : true;
            // eslint-disable-next-line no-useless-escape
            const spec = _get(type, 'special') ? /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(v) : true;
            let str = 'Password Requirements:';
            if (!len) str += ` At least ${length} characters -`;
            if (!upper) str += ` At lease ${_get(type, 'uppercase')} upper case letter${_get(type, 'uppercase') === 1 ? '' : 's'}`;
            if (!lower) str += ` At lease ${_get(type, 'lowercase')} lower case letter${_get(type, 'lowercase') === 1 ? '' : 's'}`;
            if (!spec) str += ` At lease ${_get(type, 'special')} special character${_get(type, 'special') === 1 ? '' : 's'} (!@#$%^&*()_+)`;
            return str;
        }
    }
};
export const email:V<string, undefined> = {
    label: 'Email',
    description: 'Check for a valid email',
    args: false,
    method: (field, type) => {
        const typeCheck = type ? typeof field.value === type : true;
        const formatCheck = isEmailRule(field.value);
        return typeCheck && formatCheck;
    },
    err: (field, type, error) => {
        return error ? error : `${field.name} - Enter a valid email`;
    }
};
export const phone:V<undefined, undefined> = {
    label: 'Phone',
    description: 'Check for a valid phone',
    args: false,
    method: (field) => {
        if (['number', 'string'].includes(typeof field.value)) {
            //eslint-disable-next-line
            let numTest = new RegExp('^\\s*(?:\\+?(\\d{1,3}))?[-. (]*(\\d{3})[-. )]*(\\d{3})[-. ]*(\\d{4})(?: *x(\\d+))?\\s*$');
            return field.value.valid || numTest.test(field.value);
        } else return _get(field, 'value.valid');
    },
    err: (field, type, error) => {
        return error ? error : `${field.name} - Enter a valid phone number`;
    }
};

export default {
    phone,
    email,
    password,
    lt,
    gt,
    arrayLength,
    sameAs,
    format,
    type,
    length: exactLength,
    exactLength,
    ne,
    eq,
    notEmpty
};
