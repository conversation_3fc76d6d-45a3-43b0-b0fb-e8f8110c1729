import {_get} from 'symbol-syntax-utils';
import {RRule} from 'rrule';

type RRULE = any


export const getRuleObj = (rule: RRULE) => {
    let byweekday = null;
    if (Array.isArray(_get(rule, 'BYDAY', null))) {
        _get(rule, 'BYDAY', []).forEach(day => {
            if(Array.isArray(byweekday) && byweekday.length) byweekday.push(RRule[day])
            else byweekday = [RRule[day]];
        });
    } else if (rule.BYDAY && typeof rule.BYDAY === 'string') {
        byweekday = RRule[rule.BYDAY];
    }
    const obj: any = {
        freq: RRule[rule.FREQ],
        count: _get(rule, 'COUNT', null),
        interval: _get(rule, 'INTERVAL', 1),
        bymonth: _get(rule, 'BYMONTH', null),
        byweekno: _get(rule, 'BYWEEKNO', null),
        bymonthday: _get(rule, 'BYMONTHDAY', null),
        byweekday: byweekday,
        byyearday: _get(rule, 'BYYEARDAY', null),
        byminute: _get(rule, 'BYMINUTE', null),
        byhour: _get(rule, 'BYHOUR', null),
        until: _get(rule, 'UNTIL', null),
        wkst: _get(rule, 'WKST', null),
        bysecond: null,
    };
    const kys = Object.keys(obj);
    const ruleObj = {};
    kys.forEach(k => {
        if ((obj[k] || obj[k] === 0) && obj[k] !== null && typeof obj[k] !== 'undefined') {
            ruleObj[k] = obj[k];
            // console.log('setting obj property', k, obj[k]);
        }
    });
    // console.log('ruleobj', ruleObj);
    return new RRule(ruleObj);
}

export const recurrenceString = (rule: any) => {
    if (rule) {
        // console.log('get str', rule, rule.FREQ);
        const rrule = getRuleObj(rule);
        // console.log('rrule obj', rrule);
        // console.log('rrule str', rrule.toString());
        const r = RRule.fromString(rrule.toString());
        // console.log('rrule r');
        // console.log('rrule', r, rrule.toString(), RRule.YEARLY, RRule.MONTHLY, RRule.WEEKLY, RRule.DAILY, RRule.HOURLY, RRule.MINUTELY);
        return r.toText();
    } else return 'One Time';
}

export const nextRecurrence = (rule: RRULE, date: string | Date, inc: boolean) => {
    const rrule = getRuleObj(rule);
    const d = new Date(date);
    // console.log('recurrence plugin >> rrule', rrule.toString(), rrule.all(function (date, i) {
    //     console.log('inside', i, date);
    //     return i < 2;
    // }));
    return !inc ? rrule.after(d) : rrule.after(d, inc = true);
}
export const lastRecurrence = (rule: RRULE, date: string | Date, inc: boolean) => {
    const d = new Date(date);

    const rrule = getRuleObj(rule);
    return !inc ? rrule.before(d) : rrule.before(d, inc = true);
}
