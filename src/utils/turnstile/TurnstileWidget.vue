<template>
  <div>
    <div :id="id">
    </div>
  </div>
</template>

<script setup>
  import {nextTick, onMounted, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useFingerprints} from 'stores/fingerprints';

  const fpStore = useFingerprints();
  const emit = defineEmits('update:model-value', 'update:interactive');
  const props = defineProps({
    id: { default: 'CcTurnstile' },
    interactive: Boolean,
    modelValue: Boolean,
    dark: Boolean
  })
  const fp = ref();
  const cb = async (token) => {
    // console.log('cb', token)
    if (props.modelValue) return;
    const res = await fpStore.patch(fp.value._id, { updatedAt: new Date() }, { crypto: { turnstile: token } })
    if (res.turnstile.success) {
      emit('update:model-value', true);
      emit('interactive', false);
    }
  }

  const init = async (count = 0) => {
    let fpId = LocalStorage.getItem('fpId');
    if (!fpId && count < 10) {
      return init(count + 1)
    }
    fp.value = fpStore.getFromStore(fpId)?.value;
    if (!fp.value) fp.value = await fpStore.get(fpId);
    if (fp.value?.turnstile?.success && (new Date().getTime() - new Date(fp.value.turnstile.challenge_ts || '01-01-2000').getTime() < (1000 * 60 * 60))) {
      emit('update:model-value', true);
      emit('interactive', false);
    }
    else {
      if (!document.getElementById('c-turnstile')) {
        const scriptTag = document.createElement('script');
        scriptTag.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js?onload=ccTurnNBurn';
        scriptTag.id = 'c-turnstile';
        scriptTag.async = true;
        scriptTag.defer = true;
        document.getElementsByTagName('head')[0].appendChild(scriptTag);
      }
      window.ccTurnNBurn = () => {
        setTimeout(() => {
          turnstile.render(`#${props.id}`, {
            sitekey: '0x4AAAAAAA7BhwQEEOo3fbzu',
            callback: cb,
            'before-interactive-callback': emit('update:interactive', true),
            // 'after-interactive-callback': emit('update:interactive', false),
            theme: props.dark ? 'dark' : 'light'
          })
        }, 50)
      }
    }
  }

  onMounted(() => {
    nextTick(() => init())
  })
</script>

<style lang="scss" scoped>

</style>
