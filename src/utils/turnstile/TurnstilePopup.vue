<template>
  <div :class="`__turn_pop ${interactive ? '' : '__off'}`">
    <turnstile-widget
        @update:model-value="emitUp('update:model-value', $event)"
        @update:interactive="emitUp('update:interactive', $event)"
        v-bind="{
      modelValue,
      interactive
        }"
    ></turnstile-widget>
  </div>
</template>

<script setup>
  import TurnstileWidget from './TurnstileWidget.vue';

  const emit = defineEmits(['update:model-value', 'update:interactive'])
  const props = defineProps({
    modelValue: Boolean,
    interactive: Boolean
  })

  const emitUp = (evt, ...args) => {
    if(evt === 'update:model-value' && args[0]) emit('update:interactive', false);
    emit(evt, ...args)
  }

</script>

<style lang="scss" scoped>
  .__turn_pop {
    box-shadow: 0 -2px 4px rgba(99,99,99,.1);
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: auto;
    max-width: 100vw;
    background: var(--ir-bg1);
    padding: 5px;
    border-radius: 8px 8px 0 0;
    border: solid 5px var(--q-primary);
    border-bottom: none;
    max-height: 200px;
    transition: all .3s;
    z-index: 100000;
    display: grid;
    grid-template-columns: auto;
    align-items: center;
    justify-items: center;
  }
  .__off {
    max-height: 0;
    padding: 0;
    overflow: hidden;
    opacity: 0;
    pointer-events: none;
    border: none;
  }
</style>
