import {date, DateUnitOptions, DateOptions} from 'quasar';
import getDateDiff = date.getDateDiff;
const { isSameDate: isd } = date;

export declare type Dates = Date|string|number;
export const formatDate = (date1:Dates, format?:string) => {
    const f = format ? format : 'ddd MMM-DD, YYYY';
    return date.formatDate(date1, f);
}

export const isValid = (dt:Dates) => {
    if(!['number', 'string'].includes(typeof dt)) dt = new Date(dt);
    return date.isValid(dt as string|number);
}

type Units = 'seconds'|'minutes'|'hours'|'days'|'weeks'|'months'|'years'
export const msUnits = (number:number, units:Units) => {
    const minutes = 1000 * 60;
    const hours = minutes * 60;
    const days = hours * 24;
    const weeks = days * 7;
    const years = weeks * 52;
    const unitObj = {
        'seconds': 1000,
        minutes,
        hours,
        days,
        weeks,
        months: years / 12,
        years
    }
    return number * (unitObj[units] || 0)
}

export const sUnits = (number:number, units:Units) => {
    return (msUnits(number, units) || 0) * 1000
}

export const lastDayOfLastMonth = () => {
    // Get the current date
    const currentDate = new Date();

    // Set the date to the first day of the current month
    currentDate.setDate(1);

    // Subtract one day to get the last day of the previous month
    currentDate.setDate(currentDate.getDate() - 1);

    return currentDate;
}
export const firstDayOfThisMonth = () => {
    // Get the current date
    const currentDate = new Date();

    // Set the date to the first day of the current month
    currentDate.setDate(1);

    return currentDate;
}

export const firstDateOfNextMonth = () => {
    const now = new Date();
    const nextMonth = now.getMonth() + 1;
    const year = nextMonth > 11 ? now.getFullYear() + 1 : now.getFullYear();
    return new Date(year, nextMonth % 12, 1);
}

export const buildDate = (options:DateOptions, utc?:boolean) => {
    return date.buildDate(options, utc);
}

export const subtractFromDate = (date1:Dates, units:DateOptions) => {
    return date.subtractFromDate(date1, units);
}

export const extractDate = (date1:string, format?:string) => {
    return date.extractDate(date1, format || 'MM/DD/YYYY');
}

export const isSameDate = (date:Dates, date2:Dates, unit:DateUnitOptions) => {
    return isd(date, date2, unit);
};

export const genDateHour = (h:number, m:number) => {
    return date.adjustDate(new Date(), { hours: h, minutes: m ? m : 0 });
};

export const parseDateHour = (hour:number, format?:string) => {
    const useFormat = format ? format : 'h:mm a';
    const date = formatDate(new Date(), 'YY-MM-DD');
    const dateHour = date + ' ' + hour;
    const extractedDate = extractDate(dateHour, 'YY-MM-DD Hmm');
    return formatDate(extractedDate, useFormat);
};

export const dateDiff = (date:Date|number|string, subtract:Date|number|string, unit?:`${DateUnitOptions}s`):number => {
    return getDateDiff(date, subtract, unit);
}

export const isBetweenDates = (date:Date|number|string, start:Date|number|string, end:Date|number|string) => {
    if(!date || !start) return false;
    const d = new Date(date).getTime();
    const s = new Date(start).getTime();
    const e = end ? new Date(end).getTime() : 10000000000000000000;
    return d >= s && d < e;
}
export default date;
export const ago = (date1: Date | string, format?: string, never?:string) => {
    if(!date1) return never || 'never'
    const { getDateDiff } = date;
    const name = getDateDiff(date1, new Date()) > 0 ? 'from now' : 'ago';
    const minutes = Math.abs(getDateDiff(new Date(), date1, 'minutes'));
    if (minutes && minutes <= 60) {
        return minutes + ` minutes ${name}`;
    } else {
        const hours = Math.abs(getDateDiff(new Date(), date1, 'hours'));
        if (hours && hours <= 24) {
            return hours + ` hours ${name}`;
        } else {
            const days = Math.abs(getDateDiff(new Date(), date1, 'days'));
            if (days && days < 32) {
                return days + ` days ${name}`;
            } else {
                const months = Math.abs(getDateDiff(new Date(), date1, 'months'));
                if(months && months < 19){
                    return months + ` months ${name}`
                } else {
                    const years = Math.abs(getDateDiff(new Date(), date1, 'years'));
                    if(years){
                        return years + ` years ${name}`;
                    }  else return date.formatDate(date1, format ? format : 'ddd MMM-DD, YYYY h:mm a');
                }

            }
        }
    }
}

export const getAgeDate = (aGe:any, format?:string): string => {
    if(!aGe && aGe !== 0) return '2024-01-01';
    const age = Number(aGe);

    // Get today's date
    const today = new Date();

    // Calculate the year of birth
    const birthYear = today.getFullYear() - age;

    // Create a new date object for the start of the year (defaulting to January 1st)
    const dob = new Date(birthYear, today.getMonth(), today.getDate());

    // Format the date as YYYY-MM-DD
    if(format) return formatDate(dob, format);
    return dob;
}

