import {useFeathers} from 'src/stores';
// import {Application} from '@feathersjs/feathers';
import {LocalStorage, SessionStorage} from 'symbol-auth-client';
import {useAuthManagement} from 'src/stores/auth-management';
import {useAuth} from 'src/stores/auth';

const {api} = useFeathers();
import {ucanToken, simpleCapability, genCapability, CapabilityParts, Capability, verifyUcan} from 'src/utils/ucans';
import {VerifyResult} from 'src/utils/ucans';
import {_get, _set} from 'symbol-syntax-utils';
import {ComputedRef, ref, Ref, watch} from 'vue';
import {useCaps} from 'stores/caps';
import {AnyRef} from 'src/utils/types';

export const SUPERUSER = '*';
type AnyObj = {
    [key: string]: any
}

export const getUcanToken = async () => {
    return SessionStorage.getItem('client_ucan') || await (api as {
        [key: string]: any
    }).authentication.getAccessToken();
}

export const getRootIssuer = async () => {
    let root_issuer = SessionStorage.getItem('rootIssuer');
    if (!root_issuer) {
        const {rootIssuer} = await useAuthManagement().get('rootIssuer');
        root_issuer = rootIssuer;
        // console.log('root issuer from get', root_issuer);
        if (rootIssuer) SessionStorage.setItem('rootIssuer', rootIssuer);
    }
    return root_issuer;
}

export declare type LoginPassOption = [Array<string>, Array<string> | '*']
export type CanUSettings = {
    requiredCapabilities: Array<CapabilityParts | Partial<Capability>>,
    name?: string,
    throwNotAuth?: (args?: any) => void,
    or?: boolean,
    loginPass?: Array<LoginPassOption>,
    subject?: AnyObj,
    login?: { _id: string } & AnyObj,
    log?: boolean,
    cap_subjects?: Array<string>,
    capStore?: any,
    audience?: string
}

export type VerifyOptions = {
    audience: string,
    encodeUcan?: boolean,
    requiredCapabilities?: Array<{ capability: Capability, rootIssuer: string }>
}

type MethodOptions = {
    aud: string
}

type LoadCaps = {
    log?: boolean,
    subjects: Array<string>,
    loginId: string,
    capStore: any,
    vMethod: (uc?: string, methodOptions?: MethodOptions) => Promise<any>
}
const loadAndCheckCaps = async ({
                            log,
                            subjects,
                            loginId,
                            capStore,
                            vMethod
                        }: LoadCaps): Promise<VerifyResult<any, any>> => {
    let caps = {data: [], total: 0}
    if (!subjects.length) return {ok: false, error: ['No subjects to load caps for']};
    const foundBySubject:{[key:string]:any} = {}
    if (capStore) caps = capStore.findInStore({query: {$limit: subjects.length, subject: {$in: subjects}}})
    for (const cap of caps.data || []) {
        foundBySubject[cap.subject] = cap;
    }
    const cap_ucans:object = SessionStorage.getItem('cap_ucans') || {}
    for (const id of subjects) {
        if (!foundBySubject[id]) {
            const sesh = cap_ucans[id]
            if (sesh) {
                foundBySubject[id] = sesh;
            }
        }
    }
    const ids = Object.keys(foundBySubject);
    if (ids.length < subjects.length) {
        const fetched = await capStore.find({query: {$limit: subjects.length, subject: {$in: subjects}}})
            .catch(err => {
                console.log(`Error finding caps in ucan auth: ${err.message}`)
                return {data: []}
            })
        for (const cap of fetched.data || []) {
            foundBySubject[cap.subject] = cap;
        }
    }
    let v: VerifyResult<any, any> = { ok: false, error: 'Unable to verify against any caps'};
    for (const cap of Object.values(foundBySubject)) {
        const reconstruct = { _id: cap._id, subject: cap.subject, did: cap.did, caps: {} };
        for (const k in cap.caps || {}) {
            if (log) console.log('check cap', k, cap.caps[k].logins, loginId);
            if ((cap.caps[k].logins || []).map((a: any) => String(a)).includes(loginId)) {
                reconstruct.caps[k] = {...cap.caps[k], logins: [loginId]};
                try {
                    const ucanString = ucanToken(cap.caps[k].ucan)
                    if (log) console.log('got ucan string', ucanString);
                    if (ucanString) {
                        v = await vMethod(ucanString, {aud: cap.did})
                        if (log) console.log('tried v on cap', v);
                    }
                } catch (e: any) {
                    console.log(`Error verifying ucan from cap: ${cap._id}. Err:${e.message}`)
                }
                if (log) console.log('tried v on cap', v);
                if (v.ok) {
                    SessionStorage.setItem('cap_ucans', {...cap_ucans, [cap.subject]: reconstruct});
                    return v;
                }
            }
        }
    }
    return {ok:false, error: 'Unable to verify against any caps'};
}

type VerifyOne = { ucan: string } & VerifyOptions;

const verifyOne = async (ucan: string, options: VerifyOptions, log?: boolean) => {
    let v = await verifyUcan(ucan, options);
    // console.log('verify one here', v);
    if (!v?.ok && options.requiredCapabilities) {
        const newCapabilities = options.requiredCapabilities.map(a => {
            if (a.capability.can !== SUPERUSER) a.capability.can.segments = ['*']
            return a
        })
        v = await verifyUcan(ucan, {
            ...options, requiredCapabilities: newCapabilities
        })
        if (log) console.log('Second verification result:', v);
    }
    return v;
};
export const orVerifyLoop = async (arr: Array<VerifyOne>, log?: boolean): Promise<VerifyResult<any, any>> => {
    let v: any = {ok: false, value: []};

    for (const i of arr) {
        if (!v?.ok) {
            const {ucan, ...options} = i;
            v = await verifyOne(ucan, options)
            if (log) console.log('got in verify loop', v);
        } else break;
    }
    return v;
}

const verifyAgainstReqs = async (ucan: string, audience: string, reqs: Array<any>, settings: any) => {
    try {
        const log = settings?.log;
        if (log) console.log('verify against reqs', reqs)
        let vMethod: (uc?: string, methodOptions?: MethodOptions) => Promise<any>
        const or = settings?.or || []
        if (ucan && or) vMethod = (uc?: string, methodOptions?: MethodOptions) => orVerifyLoop((reqs || []).map(a => {
            return {
                ucan: uc || ucan,
                audience: methodOptions?.aud || audience,
                requiredCapabilities: [a]
            }
        }), log)
        else vMethod = (uc?: string, methodOptions?: MethodOptions) => verifyOne(uc || ucan, {
            audience: methodOptions?.aud || audience,
            requiredCapabilities: reqs
        }, log) as Promise<any>
        const v = await vMethod()
        if (log) console.log('first verify try', v);
        if (v.ok) return v;
        const cs = (settings?.cap_subjects || []).filter(a => !!a)
        if (log) console.log('check cap_subjects', cs);

        /** cap subjects need to already be loaded into the store for this feature to work, a fetch will not be made */
        const capStore = settings?.capStore
        if (cs && capStore) {
            const loginCheckId = settings?.login?._id;
            return await loadAndCheckCaps({
                log,
                subjects: cs,
                loginId: loginCheckId,
                capStore,
                vMethod
            })
        }
    } catch (e) {
        console.error(`Error verifying ucan: ${e.message}`);
    }
    return {ok: false}

}


type CanU = { ok: boolean, error?: any, value?: any };
export const canU = async (settings: CanUSettings): Promise<CanU> => {
    // OBTAIN ALL UCAN PARTS FOR VERIFICATION
    const ucan = await getUcanToken();
    // console.log('token?', ucan);
    const audience = settings.audience ? settings.audience : LocalStorage.getItem('ucan_aud') as string;
    const root_issuer = await getRootIssuer();
    // ENSURE ALL VERIFICATION PARTS ARE PRESENT
    // console.log('got what we need?', ucan, audience, root_issuer);
    if (!ucan || !audience || !root_issuer) {
        if (settings.throwNotAuth) settings.throwNotAuth();
        return {ok: false, value: []}
    } else {
        const caps = (settings.requiredCapabilities || []) as Array<CapabilityParts>
        // console.log('caps?', caps);
        // CONSTRUCT VERIFY UCAN REQUIRED CAPABILITIES
        const reqs = caps.map(a => {
            return {
                capability: Array.isArray(a) ? simpleCapability(a) : genCapability(a),
                rootIssuer: root_issuer as string
            };
        })
        let v: CanU = {ok: false};
        v = await verifyAgainstReqs(ucan, audience, reqs, settings)
        // console.log('inital verification', v);
        if (!v?.ok) {
            let hasSplitNamespace = false;
            const reducedReqs: Array<any> = [];
            // console.log('last leg', reqs);
            reqs.forEach((req, i) => {
                const splt = (_get<any, string>(req, 'capability.can.namespace') || '').split(':')
                if (splt[1]) {
                    req = _set(req, 'capability.can.namespace', splt[0]);
                    hasSplitNamespace = true;
                }
                reducedReqs.push(req)
                // console.log('bottom of loop', reducedReqs);
            })
            if (hasSplitNamespace) v = await verifyAgainstReqs(ucan, audience, reqs, settings) as VerifyResult<any, any>;
            if (!v?.ok && settings.loginPass && settings.subject && settings.login) {

                const checkLoginPass = async (lpass: LoginPassOption) => {
                    //separate out any field specific methods e.g. patch/name,avatar
                    //retrieve existing record to check ids for login id
                    let loginOk = false;
                    //perform the check
                    for (const passPath of lpass[0] || []) {
                        const spl = passPath.split('/');
                        const recordLoginPassId = _get(settings.subject, spl[0]);
                        const loginIdPath = spl[1] || '_id';
                        const loginCheckId = _get(settings.login, loginIdPath) as any;
                        if (loginCheckId && recordLoginPassId) {
                            const checkArr = Array.isArray(loginCheckId) ? loginCheckId.map(a => String(a)) : [String(loginCheckId)];
                            if (Array.isArray(recordLoginPassId)) {
                                for (let i = 0; i < checkArr.length; i++) {
                                    const checkId = String(checkArr[i])
                                    for (let rl = 0; rl < recordLoginPassId.length;) {
                                        const rlId = String(recordLoginPassId[rl]);
                                        if (rlId === checkId) loginOk = true;
                                        else rl++;
                                    }
                                    if (loginOk) break
                                }
                            } else if (checkArr.includes(String(recordLoginPassId))) {
                                loginOk = true;
                                break;
                            }
                        }
                    }

                    if (loginOk) v.ok = true
                }
                // const checkLoginPass = (lpass:LoginPass) => {
                //     const arr = _flatten((lpass[0] || []).map(a => _get(settings.subject, a) as any).filter(a => !!a).map(a => Array.isArray(a) ? a : [a])) as Array<any>;
                //     const id = settings.login?._id as any;
                //     v.ok = arr.map(a => String(a)).includes(String(id))
                // }
                //get an array of arrays of the subject paths where loginPass is allowed to check for our login id

                for (const lpass of settings.loginPass) {
                    if (!v.ok) checkLoginPass(lpass);
                    else break;
                }

            }
        }
        return v;
    }
};

export const canUOrCreated = async (createdByLogin: string, settings?: CanUSettings): Promise<{
    ok: boolean,
    value?: any,
    err?: any
}> => {
    const login = useAuth().user;
    if (createdByLogin && login?._id === createdByLogin) return Promise.resolve({ok: true, value: 'creator'});
    else if (settings) return await canU(settings);
    else return Promise.resolve({ok: false});
}

type Subject = { _id: string } & AnyObj
type ClientCanU = {
    subject: Ref<Subject> | ComputedRef<Subject>,
    caps: Ref<Array<CapabilityParts | Partial<Capability>>> | ComputedRef<Array<CapabilityParts | Partial<Capability>>>,
    or?: boolean,
    login: Ref<any> | ComputedRef<any>,
    loginPass?: Array<LoginPassOption>,
    cap_subjects?: AnyRef<Array<string>>,
    log?: boolean
}
export const clientCanU = ({subject, caps, or, login, loginPass, cap_subjects, log}: ClientCanU) => {

    let capStore;
    if (cap_subjects?.value) capStore = useCaps();
    const canEdit = ref({ok: false});

    const reset = async () => {
        canEdit.value = await canU({
            requiredCapabilities: caps.value,
            or: !!or,
            subject: subject.value,
            login: login.value,
            loginPass,
            cap_subjects: cap_subjects?.value,
            capStore,
            log
        })
    }

    watch(subject, (nv, ov) => {
        if (nv && nv._id !== ov?._id) {
            reset()
        }
    }, {immediate: true})
    return {
        reset,
        canEdit
    }
}
