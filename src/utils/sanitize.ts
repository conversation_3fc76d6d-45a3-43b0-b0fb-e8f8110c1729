import sanitizeHtml from 'sanitize-html';

const allowedTags = ['div', 'span', 'code', 'br', 'p', 'b', 'a', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'h7', 'i'];

const defaultOptions = {
    allowedTags
}

type Options = {
    allowedTags?: string[],
    nonBooleanAttributes?: string[],
    disallowedTagsMode?: string,
    allowedAttributes?: string[],
    selfClosing?: string[],
    allowedSchemes?: string[],
    allowedSchemesByTag?: object,
    allowedSchemesAppliedToAttributes?: string[],
    allowProtocolRelative?: boolean,
    enforceHtmlBoundary?: boolean,
    parseStyleAttributes?: boolean
}
export const sanitize = (text: string, options?: Options) => {
    return sanitizeHtml(text, {...defaultOptions, ...options})
}

export const plainTextSanitize = (text:string) => {
    return text.replace(/<\/?[^>]+(>|$)/g, "");
}
