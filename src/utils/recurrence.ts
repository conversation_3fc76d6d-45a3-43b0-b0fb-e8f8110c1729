// eslint-disable-next-line no-unused-vars
import { RRule } from 'rrule';
import {AnyObj} from 'src/utils/types';
import {_get} from 'symbol-syntax-utils';
import type { Dates } from './date-utils';
export const nextRecurrence = (rule:AnyObj, date:Dates, inc:boolean) => {
  if(typeof date === 'string') date = new Date(date);
  const rrule = getRuleObj(rule);
  return !inc ? rrule.after(date as Date) : rrule.after(date as Date, inc=true);
};
export const getRuleObj = (rule:AnyObj) => {
  let byweekday:any = null;
  if(Array.isArray(_get(rule, 'BYDAY', null))){
    ((_get(rule, 'BYDAY') || []) as Array<string>).forEach((day:string) => {
      Array.isArray(byweekday) && byweekday.length ? byweekday.push(RRule[day as keyof typeof RRule]) : byweekday = [RRule[day as keyof typeof RRule]];
    });
  } else if(rule.BYDAY && typeof rule.BYDAY === 'string') {
    byweekday = RRule[rule.BYDAY as keyof typeof RRule];
  }
  const obj = {
    freq: RRule[rule.FREQ as keyof typeof RRule],
    count: _get(rule, 'COUNT', null),
    interval: _get(rule, 'INTERVAL', 1),
    bymonth: _get(rule, 'BYMONTH', null),
    byweekno: _get(rule, 'BYWEEKNO', null),
    bymonthday: _get(rule, 'BYMONTHDAY', null),
    byweekday: byweekday,
    byyearday: _get(rule, 'BYYEARDAY', null),
    byminute: _get(rule, 'BYMINUTE', null),
    byhour: _get(rule, 'BYHOUR', null),
    until: _get(rule, 'UNTIL', null),
    wkst: _get(rule, 'WKST', null),
    bysecond: null,
  };
  type K = keyof typeof obj;
  const ruleObj:{[key:string]:any} = {};
  for(const k in obj){
    if((obj[k as K] || obj[k as K] === 0) && obj[k as K] !== null && typeof obj[k as K] !== 'undefined') {
      ruleObj[k as K] = obj[k as K];
      // console.log('setting obj property', k, obj[k]);
    }
  }
  return new RRule(ruleObj);
}

export const lastRecurrence = (rule:AnyObj, date:Dates, inc?:boolean) => {
  if(typeof date === 'string') date = new Date(date);
  const rrule = getRuleObj(rule);
  return !inc ? rrule.before(date as Date) : rrule.before(date as Date, inc=true);
}

export const recurrenceString = (rule:AnyObj) => {
  if(rule) {
    // console.log('get str', rule, rule.FREQ);
    const rrule = getRuleObj(rule);
    // console.log('rrule obj', rrule);
    // console.log('rrule str', rrule.toString());
    const r = RRule.fromString(rrule.toString());
    // console.log('rrule r');
    // console.log('rrule', r, rrule.toString(), RRule.YEARLY, RRule.MONTHLY, RRule.WEEKLY, RRule.DAILY, RRule.HOURLY, RRule.MINUTELY);
    return r.toText();
  } else return 'One Time';
}
