import {computed, ComputedRef, Ref, ref, watch} from 'vue';
import {AnyObj} from './types';
import {_get} from 'symbol-syntax-utils';

type SearchObj = { text?: string, keys?: Array<string> };

export declare interface HQueryOpts {
    query?: Ref<any> | ComputedRef<any>,
    keys?: string[],
    search?: Ref<SearchObj> | ComputedRef<SearchObj>,
    regex?: boolean,
    skip?: number,
    delay?: number,
    or?:boolean
}

export const getRegexQuery = (val:string, or?:boolean) => {
    return {
        $regex: `${or ? val.split(' ').join('|') : val}`,
            $options: 'i',
    }
}
export const HQuery = ({query, or, keys = ['name'], search = ref({}), regex = true, skip = 2, delay = 1000}: HQueryOpts) => {
    const searchQ = ref({});
    const internalSearch = ref({ text: '', keys })
    const searchText = computed(() => internalSearch.value?.text || search.value?.text || '');

    const setSearch = () => {
        const kEys = search.value?.keys || keys || [];

        const q: any = {...query?.value || {}};
        if (keys) {
            if (searchText.value && searchText.value.length) {
                if (regex) {
                    const addKey = (key: string) => {
                        const obj: AnyObj = {};

                        const patternExp = getRegexQuery(searchText.value, or)
                        obj[key] = patternExp;
                        if (_get(q, '$or[0]')) {
                            q.$or.push(obj)
                        } else q.$or = [obj];
                    }
                    if (kEys.length > 1) {
                        kEys.forEach((a: string) => addKey(a));
                    } else {
                        q[kEys[0]] = {
                            $regex: `${searchText.value}`,
                            $options: 'i',
                        }

                    }
                } else {
                    q.$text = {
                        $search: searchText.value
                    }
                }
            } else kEys.map((a: string) => delete q[a]);
        }
        searchQ.value = q;
    }

    const time = ref(false);
    watch(searchText, (nv, ov) => {
        if (nv && nv !== ov) {
            if (nv.length % skip === 0) {
                time.value = false;
                setSearch()
            } else {
                time.value = true;
                setTimeout(() => {
                    if (time.value) setSearch();
                }, delay)
            }
        } else if (!nv) {
            searchQ.value = {...(query?.value || {})}
        }
    }, {immediate: true})


    return {
        searchQ,
        search:internalSearch
    }
}
