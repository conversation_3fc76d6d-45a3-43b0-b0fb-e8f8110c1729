import {point, centroid, distance, Units, booleanPointInPolygon} from '@turf/turf';
import {AnyObj} from './types';
import {_get} from 'symbol-syntax-utils';

export type Address = {
    address1: string,
    address2: string,
    formatted: string,
    postal: string,
    city: string,
    region: string,
    country: string,
    latitude: number,
    longitude: number,
    googleAddress: {[key:string]:any},
    name: string,
    tags: {[key:string]:any},
    type: {[key:string]:any},
}

export type Coordinate = [number, number];

export const isGeoJson = (anyArg:any) => {
    return anyArg && typeof anyArg === 'object' && anyArg.geometry;
};

export const isCoordinate = (coord:any) => {
    return Array.isArray(coord) && typeof coord[0] === 'number' && typeof coord[1] === 'number';
};

export const polyCenter = (geo:any):any => {
    const c = centroid(geo);
    return c?.geometry?.coordinates
}

export const circleRadius = (feature:any, units?:Units) => {
    const coords:Array<any> = _get(feature, 'geometry.coordinates[0]') || [];
    const to = coords[Math.floor((coords.length || 0) / 2)];
    const from = coords[0];
    return distance(to, from, {units})/2
}

export const pointToGeo = (pointOrAddress:Address|Coordinate) => {
    if(!isCoordinate(pointOrAddress) && !isGeoJson(pointOrAddress)){
        console.error('Not valid geojson or coordinate', pointOrAddress);
        return undefined;
    }
    if(Array.isArray(pointOrAddress)) return point(pointOrAddress);
    else return point([pointOrAddress.longitude, pointOrAddress.latitude]);
};

export const geoToAddress = (address:AnyObj) => {
    const pos = address.position ? typeof address.position === 'string' ? address.position.split(',') : [_get(address, 'position.lat'), _get(address, 'position.lon')] : [];
    const addressObj = {
        uuid: `address:${new Date().getTime()}`,
        formatted: _get(address, 'address.freeformAddress', null),
        address1: _get(address, 'address.streetNumber', '') + ' ' + _get(address, 'address.streetName', ''),
        region: _get(address, 'address.countrySubdivision', ''),
        city: _get(address, 'address.municipality', ''),
        postal: _get(address, 'address.postalCode', ''),
        country: _get(address, 'address.country', ''),
        latitude: Number(_get(address, 'position.lat', pos[0])),
        longitude: Number(_get(address, 'position.lon', pos[1])),
        // tomtomAddress: address.address
    };
    return addressObj;
};

export const kmToMi = (km:number, digits = 0) => {
    return digits ? (km / 1.60934).toFixed(digits) : Math.round(km / 1.60934);
}

export const miToKm = (mi:number, digits = 2):number => {
    return Number((mi * 1.60934).toFixed(digits));
}

export const createGeoJSONCircle = (center:[number, number], radiusInKm:number, points?:number) => {
    if (!points) points = 32;
    const ret = [];
    if(center) {
        const coords = {
            latitude: center[1],
            longitude: center[0]
        };

        const km = radiusInKm;

        const distanceX = km / (111.320 * Math.cos(coords.latitude * Math.PI / 180));
        const distanceY = km / 110.574;

        let theta, x, y;
        for (let i = 0; i < points; i++) {
            theta = (i / points) * (2 * Math.PI);
            x = distanceX * Math.cos(theta);
            y = distanceY * Math.sin(theta);

            ret.push([coords.longitude + x, coords.latitude + y]);
        }
        ret.push(ret[0]);
    }

    return {
        type: 'Polygon',
        coordinates: center ? [ret] : []
    };
};

export const containsPoint = (point:[number,number], geo:any) => {
    if(point.filter(a => typeof a !== 'number').length) return false;
    const run = (polygon:any) => {
        if (!isGeoJson(polygon)) return false;
        return booleanPointInPolygon(point, polygon);
    }
    if(geo.geometry) return run(geo);
    else if(geo.features) {
        let is = false;
        for(let i = 0; i < geo.features.length; i++){
            if(run(geo.features[i])){
                is = true;
                break;
            }
        }
        return is;
    } else return false;
}
