<template>
  <div class="__dot" :style="{ height: size, width: size}">
    <div class="__lil __1"></div>
    <div class="__lil __2"></div>
    <div class="__lil __3"></div>
  </div>
</template>

<script setup>

  const props = defineProps({
    size: { default: '15px' },
  })

</script>

<style lang="scss" scoped>
  .__dot {
    border-radius: 50%;
    background: transparent;
    animation: spin 2.5s linear infinite;
    position: relative;
  }

  .__lil {
    height: 40%;
    width: 40%;
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    background: var(--q-secondary);
    transform: translate(-50%, -50%);
  }

  .__1 {
    animation: spread_1 2.5s linear infinite, recolor 5s infinite;
  }
  .__2 {
    animation: spread_2 2.5s linear infinite, recolor 5s infinite;
  }
  .__3 {
    animation: spread_3 2.5s linear infinite, recolor 5s infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    40% {
      transform: rotate(0deg);
    }
    60% {
      transform: rotate(360deg);
    }
    80% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(0deg)
    }
  }

  @keyframes spread_1 {
    0% {
      transform: translate(-50%, -50%);
    }
    20% {
      transform: translate(-50%, -50%);
    }
    40% {
      top: 10%;
      left: 0;
      transform: none;
    }
    80% {
      top: 10%;
      left: 0;
      transform: none;
    }
    100% {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  @keyframes spread_2 {
    0% {
      transform: translate(-50%, -50%);
    }
    20% {
      transform: translate(-50%, -50%);
    }
    40% {
      top: 10%;
      left: 100%;
      transform: translate(-100%, 0);
    }
    80% {
      top: 10%;
      left: 100%;
      transform: translate(-100%, 0);
    }
    100% {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  @keyframes spread_3 {
    0% {
      transform: translate(-50%, -50%);
    }
    20% {
      transform: translate(-50%, -50%);
    }
    40% {
      top: 100%;
      left: 50%;
      transform: translate(-50%, -100%);
    }
    80% {
      top: 100%;
      left: 50%;
      transform: translate(-50%, -100%);
    }
    100% {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  @keyframes recolor {
    0% {
      background: var(--q-secondary);
    }
    40% {
      background: var(--q-secondary);
    }
    50% {
      background: var(--q-primary);
    }
    90% {
      background: var(--q-primary);
    }
    100% {
      background: var(--q-secondary);
    }
  }
</style>
