export const parseNumber = (val:string):string => {
    return val ? val.replace(/[$,]/g, '') : '0';
};

export const maybeParseNumber = (value: string): number | string => {
    const trimmed = value.trim();
    const num = Number(trimmed.replace(/,/g, ''));

    return isNaN(num) || trimmed === '' ? value : num;
};

export const multiCellArray = (pasted: string): Array<Array<string | number>> => {
    const lines = pasted.trim().split(/\r?\n/);
    return lines.map(line => {
        const cells = line.split('\t'); // use CSV parser here if it's comma-separated
        return cells.map(cell => maybeParseNumber(cell));
    });
};

// export const multiCellArray = (pasted:string):Array<Array<string>> => {
//     return pasted.trim().split(/\r?\n */).map(r=>r.split(/\t/)).map(a => a.map(b => parseNumber(b)));
// };

export const pasteListener = (evt:ClipboardEvent, cb: (text:string) => string|void) => {
    evt.preventDefault();
    const text = evt.clipboardData?.getData('text').trim();
    return cb(text || '');
};
