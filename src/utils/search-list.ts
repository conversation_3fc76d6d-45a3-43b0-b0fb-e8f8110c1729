import {AnyObj} from './types';
import {isSelected, itemClick} from './select-utils';
import {computed, ref, Ref, ComputedRef, watch} from 'vue';
import {HQuery} from './hQuery';
import {HFind} from './hFind';

type Paginated<M> = {
    data: Array<M>,
    total: number,
    limit: number,
    skip: number
}

type SearchListOptions<T> = {
    store: any,
    modelValue?: ComputedRef<T | Array<T>> | Ref<T | Array<T>>,
    multiple?: boolean,
    emitValue?: boolean,
    optionValue?: string,
    query?: ComputedRef<AnyObj> | Ref<AnyObj>,
    skip?: number,
    delay?: number,
    emit?: (evt: string, payload: any) => void,
    log?: boolean,
    qid?: string,
    name?: string,
    limit?: Ref<number>,
    keys?:Array<string>
};

export const searchList = <T, >({
                                    qid,
                                    store,
                                    log,
                                    modelValue,
                                    multiple,
                                    emitValue,
                                    optionValue = '_id',
                                    query,
                                    skip = 2,
                                    name,
                                    limit,
                                    delay = 2000,
                                    keys = ['name'],
                                    emit = (evt, payload) => console.log(`Cannot emit payload: ${payload}, no event name: ${evt}`)
                                }: SearchListOptions<T>) => {

    const {searchQ, search} = HQuery({ keys })

    const mv = computed(() => {
        return modelValue?.value || [];
    });
    const selected: Ref<T | Array<T> | undefined> = ref(undefined);

    watch(mv, (nv) => {
        selected.value = nv;
    }, {immediate: true});


    const clickItem = (val:any) => {
        emit('update:selected', val);
        const payload = itemClick(val, {modelValue: modelValue?.value, multiple, emitValue, optionValue, log});
        selected.value = payload;
        emit('update:model-value', payload);
    };

    const remove = (i:number) => {
        if (multiple) {
            const v = Array.isArray(selected.value) ? (selected.value || []).map(a => a).splice(i, 1) : [];
            selected.value = v;
            emit('update:model-value', v);
        } else {
            selected.value = undefined;
            emit('update:model-value', undefined);
        }
    };

    const focusOn = ref(false);


    let timeout:any;
    const input = (val:string) => {
        if (val?.length % skip === 0) search.value.text = val;
        else {
            if (timeout) clearTimeout(timeout);
            timeout = setTimeout(() => {
                search.value.text = val;
            }, delay);
        }
    };

    const params = computed(() => {
        return {
            query: {
                ...searchQ.value,
                ...query?.value
            },
            qid: qid
        };
    });

    const {h$, params: hParams, init} = HFind<T>({
        store,
        immediate: false,
        params,
        limit,
        name: `search-list: ${name}`
    });
    const checkFocus = () => {
        focusOn.value = true;
        if (!h$.haveBeenRequested) init();
        emit('focus', undefined);
    };


    return {
        h$,
        init,
        hParams,
        input,
        focusOn,
        checkFocus,
        remove,
        clickItem,
        query,
        search,
        selected,
        isSelected
    }
};
