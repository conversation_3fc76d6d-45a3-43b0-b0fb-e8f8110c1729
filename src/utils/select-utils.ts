import {_get} from 'symbol-syntax-utils';

type AnyObj = { [key:string]: any }

type ClickOptions = {
    optionValue?:string
    multiple?:boolean,
    emitValue?:boolean,
    beforeEmit?:string,
    afterEmit?:string,
    modelValue:any,
    log?:boolean
};
function itemClick<T>(item:T, options:ClickOptions){
    const path = _get(options, 'optionValue', '_id') as string;
    let val:any = options.emitValue ? _get(item, path) : item;
    if(options.multiple){
        if(!Array.isArray(options.modelValue)) options.modelValue = options.modelValue ? [options.modelValue] : [];
        const idx = options.modelValue.map((a:any) => _get(a, path, a)).indexOf(_get(val, path, val));
        if(idx > -1) {
            val = options.modelValue.map((a:any) => a) as Array<T>;
            val.splice(idx, 1);
        }
        else if(val) val = [...options.modelValue, val];
    }
    if(options.log) console.log('item click', val, options.multiple, options.emitValue, item);
   return val;
};

function isSelected<T>(val:T, modelValue:Array<T>|T, options?:{ optionValue:string }){
    const list = modelValue ? Array.isArray(modelValue) ? modelValue : [modelValue] : [];
    const path = _get(options, 'optionValue', '_id') as string;
    return list && list.map(a => _get(a, path, a)).includes(_get(val, path, val));
};


export {
    itemClick,
    isSelected
};
