type Options = {
    version?: number,
    keyPath?: string,
    log?: boolean
}

type TransOptions = {
    type?: 'readonly' | 'readwrite'
}

export class LocalDb {
    db?: IDBDatabase | null = null;
    success?: boolean = false;
    collectionName?: string = '';
    log = false;
    errors: any[] = [];
    keyPath = '_id';

    constructor(dbName: string, collection: string, { version, keyPath, log }: Options) {
        this.log = log ?? false;
        this.keyPath = keyPath || '_id';
        const request = indexedDB.open(dbName, version || 1);

        request.onupgradeneeded = () => {
            const db = request.result;
            if (!db.objectStoreNames.contains(collection)) {
                db.createObjectStore(collection, { keyPath: this.keyPath });
            }
        };

        request.onsuccess = () => {
            this.db = request.result;
            this.success = true;
            if (this.log) console.log(dbName, ' - ', version, ' open');
        };

        request.onerror = (event) => {
            this.errors.push(['open', event]);
            console.error('Database open error:', event);
        };

        this.collectionName = collection;
    }

    putItem<T = any>(val: T, options?: TransOptions) {
        if (!this.db) {
            this.errors.push(['put', 'Database is not initialized']);
            return;
        }

        const { type } = options || {};
        const transaction = this.db.transaction(this.collectionName, type || 'readwrite');
        const store = transaction.objectStore(this.collectionName);
        const request = store.put(val);

        request.onsuccess = () => {
            if (this.log) console.log('Successful put');
        };
        request.onerror = () => {
            this.errors.push(['put', request.error]);
        };
    }

    addItem<T = any>(val: T, options?: TransOptions) {
        if (!this.db) {
            this.errors.push(['add', 'Database is not initialized']);
            return;
        }

        const { type } = options || {};
        const transaction = this.db.transaction(this.collectionName, type || 'readwrite');
        const store = transaction.objectStore(this.collectionName);
        const request = store.add(val);

        request.onsuccess = () => {
            if (this.log) console.log('Successful add');
        };
        request.onerror = () => {
            this.errors.push(['add', request.error]);
        };
    }

    async getItem(id: string) {
        if (!this.db) {
            this.errors.push(['get', 'Database is not initialized']);
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction(this.collectionName, 'readonly');
            const store = transaction.objectStore(this.collectionName);
            const request = store.get(id);

            request.onsuccess = () => {
                resolve(request.result);
            };
            request.onerror = () => {
                this.errors.push(['get', request.error]);
                reject(request.error);
            };
        });
    }

    removeStore(name: string) {
        if (!this.db) {
            this.errors.push(['removeStore', 'Database is not initialized']);
            return;
        }

        this.db.close();
        const request = indexedDB.open(name, this.db.version + 1);

        request.onupgradeneeded = () => {
            const db = request.result;
            if (db.objectStoreNames.contains(name)) {
                db.deleteObjectStore(name);
            }
        };

        request.onsuccess = () => {
            this.db = request.result;
        };
    }
}
