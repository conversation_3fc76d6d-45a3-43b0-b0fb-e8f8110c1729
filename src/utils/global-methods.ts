import {colors, Notify, Loading, date} from 'quasar';
import {_get} from 'symbol-syntax-utils';
import {RouterType, IIndexable, AnyObj} from './types';
import {useRouter, useRoute, RouteLocationNormalizedLoaded, Router} from 'vue-router';
import {Address} from 'src/utils/geo-utils';

export const genPhrase = () => {
    const first = ['golden', 'amazing', 'complex', 'fast', 'huge', 'tired', 'attractive', 'hard', 'rough', 'enticing', 'shiny', 'rough', 'lonely', 'anxious', 'happy'];
    const second = ['leaf', 'grass', 'road', 'hands', 'day', 'surface', 'material', 'environment', 'chair', 'shoe', 'stick', 'tire', 'ape', 'rock', 'lion', 'fork']
    const f = Math.floor(Math.random() * first.length);
    const s = Math.floor(Math.random() * second.length);
    return first[f] + '-' + second[s];
}
export const $openInNew = (route: any, router: Router) => {
    const {href} = router.resolve(route);
    window.open(href, '_blank')
}
export const $max = (...args) => Math.max(...args)
export const $round = (num: number) => Math.round(num)

export const $isN = (str: string): string => ['a', 'e', 'i', 'o', 'u'].includes((str?.charAt(0) || 'z').toLowerCase()) ? 'n' : '';

export const $pluralExtension = (str = '', arr: Array<any> | number, singular = '', plural = 's') => {
    const num = Array.isArray(arr) ? arr.length : Number(arr || 0);
    if (num === 1) return `${str}${singular}`;
    else return `${str}${plural}`;
}
export const $possiblyPlural = (str = '', arr: Array<any> | number, singular = '', plural = 's') => {
    const num = Array.isArray(arr) ? arr.length : Number(arr || 0);
    return `${num} ${$pluralExtension(str, num, singular, plural)}`
};

export const fakeId = '123456781234567812345678'

const $limitStr = (string: string, limit: number, append?: string, mid?: boolean) => {
    const apnd = append ? append : '...';
    const appendLength = apnd ? JSON.stringify(apnd).length : 0;
    const stringLength = string ? string.length : 0;
    if (limit && stringLength && stringLength > limit) {
        return string.substring(0, limit - appendLength - (mid ? 3 : 0)) + apnd + (mid ? string.substring(string.length - 4, string.length) : '')
    } else return string;
}

export const $addressDisplay = (address: Address) => {
    if (!address) return ''
    return `${address.address1} ${address.address2 || ''} ${address.city}, ${address.region || ''} ${address.postal || ''}`.split('  ').join(' ')
}

export const $dateDisplay = (date1:string|Date, format?:string, def?:string) => {
    if(!date1) return def || 'n/a';
    const today = date.isSameDate(date1, new Date(), 'date');
    if(today) return 'Today' + date.formatDate(date1, ' h:mm a');
    const diff = date.getDateDiff(date1, new Date(), 'days');
    if(diff === 1) return 'Yesterday' + date.formatDate(date1, ' h:mm a');
    else if(diff < 7) return date.formatDate(date1, 'ddd h:mm a')
    else return date.formatDate(date1, format || 'ddd MMM-DD, YYYY');
}
export const $ago = (date1: string | Date, format?: string, def?:string) => {
    if(!date1) return def || 'n/a';
    const name = date.getDateDiff(date1, new Date()) > 0 ? 'from now' : 'ago';
    const dt = new Date();
    const minutes = Math.abs(date.getDateDiff(dt, date1, 'minutes'));
    const plural = (num: number, str: string) => {
        if (num > 0 && num < 2) return str;
        else return str + 's';
    }
    if (minutes) {
        if (minutes < 1) {
            return Math.abs(date.getDateDiff(dt, date1, 'seconds')) + ` seconds ${name}`
        }
        if (minutes <= 60) {
            return minutes + ` ${plural(minutes, 'minute')} ${name}`;
        }
    } else {
        const hours = Math.abs(date.getDateDiff(dt, date1, 'hours'));
        if (hours && hours <= 24) {
            return hours + ` ${plural(hours, 'hour')} ${name}`;
        } else {
            const days = Math.abs(date.getDateDiff(dt, date1, 'days'));
            if (days && days < 200) {
                return days + ` ${plural(days, 'day')} ${name}`;
            } else return date.formatDate(date1, format ? format : 'MM/DD/YYYY');
        }
    }
}
const $successNotify = (message?: string): void => {
    Notify.create({message: message, color: 'positive', timeout: 4000})
}

const $capitalizeFirstLetter = (string: string): string => {
    return string ? string.charAt(0).toUpperCase() + string.slice(1) : '';
};

const localUuid = () => {
    const rand = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, c => {
        const r = Math.random() * 16 | 0;
        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });

    const timestamp = String(new Date().getTime());
    const combined = rand.slice(0, rand.length - timestamp.length) + timestamp;

    const formatted = [8, 12, 16, 20].reduce((str, idx, i) =>
        str.slice(0, idx + i) + '-' + str.slice(idx + i), combined);

    return formatted.slice(0, 10) + '4' + formatted.slice(11, 15) + '4' + formatted.slice(16);
};

const $infoNotify = (message?: string): void => {
    Notify.create({
        message: message,
        color: 'info',
        position: 'top',
        icon: 'mdi-information',
        timeout: 3000,
        actions: [{icon: 'mdi-close', color: 'white'}],
    })
}
const $errNotify = (message?: string, opts?: AnyObj): void => {
    Notify.create({
        message: message,
        color: 'negative',
        position: 'bottom',
        icon: 'mdi-alert-circle',
        timeout: 10000,
        actions: [{icon: 'mdi-close', color: 'white'}],
        ...opts
    });
}

const $arrayFilterZero = (filteredArray: [], def?: string | object | [] | number, prop?: string, i?: number) => {
    const idx = i ? i : 0;
    if (filteredArray?.length) {
        return !prop ? filteredArray[idx] : filteredArray[idx][prop];
    } else return def || def === 0 ? def : null;
}

const $rHistory = ({route, $route, $router}: {
    $route: RouteLocationNormalizedLoaded,
    $router: Router,
    route: RouterType
}): void => {
    const r = {...$route, ...route}
    // $route.params = r.params;
    // $route.query = r.query;
    // $route.meta = r.meta;
    // $route.name = r.name;
    const {href} = $router.resolve(r);
    window.history.pushState({}, '', href);
}

const dollarString = (val: string | number, symbol = '$', dec?: number, def = 'N/A'): string => {
    const v = typeof val !== 'number' ? parseFloat(val) : val;
    if (isNaN(v)) return def;
    // console.log('dollar string', val, v)
    const decimal = dec || dec === 0 ? dec : 2;
    const valDec = v.toFixed(decimal);
    return (symbol) + valDec.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

const $go = ({
                 name,
                 params,
                 query,
                 path
             }: RouterType, action = 'push', filterOut: RouterType = {query: {access_token: ''}}): void => {
    const $route = useRoute();
    const routerObj: RouterType = {};
    if (name) routerObj.name = name;
    if (params) routerObj.params = params;
    if (query) routerObj.query = query;
    if (path) routerObj.path = path;
    const obj = {...$route, ...filterOut, ...routerObj};
    return (useRouter() as IIndexable)[action](obj).catch((err: AnyObj) => {
        throw new Error(`boot/global.js -> $go -> $router.push: ${err}.`);
    });
}

const $getCssVar = (css_var: string, element: HTMLElement = document.body): string => {

    if (!(element instanceof Element)) {
        throw new TypeError('Expected a DOM element');
    }
    let styles = getComputedStyle(element);
    let value = String(styles.getPropertyValue(css_var)).trim();
    if (!value) {
        styles = getComputedStyle(document.documentElement);
        value = String(styles.getPropertyValue(css_var)).trim();
    }
    return value || '';
}

const $setCssVar = (css_var: string, value: string, element: HTMLElement = document.body) => {
    if (!(element instanceof Element)) {
        throw new TypeError('Expected a DOM element');
    }
    const styles = element.style;
    styles.setProperty(css_var, value);
    // console.log('css var set', styles, element);
}

const $removeCssVar = (css_var: string, element: HTMLElement = document.body) => {
    if (!(element instanceof Element)) {
        throw new TypeError('Expected a DOM element');
    }
    const styles = element.style;
    styles.removeProperty(css_var);
}

const $getAllCssVars = (element: HTMLElement = document.body) => {
    if (!(element instanceof Element)) {
        throw new TypeError('Expected a DOM element');
    }
    const styles = element.style;
    let cssVars;
    for (let i = styles.length; i--;) {
        if (styles[i].startsWith('--')) {
            const s = styles[i];
            !cssVars ? cssVars = [s] : cssVars.push(s);
        }
    }
    return cssVars;
}

const $removeAllCssVars = (element: HTMLElement = document.body) => {
    if (!(element instanceof Element)) {
        throw new TypeError('Expected a DOM element');
    }
    const list = $getAllCssVars(element)
    if (list) list.forEach(k => {
        $removeCssVar(k, element);
    });
}


const $getTextColor = (css_var: string): string => {
    const rgba = colors.hexToRgb($getCssVar(css_var));

    if ((rgba['r'] * 0.299) + (rgba['g'] * 0.587) + (rgba['b'] * 0.114) > 186) {
        return 'black';
    } else {
        return 'white';
    }
}

const $isCssVarDark = (css_var: string): boolean => {
    const rgba = colors.hexToRgb($getCssVar(css_var));
    return (rgba['r'] * 0.299) + (rgba['g'] * 0.587) + (rgba['b'] * 0.114) <= 186;
}

const $isHexDark = (hex: string): boolean => {
    const rgba = colors.hexToRgb(hex);

    return (rgba['r'] * 0.299) + (rgba['g'] * 0.587) + (rgba['b'] * 0.114) <= 186;
}

const $isLoading = (val?: boolean | string): void => {
    if (val) {
        if (!Loading.isActive) Loading.show();
    } else {
        Loading.hide();
    }
}

const $searchExactMatch = (item_list: (string | object | [] | number)[], search_query: string, keys_list?: string[]): (string | object | [] | number)[] => {
    const lowSearch = search_query.toLowerCase().trim();
    const keys = keys_list || [];
    if (!lowSearch) return item_list;

    return item_list.filter(function (item) {
        return keys.some(key =>
            String(_get(item, key, '')).toLowerCase().includes(lowSearch)
        );
    });
}

const $searchOrMatch = (item_list: (string | object | [] | number)[], search_query: string, keys_list: string[]): (string | object | [] | number)[] => {
    if (!search_query) return item_list;

    const low_search_list = search_query.toLowerCase().trim().split(/[ ,]+/);
    const searchRegex = new RegExp(low_search_list.join('|'), 'g');
    const keys = keys_list;

    return item_list.filter(function (item) {
        return keys.some(key =>
            String(_get(item, key, '')).toLowerCase().match(searchRegex)
        );
    });
}

const $searchAndMatch = (item_list: (string | object | [] | number)[], search_query: string, keys_list: string[]): (string | object | [] | number)[] => {
    if (!search_query) return item_list;

    const low_search_list = search_query.toLowerCase().trim().split(/[ ,]+/);
    const searchRegex = '(?=.*' + low_search_list.join(')(?=.*') + ')';
    const keys = keys_list;

    return item_list.filter(function (item) {
        return keys.some(key =>
            String(_get(item, key, '')).toLowerCase().match(searchRegex)
        );
    });
}

const $highlight = (words: string, query?: string): string => {
    if (!query) return words;
    const low_search_list = query.toLowerCase().trim().split(/[ ,]+/);
    const iQuery = new RegExp(low_search_list.join('|'), 'ig');

    return words.toString().replace(iQuery, function (matchedTxt) {
        return ('<span style=\'background-color: orange;\'>' + matchedTxt + '</span>');
    });
}

const fallbackCopyTextToClipboard = (text: string) => {
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    let success = 0;
    try {
        const successful = document.execCommand('copy');
        let msg = 'unsuccessful';
        if (successful) {
            msg = 'successful';
            success = 1;
        }
        Notify.create({
            message: 'Copied text to clipboard',
            color: 'black',
            icon: 'mdi-content-copy'
        })
        console.log('Fallback: Copying text command was ' + msg);
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
    }

    document.body.removeChild(textArea);
    return success;
}

const $copyTextToClipboard = async (text: string, message = 'Copied text to clipboard') => {
    let success = 0;
    if (!navigator.clipboard) {
        fallbackCopyTextToClipboard(text);
        return;
    }
    navigator.clipboard.writeText(text).then(() => {
        success = 1;
        console.log('Async: Copying to clipboard was successful!');
        Notify.create({
            message: message,
            color: 'black',
            icon: 'mdi-content-copy'
        })
        return success;
    }, (err) => {
        console.error('Async: Could not copy text: ', err);
        return success;
    });
}

export const abbrNumber = (num: number, pre = '$', dec = 0) => {
    let append = '';
    if (!num || typeof num !== 'number') return dollarString(0, pre, dec);
    let v = num;
    if (num < 1000) {
        return dollarString(num, pre, num < 1 ? Math.max(2, dec) : dec);
    } else if (num >= 1000 && num < 1000000) {
        v = num / 1000;
        append = 'K';
    } else if (num >= 1000000 && num < 1000000000) {
        v = num / 1000000;
        dec = num % 10 === 0 ? dec : 1;
        append = 'MM';
    } else if (num >= 1000000000 && num < 1000000000000) {
        v = num / 1000000000;
        dec = num % 10 === 0 ? dec : 1;
        append = 'B'
    } else return dollarString(num, pre, dec);
    return `${dollarString(v, pre, dec)}${append}`
};

export const abbrMoney = (num: number, pre = '$', dec = 0) => {
    if (!num || typeof num !== 'number') return dollarString(0, pre, dec);
    if (num < 1) {
        return `${dollarString(num * 100, '', 0)}¢`;
    } else return dollarString(num, pre, dec);
};

export const getRootDomain = () => {
    const origin = window.origin;
    const spl = origin.split('.');
    if (spl.length > 2) return spl.slice(-2).join('.');
    return spl.join('.')
}


export {
    $copyTextToClipboard,
    $limitStr,
    $successNotify,
    $infoNotify,
    $errNotify,
    $arrayFilterZero,
    $rHistory,
    $go,
    dollarString,
    $getCssVar,
    $setCssVar,
    $getAllCssVars,
    $removeAllCssVars,
    $removeCssVar,
    $getTextColor,
    $isCssVarDark,
    $isHexDark,
    $isLoading,
    $searchExactMatch,
    $searchOrMatch,
    $searchAndMatch,
    $highlight,
    localUuid,
    $capitalizeFirstLetter
}
