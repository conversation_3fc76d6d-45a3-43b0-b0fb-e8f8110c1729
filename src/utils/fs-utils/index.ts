import {AnyObj} from 'src/utils/types';
import { _get } from 'symbol-syntax-utils';

interface FileOpts {
    obj: AnyObj | AnyObj[],
    path?: string | string[],
    type?: string,
    index?: number,
    def?: string,
    subPath?: string[],
    urlPath?: string[],
    prePath?: string[]
}

export const getFile = (
    {
        obj,
        path = [],
        subPath = [],
        index,
        def,
        urlPath = ['url'],
        prePath = ['_fastjoin', 'files']
    }: FileOpts) => {
    let paths = [...path, ...subPath, ...urlPath];
    if(index || index === 0) paths = [...path, ...subPath, String(index), ...urlPath];
    return _get(obj, [...prePath, ...paths]) || _get(obj, paths, _get(obj, 'url', def));
}

