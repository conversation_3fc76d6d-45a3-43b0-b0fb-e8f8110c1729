// import { fileTypeFromBuffer, fileTypeFromBlob } from 'file-type';
import {MimeList, mimeList} from './mime-types';

type FileGroups = { [key:string]: string[] }

export const fileGroups:FileGroups = {
    image: ['jpg', 'jpeg', 'png', 'tiff', 'gif', 'raw'],
    video: ['mp4', 'mov', 'wmv', 'avi', 'avchd', 'flv', 'fv4', 'swf', 'mkv', 'webm'],
    vector:  ['pdf'],
    sheet: ['xlxs', 'xlx', 'xls'],
    delimited: ['numbers', 'csv'],
    json: ['json'],
    code: ['js', 'ts', 'jsx', 'jsm', 'py', 'c', 'cpp', 'java', 'css', 'scss', 'sass', 'vue', 'svlte', 'go', 'ruby', 'php', 'html']
};

type ColorMap = { [key:string]: string };

export const iconColors = (ext:string):string => {
    const colorMap:ColorMap = {
        image: 'ir-light-blue',
        video: 'ir-purple',
        vector: 'ir-red',
        sheet: 'ir-green',
        delimited: 'ir-green',
        json: 'ir-blue-8',
        code: 'ir-orange'
    };
    const key = Object.keys(fileGroups).filter((key) => fileGroups[key as keyof FileGroups].includes(ext))[0];
    return colorMap[key as keyof ColorMap] || 'ir-grey-7';
};

export type DetectableFileTypes = string|ArrayBuffer|Uint8Array|Blob;


// export const getFileType = async (file:DetectableFileTypes, type:string) => {
//     const obj = {
//         'buffer': fileTypeFromBuffer(file as ArrayBuffer | Uint8Array),
//         'blob': fileTypeFromBlob(file as Blob)
//     }
//     return await obj[type](file);
// }

//TODO: try this https://stackoverflow.com/questions/18299806/how-to-check-file-mime-type-with-javascript-before-upload

type FileIconsOptions = {returnObj?:boolean, type?:string};

export const parseFileExtension = (name:string) => {
    const nameArr = name ? name.split('.') : [];
    return nameArr[nameArr.length - 1];
};

export const getMimeType = (file:DetectableFileTypes):string|null => {
    const type = (mimeList as MimeList)[parseFileExtension(file as string) as keyof MimeList];
    return type || null;
}

type FileShort = { [key: string]:string };
export const extTypes:FileShort = {
    'jpg': 'mdi-image',
    'jpeg': 'mdi-image',
    'png': 'mdi-image',
    'tiff': 'mdi-image',
    'gif': 'mdi-image',
    'raw': 'mdi-image',
    'mp4': 'mdi-video',
    'mov': 'mdi-video',
    'wmv': 'mdi-video',
    'avi': 'mdi-video',
    'avchd': 'mdi-video',
    'flv': 'mdi-video',
    'fv4': 'mdi-video',
    'swf': 'mdi-video',
    'mkv': 'mdi-video',
    'webm': 'mdi-video',
    'pdf': 'mdi-file-pdf-box',
    'doc': 'mdi-file-document',
    'xlxs': 'mdi-file-excel-box',
    'xlx': 'mdi-file-excel-box',
    'xlxm': 'mdi-file-excel-box',
    'xls': 'mdi-file-excel-box',
    'numbers': 'mdi-file-delimited',
    'csv': 'mdi-file-delimited',
    'json': 'mdi-code-json',
    'js': 'mdi-language-javascript',
    'ts': 'mdi-language-typescript',
    'c': 'mdi-language-c',
    'cpp': 'mdi-language-c++',
    'py': 'mdi-language-python',
    'vue': 'mdi-vuejs',
    'css': 'mdi-language-css3',
    'scss': 'mdi-language-css3',
    'sass': 'mdi-language-css3',
    'go': 'mdi-language-go',
    'ruby': 'mdi-language-ruby',
    'php': 'mdi-language-php',
    'java': 'mdi-language-java',
    'html': 'mdi-language-html5'
};

export const fileIcons = (file:DetectableFileTypes, options:FileIconsOptions = { returnObj: false, type: 'string'}) => {
    const ext = parseFileExtension(file as string);
    // if((options.type || 'string') === 'string'){
    // } else {
    //     ext = await getFileType(file, options.type || 'string')
    // }
    const name = extTypes[ext] || 'mdi-file';
    if(!options.returnObj) return name;
    else return { name, color: iconColors(ext) }
};


