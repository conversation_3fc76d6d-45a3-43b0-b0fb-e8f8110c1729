import {LocationQuery, RouteLocation, RouteMeta, RouteParams, RouteRecordName} from 'vue-router';
import { Params } from 'feathers-pinia'
import {ComputedRef, Ref} from 'vue';

export interface AnyObj {
    [key: string]: any
}


export interface ParamsOpts extends Params<any>{
 [key:string]: any
}


export declare interface RouterType {
    /**
     * Percentage encoded pathname section of the URL.
     */
    path?: string;
    /**
     * The whole location including the `search` and `hash`. This string is
     * percentage encoded.
     */
    fullPath?: string;
    /**
     * Object representation of the `search` property of the current location.
     */
    query?: LocationQuery;
    /**
     * Hash of the current location. If present, starts with a `#`.
     */
    hash?: string;
    /**
     * Name of the matched record
     */
    name?: RouteRecordName | null | undefined;
    /**
     * Object of decoded params extracted from the `path`.
     */
    params?: RouteParams;
    /**
     * Contains the location we were initially trying to access before ending up
     * on the current location.
     */
    redirectedFrom?: RouteLocation | undefined;
    /**
     * Merged `meta` properties from all of the matched route records.
     */
    meta?: RouteMeta;
}

export interface IIndexable {
    [key: string]: any;
}

export declare type AnyRef<T=any> = Ref<T>|ComputedRef<T>


