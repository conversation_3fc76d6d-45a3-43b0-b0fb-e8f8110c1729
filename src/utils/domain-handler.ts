import { _get } from 'symbol-syntax-utils';
import { parseDomain, ParseResultType, ParseResult } from 'parse-domain';

// const extractEnvironments = ({ domain }) => {
//   let environments = _get(domain, '_fastjoin.host._fastjoin.environments', []);
//   return environments;
// };

export const splitUrl = (inputUrl: string) => {
  // Ensure the URL has a protocol; default to 'http' if missing.
  let urlObj: URL;
  try {
    urlObj = new URL(inputUrl);
  } catch (e:any) {
    urlObj = new URL(`http://${inputUrl}`);
  }

  // Extract basic URL parts.
  const protocol = urlObj.protocol.slice(0, -1); // e.g. "http" from "http:"
  const fqdn = urlObj.host; // includes port if present
  const hostname = urlObj.hostname; // just the hostname without port
  const path = urlObj.pathname;

  // Determine the port, defaulting to 80 for HTTP and 443 for HTTPS.
  let port = urlObj.port;
  if (!port) {
    port = protocol === 'https' ? '443' : '80';
  }

  // Parse the hostname for domain details.
  const parseResult = parseDomain(hostname) as ParseResult & { icann: () => undefined };

  let subdomain: string = '';
  let domain: string = '';
  let host: string = '';

  if (parseResult) {
    switch (parseResult.type) {
      case ParseResultType.Listed: {
        // For a domain like "admin.somesite.com", subDomains might be ["admin"]
        const subDomains: string[] = _get(parseResult, 'subDomains', []) || [];
        // Instead of shifting the first element, we join all subdomains:
        subdomain = subDomains.join('.');
        // Optionally, you can still capture the first label as 'host'
        host = subDomains[0] || '';
        const topLevelDomains: string[] = _get(parseResult, 'topLevelDomains', []) || [];
        const ext = topLevelDomains.length ? topLevelDomains.join('.') : '';
        domain = `${_get(parseResult, 'domain')}.${ext}`;
        break;
      }
      case ParseResultType.Reserved:
      case ParseResultType.NotListed: {
        domain = parseResult.hostname;
        subdomain = (_get(parseResult, 'labels', [])[0]) || '';
        break;
      }
      case ParseResultType.Ip: {
        domain = parseResult.hostname;
        break;
      }
      default:
        throw new Error(`${hostname} is an invalid domain`);
    }
  } else {
    throw new Error("Failed to parse the domain");
  }

  return {
    fqdn,
    protocol,
    host,
    subdomain,
    domain,
    port: parseInt(port, 10),
    path,
  };
};

const splitDomain = (url:string) => {
  return splitUrl(url);
};

export class DomainHandler {
  constructor(log?:any) {
    if(log) console.log('domain handler constructed');
  }

  splitDomain(domain:string) {
    return splitDomain(domain);
  }

  // extractEnvironments({ domain, application }) {
  //   return extractEnvironments({ domain, application });
  // }

  // extractHost(
  //   {
  //     domain,
  //     path,
  //   }) {
  //   let host = _get(domain, '_fastjoin.host');
  //   let obj = { host, environment:  _get(host, '_fastjoin.environment')};
  //   return path ? obj[path] : obj;
  // };
};

export default DomainHandler

