// import flattenArray from 'lodash.flattendeep';
import {onMounted, watch as vWatch, ref, Ref, computed, ComputedRef, nextTick} from 'vue';
// import {useFind} from "feathers-pinia";
import {HQuery} from './hQuery';
import {Notify} from 'quasar';
import {HFind} from './hFind';
import {_get, _flatten} from 'symbol-syntax-utils';

export const TagLoader = (
    {
        name = 'tags',
        loadOnMount,
        tagPath = 'tags',
        params = ref({query: {}}),
        staticOptions = ref([]),
        store,
        adding = true,
        minLength = 2,
        multiple,
        watch = true,
        skipText = 2,
        emit,
        qId = 'tags'
    }: {
        staticOptions: Ref<string[]>,
        name?: string,
        loadOnMount?: boolean,
        tagPath?: string,
        params: any | ComputedRef<any>,
        store: any,
        adding?: boolean,
        minLength?: number,
        multiple?: boolean,
        watch?: boolean,
        skipText?: number,
        emit?: (args: any) => void,
        qId?: string,
        paginateApi?: 'hybrid' | 'client' | 'server'
    }) => {

    const searchInput = ref('');
    const loading = ref(false);
    const selected = ref<string | string[]>(multiple ? [] : '');
    const addInput = ref('');
    const options = ref<string[]>([]);

    const qw = computed(() => false);

    const search = computed(() => {
        return {text: searchInput.value, keys: [tagPath]};
    });

    const {searchQ} = HQuery({query: params.value.query, search})

    const {
        h$,
        params: hParams = {},
        // canNext = false,
        // canPrev = false,
        // currentPage = 1,
        // pageCount = 1,
        // data = [],
        // paginationData = {},
        // isPending = false,
        // haveBeenRequested = false,
        // haveLoaded = false,
        // error = '',
        // find = () => console.log('no store provided to tag loader')
    } = store ? HFind({
        store,
        params: {...params, query: searchQ, qid: qId},
        watch: qw.value,
        immediate: true
    }) : {
        h$: {}
    };


    const useOptions = computed(() => {
        if (staticOptions.value?.length) return staticOptions.value;
        if (options.value) {
            if (searchInput.value && searchInput.value.length) {
                return options.value.filter(a => {
                    return a?.toLowerCase().indexOf(searchInput.value?.toLowerCase() || '') > -1;
                });
            } else return options.value;
        } else return []
    });

    const isSelected = (tag: string) => {
        if (Array.isArray(selected.value)) {
            if (multiple) return selected.value.indexOf(tag) > 1;
            else return typeof selected.value === 'string' && selected.value === tag;
        } else return false;
    }

    const add = (tag?: string) => {
        // console.log('adding', tag, selected.value);
        if (adding) {
            setTimeout(() => {
                const val = tag || addInput.value;

                if (val && val.length >= minLength) {
                    if (multiple) {
                        Array.isArray(selected.value) ? selected.value.push(val) : selected.value = [val];
                    } else {
                        selected.value = val;
                    }
                    if (emit) {
                        emit(Array.from(new Set(selected.value)))
                    }
                } else if (val) Notify.create({
                    message: `Tags must be ${minLength} characters`,
                    color: 'info',
                    timeout: 4000
                });
            }, 100)
            // this[`${name}AddInput`] = undefined;
        }
    }

    const load = (): any => {
        // console.log('load call', staticOptions.value);
        if (!staticOptions.value?.length) {
            loading.value = true;
            const findResult = h$.data;
            // console.log('find result?', h$.data, findResult);
            loading.value = false;
            if (findResult) {
                options.value = Array.from(new Set(_flatten((findResult || []).map((a: any) => _get(a, tagPath))))) as string[]
            }
            return findResult;
        } else return undefined
    }

    const reload = async (i: number) => {
        if (typeof i === 'number') {
            h$.toPage(i);
            return load();
        }
    }

    onMounted(() => {
        if (loadOnMount) load()
    })

    vWatch(searchInput, (newVal, oldVal) => {
        const newLength = newVal && newVal.length ? newVal.length : 0;
        const oldLength = oldVal && oldVal.length ? oldVal.length : 0;
        if (watch && newLength && newLength > oldLength && newLength % skipText === 0) {
            load();
        }
    }, {immediate: true})

    return {
        h$,
        //total
        useOptions,
        load,
        isSelected,
        selected,
        addInput,
        add,
        emit,
        reload,
        // data,
        // toEnd,
        // toStart,
        // next,
        // prev,
        // canNext,
        // canPrev,
        // currentPage,
        // pageCount,
        // paginationData,
        // isPending,
        // haveBeenRequested,
        // haveLoaded,
        // error,
        searchInput
    }
}
