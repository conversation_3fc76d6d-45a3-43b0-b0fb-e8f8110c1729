import {AnyRef} from 'src/utils/types';
import {ref, watch} from 'vue';

type Args = {
    list: AnyRef<Array<any>>,
    prefix:string
}
export const tabHandler = ({list, prefix
}:Args) => {
    const hasFocused = ref(false);
    const focusIndex = ref(0);
    const handleFocus = (idx:any) => {
        focusIndex.value = idx;
        hasFocused.value = true;
    }
    let tabListener;
    const senseTab = () => {
        tabListener = document.addEventListener('keydown', (e:any) => {
            if (hasFocused.value && e.keyCode === 9) {
                let idx = focusIndex.value;
                if (idx < list.value.length - 1) idx = focusIndex.value + 1;
                else idx = 0;
                const nextId = `${prefix}-${idx}`;
                const el = document.getElementById(nextId);
                if (el) el.focus();
            }
        });
    };

    watch(list, (nv:any, ov:any) => {
        if (nv?.length !== ov?.data?.length) senseTab()
    }, { immediate: true })

    return {
        senseTab,
        handleFocus,
        hasFocused
    }
}
