<template>
  <q-select
      v-bind="{
      style: 'width: 100%; transition: all .2s ease-out;',
      placeholder: selected && selected.length ? '' : placeholder ? placeholder : 'Search Or Add Tags...',
      options: useOptions,
      useInput: true,
      newValueMode: 'add-unique',
      multiple: multiple,
      ...attrs,
        }"
      :model-value="selected"
      @focus="handleFocus"
      @input-value="addTagInput"
      @keyup.enter="add()"
      @clear="reload(0)"
  >
    <template v-slot:prepend>
      <q-icon :color="color" :name="prependIcon"></q-icon>
    </template>

    <template v-slot:append v-if="searchInput && adding">
      <q-btn size="sm" push :color="color" @click="add()" icon="mdi-plus"></q-btn>
    </template>

    <template v-slot:before-options v-if="searchInput && adding">
      <q-item clickable @click="add()">
        <q-item-section>
          <div>+ Add <span class="text-italic text-weight-medium">{{ searchInput }}</span></div>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:after-options v-if="canNext">
      <q-item dense clickable @click="reload(currentPage + 1)">
        <q-item-section>
          <q-item-label>
            <div class="row items-center">
              <div>Load More</div>
              <q-icon name="mdi-menu-down"></q-icon>
            </div>
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:no-option>
      <q-item>
        <q-item-section avatar v-if="isPending">
          <q-spinner></q-spinner>
        </q-item-section>
        <q-item-section>
          <q-item-label>
            {{ isPending ? 'Loading...' : emptyText }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:option="scope">
      <q-item clickable @click="add(scope.opt)">
        <q-item-section avatar>
          <q-icon :color="isSelected(scope.opt) ? color : 'grey-5'" :name="tagIcon"></q-icon>
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-xs text-mb-sm text-weight-light">{{ scope.opt }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:selected-item="scope">
      <q-chip
          :dark="dark"
          square
          :color="color"
          :icon="noIcon ? undefined : tagIcon"
          :label="$limitStr(scope.opt, 21)"
          removable
          @remove="removeTag(scope.opt)"
      >
        <q-tooltip v-if="scope.opt && scope.opt.length > 20">
          {{ scope.opt }}
        </q-tooltip>
      </q-chip>
    </template>

  </q-select>
</template>

<script setup>
  import {watch, computed, useAttrs} from 'vue';
  import {TagLoader} from 'src/utils/tag-loader';
  import {$limitStr} from 'src/utils/global-methods';

  const attrs = useAttrs();
  const props = defineProps({
    optionsIn: Array,
    noIcon: { type: Boolean, default: true },
    behavior: {
      type: String,
      default: 'menu'
    },
    placeholder: String,
    tagParams: { type: Object },
    emptyText: { type: String, default: 'No Tags - Add One' },
    inputClass: { type: String, default: 'font-1r' },
    multiple: {
      type: Boolean,
      default: true
    },
    adding: {
      type: Boolean,
      default: true
    },
    prependIcon: {
      type: String,
      default: 'mdi-at'
    },
    dark: {
      type: Boolean,
      default: true
    },
    skipText: {
      type: Number,
      default: 3
    },
    minTagLength: {
      type: Number,
      default: 2
    },
    color: {
      type: String,
      default: 'primary'
    },
    tagIcon: {
      type: String,
      default: 'mdi-at'
    },
    tagPath: {
      type: String,
      default: 'tags'
    },
    store: { required: false },
    modelValue: [String, Array],
    qid: { type: String, default: 'tags' },
  });

  const tp = computed(() => {
    if (props.tagParams) return props.tagParams;
    else return { query: {} };
  });

  const emit = defineEmits(['update:model-value', 'focus']);

  const {
    useOptions,
    isPending,
    searchInput,
    addInput,
    selected,
    isSelected,
    canNext,
    currentPage,
    load,
    add,
    reload
  } = TagLoader(
      {
        store: props.store,
        tagPath: props.tagPath,
        adding: props.adding,
        multiple: props.multiple,
        qId: props.qid,
        params: tp,
        staticOptions: props.optionsIn,
        emit: (s) => emit('update:model-value', s)
      }
  );

  const handleFocus = () => {
    !useOptions.value?.length ? load() : '';
    emit('focus');
  };

  const mv = computed(() => {
    return props.modelValue;
  });

  const removeTag = (tag) => {
    if (props.multiple) {
      const idx = selected.value.indexOf(tag);
      selected.value.splice(idx, 1)
      emit('update:model-value', selected.value);
    } else emit('update:model-value', undefined)
  }

  watch(mv, (nv) => {
    selected.value = nv;
  }, { immediate: true });

  const addTagInput = (val) => {
    if (props.adding) {
      searchInput.value = val;
      let timeout = 0;
      if (!val || !val.length) timeout = 200;
      setTimeout(() => {
        addInput.value = JSON.parse(JSON.stringify(val));
      }, timeout);
    }
  };

</script>
