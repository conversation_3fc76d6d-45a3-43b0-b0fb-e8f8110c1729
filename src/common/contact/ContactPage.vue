<template>
  <q-page class="pd12">

    <div class="q-pb-xl q-mb-xl row justify-center">
      <div>
        <div class="text-xl text-weight-bold text-center">Reach us by email</div>
        <div class="q-py-lg">
          <div class="row justify-center">
            <q-chip
                v-for="(email, i) in emails"
                :key="`email-${i}`"
                size="xl"
                color="white"
                :text-color="email.color"
                icon="mdi-email"
            >
          <span class="q-ml-sm">
            <span class="text-weight-bold">{{ email.label }}</span><span class="text-black">@commoncare.org</span>
          </span>
            </q-chip>
          </div>
        </div>
      </div>
    </div>

    <reach-out
        title="Let's talk"
        caption="We are building something awesome, we love doing it, and we are quite happy to talk with anyone about it."
    ></reach-out>

  </q-page>
</template>

<script setup>

  import ReachOut from './ReachOut.vue';
  import {ref} from 'vue';

  const emails = ref([
    {
      label: 'groups',
      email: '<EMAIL>',
      color: 'primary'
    },
    {
      label: 'providers',
      email: '<EMAIL>',
      color: 'accent'
    },
    {
      label: 'participants',
      email: '<EMAIL>',
      color: 'secondary'
    }
  ])
</script>

<style lang="scss" scoped>

</style>
