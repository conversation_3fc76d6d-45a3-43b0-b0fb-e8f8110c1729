<template>
  <div :class="`full-width bg-${bg}`">
    <div class="row justify-center">
      <div class="_cent">
        <div class="row justify-center">

          <div :class="`col-12 col-md-6 q-pa-md`">
            <div class="row justify-center justify-md-end">
              <div style="width: 600px; max-width: 100%; padding: 0 1vw;">
                <div class="text-xxl text-mb-xxl text-weight-bold">{{ title }}</div>
                <q-separator :dark="dark" class="q-my-sm"></q-separator>
                <div class="text-md text-mb-md">{{ caption }}
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 q-pa-md">
            <div class="row justify-center">
              <div style="width: 600px; max-width: 100%;">
                <contact-base></contact-base>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import ContactBase from './ContactBase.vue';

  export default {
    name: 'ReachOut',
    components: {
      ContactBase
    },
    mixins: [],
    props: {
      toggle: Boolean,
      dark: Boolean,
      title: String,
      caption: String,
      textColor: { type: String, default: 'black' },
      accentColor: { type: String, default: 'primary' },
      bg: String
    },
    data() {
      return {
        tab: 'us',
        colInner: 'row justify-center'
      };
    },
    computed: {},
    methods: {}
  };
</script>

<style scoped lang="scss">
  .__reach_out {
    width: 100%;
    padding: 12vh 0;
    background: var(--q-secondary);
  }

  .__strip {
    width: 100%;
    //background: linear-gradient(12deg, var(--q-primary), var(--q-secondary));
    height: 13px;
    border-radius: 5px;
    margin: 4px 0;
  }
</style>
