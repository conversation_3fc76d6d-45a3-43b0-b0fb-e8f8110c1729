<template>
  <div class="_fa" :style="$attrs.divStyle">
    <div class="row justify-center _fh">
      <div
          :style="{'border-radius': '5px', 'box-shadow': '0 0 3px rgba(0,0,0,.4)', position: 'relative', height: height, width: width}"
          class="flex flex-center">

        <q-btn push class="b-r bg-white text-primary" dense icon="mdi-download"
               @click="openFile(file, $router)"/>

        <iframe sandbox="" v-if="canPreview" :src="url" height="100%" width="100%" frameborder="0"></iframe>
        <q-img v-else-if="fileType" style="width: 80px; height: 150px; max-height: 100%;" fit="cover"
               :src="previewSrc"></q-img>
      </div>
    </div>
    <div v-if="nameOn" class="row justify-center" :style="{width: width}">
      <div :class="textClass ? textClass : 'text-xxxs text-mb-xxs'">{{ file.name }}</div>
    </div>
  </div>
</template>

<script setup>
  import csv from 'src/assets/icons/excel.png';
  import pdf from 'src/assets/icons/pdf.png';
  import doc from 'src/assets/icons/doc.png';
  import excel from 'src/assets/icons/excel.png';
  import {_get} from 'symbol-syntax-utils';
  import {computed} from 'vue';
  import {Screen} from 'quasar';
  import {openFile} from 'src/components/common/uploads/utils/files';

  const props = defineProps({
    divStyle: Object,
    placeholder: String,
    download: {
      type: Boolean,
      default: true
    },
    btnSize: { type: String, default: 'md' },
    nameOn: Boolean,
    textClass: String,
    file: Object,
    urlIn: String,
    height: {
      type: String,
      default: '100%'
    },
    width: {
      type: String,
      default: '100%'
    }
  })

  const types = computed(() => {
    return {
      'xlx': {
        src: excel
      },
      'spreadsheet': {
        src: csv
      },
      'csv': {
        src: csv
      },
      'pdf': {
        preview: false,
        src: pdf
      },
      'doc': {
        preview: false,
        src: doc
      },
      'image': {
        preview: true,
        src: props.urlIn
      },
      'jpg': {
        preview: true,
        src: props.urlIn
      },
      'jpeg': {
        preview: true,
        src: props.urlIn
      },
      'png': {
        preview: true,
        src: props.urlIn
      }
    }
  });

  const getFileUrl = () => {
    return props.file?.url
  };

  const url = computed(() => props.urlIn || getFileUrl(props.file));

  const canPreview = computed(() => {
    return Screen.gt.xs && _get(types.value, [fileType.value, 'preview']);
  })

  const previewSrc = computed(() => {
    return _get(types.value, [fileType.value, 'src']);
  });

  const fileType = computed(() => {
    const type = props.file?.info?.type || url.value?.split(/[#?]/)[0].split('.').pop().trim();

    for (const key in types.value) {
      if (type.indexOf(key) > -1) {
        return key;
      }
    }
    return 'image'
  })
</script>
