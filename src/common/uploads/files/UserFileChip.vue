<template>
  <q-chip
      v-bind="{
        class: 'relative-position tw-five',
        flat: true,
        clickable: true,
        color: 'white',
        ...$attrs,
        label: undefined,
        iconRight: undefined,
        icon: undefined
      }"
      class="relative-position"
      @click="dialog = true"
  >
    <span class="q-mx-xs">{{ label }}</span>
    <q-icon v-if="icon" v-bind="{name: icon?.name || icon, color: 'primary', ...icon}"></q-icon>
    <common-dialog v-model="dialog">
      <file-uploader
          @update:model-value="emitVal"
          :allow-types="allowTypes"
          :max-size="maxSize"
      ></file-uploader>
    </common-dialog>
  </q-chip>
</template>

<script setup>

  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import FileUploader from 'src/components/common/uploads/files/FileUploader.vue';
  import {ref} from 'vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    label: { default: 'Choose New File' },
    maxSize: Number,
    icon: {
      default: () => {
        return {
          name: 'mdi-plus',
          color: 'primary'
        }
      }
    },
    allowTypes: { type: [String, Array] }
  })

  const dialog = ref(false);
  const emitVal = (val) => {
    emit('update:model-value', val);
    dialog.value = false;
  }
</script>

<style lang="scss" scoped>

</style>
