<template>
  <div class="_fw q-pa-lg">
    <file-uploader-chip
        :chip-attrs="{ class: 'relative-position tw-six' }"
        :upload="upload"
        :allow-types="allowTypes"
        :max-size="maxSize"
    ></file-uploader-chip>
    <div class="q-py-sm tw-five font-1r">Choose from your files:</div>
    <q-input dense v-model="search.text">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>
    <div class="__img_grid q-py-md">
      <div
          v-for="(file, i) in h$.data || []"
          :key="`file-${i}`"
          class="cursor-pointer br5 bs2-5 _oh relative-position"
          @click="$emit('update:model-value', getFileSchema(file))"
      >
        <file-type-handler
            height="150px" width="100%"
            :file="file"
            :url="file.url"
            :icon-attrs="{ size: '130px'}"
        ></file-type-handler>
        <div class="__l tw-six text-white">
          {{$limitStr(file.info?.name, 26, '...', true)}}
          <q-tooltip>{{file.info?.name}}</q-tooltip>
        </div>
      </div>
    </div>
    <div class="row justify-end">
      <q-pagination
          flat
          @update:model-value="h$.toPage($event)"
          :model-value="pagination.currentPage"
          :min="1"
          :max="pagination.pageCount"
          direction-links
          boundary-numbers
      ></q-pagination>
    </div>
  </div>
</template>

<script setup>
  import FileUploaderChip from 'src/components/common/uploads/files/FileUploaderChip.vue';
  import FileTypeHandler from 'src/components/common/uploads/file-types/fileTypeHandler.vue';

  import { loginPerson } from 'src/stores/utils/login';
  const { login } = loginPerson()

  import {HFind} from 'src/utils/hFind';
  import { HQuery } from 'src/utils/hQuery';
  import {computed, ref} from 'vue';
  import {storjUpload, getFileSchema} from 'src/components/common/uploads/services';
  import { useUploads } from 'src/stores/uploads';
  import {$limitStr} from 'src/utils/global-methods';
  const store = useUploads();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    allowTypes: [String,Array],
    maxSize: Number
  })

  const search = ref({ text: '', keys: ['info.name', 'originalname']})
  const { searchQ } = HQuery({
    search
  })

  const params = computed(() => {
    const query = {
      ...searchQ.value,
      $sort: { updatedAt: -1 },
      'createdBy.login': login.value?._id
    };
    if(props.allowTypes){
      const format = (arr) => arr.join('|').split('/*').join('');
      const arrTypes = Array.isArray(props.allowTypes) ? format(props.allowTypes) : format(props.allowTypes.split(','));
      // const $regex = arrTypes
      const $regex = `(?:${arrTypes})`
      query['info.type'] = { $regex, $options: 'i' }
    }
    return { query }
  });

  const { h$, pagination } = HFind({
    store,
    params,
    limit: ref(6)
  })

  const uploadEmit = (evt, args) => {
    const obj = {
      'update:model-value': () => {
        emit('update:model-value', args);
      }
    };
    obj[evt] ? obj[evt]() : console.log('no evt handler for event ', evt);
  };

  const upload = storjUpload(undefined, { emit: uploadEmit })
</script>

<style lang="scss" scoped>
  .__img_grid {
    display: grid;
    width: 100%;
    grid-template-columns: repeat(auto-fit, minmax(150px, 180px));
    grid-template-rows: repeat(auto-fit, 150px);
    grid-gap: 5px;
  }
  .__l {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 5px;
    background: rgba(0,0,0,.7);
  }
</style>
