<template>
  <q-chip
      clickable
      @drop="dropHandler($event)"
      v-bind="{
        class: 'relative-position tw-five cursor-pointer',
        flat: true,
        color: 'white',
        ...$attrs,
        label: undefined,
        iconRight: undefined,
        icon: undefined,
        ...chipAttrs,
        style: 'position: relative'
      }"
  >
    <q-spinner color="primary" v-if="loading"></q-spinner>
    <span class="q-mx-xs">{{ displayText ? displayText : errorText ? errorText : label }}</span>
    <q-icon v-if="icon" v-bind="{name: icon?.name || icon, color: 'primary', size: '23px', ...icon}"></q-icon>
    <input :accept="allowTypes" type="file" class="__input" @change="dropHandler($event)">
  </q-chip>
</template>

<script setup>

  import {uploadFiles} from 'src/components/common/uploads';

  const props = defineProps({
    log: <PERSON><PERSON><PERSON>,
    label: { default: 'Upload File' },
    upload: Function,
    maxSize: Number,
    chipAttrs: Object,
    icon: {
      default: () => {
        return {
          name: 'mdi-upload',
          color: 'primary'
        }
      }
    },
    allowTypes: { type: [String, Array] }
  })

  const { display, dropHandler, displayText, errorText, loading } = uploadFiles({
    log: props.log,
    allowTypes: props.allowTypes ? Array.isArray(props.allowTypes) ? props.allowTypes : props.allowTypes.split(' ').join('').split(',') : undefined,
    upload: props.upload
  });
</script>

<style lang="scss" scoped>
  .__input {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
</style>
