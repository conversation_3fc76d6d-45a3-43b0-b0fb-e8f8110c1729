<template>
  <div class="_fw __fp">
    <template v-if="!download && canPreview && url">
      <q-img v-if="fileType === 'image'" fit="contain" :src="url" class="_fa"></q-img>
      <PdfObject v-else :url="upload?.url" id="pdfViewer" style="width: 100%; height: 100vh; border: none;"></PdfObject>
    </template>
    <div v-else class="_fa flex flex-center">
      <div class="w300 mw100">
        <q-item class="bg-white" v-if="upload">
          <q-item-section avatar>
            <file-type-handler
                height="100px" width="75px" :file="upload"></file-type-handler>
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ upload?.info?.name || 'untitled' }}</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-btn flat dense icon="mdi-download" @click="downloadFile(upload)"></q-btn>
          </q-item-section>
        </q-item>
        <div v-else class="row justify-center">
          <q-spinner size="50px" color="primary"></q-spinner>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import FileTypeHandler from 'components/common/uploads/file-types/fileTypeHandler.vue';

  import {downloadFile, getFileType} from 'components/common/uploads/utils/files';
  import {nextTick, onBeforeUnmount, onMounted, ref} from 'vue';
  import {useRoute} from 'vue-router';
  import {useUploads} from 'stores/uploads';

  const props = defineProps({
    modelValue: { required: false }
  })

  const store = useUploads();
  const route = useRoute();

  const canPreview = ref(false);
  const download = ref(false);
  const upload = ref(undefined);
  const url = ref('');
  const fileType = ref('')

  onBeforeUnmount(() => {
    const el = document.getElementById('pdf-object');
    if(el) document.getElementsByTagName('head')[0].removeChild(el);
  })
  onMounted(async () => {
    if (!document.getElementById('pdf-object')) {
      const scriptTag = document.createElement("script");
      scriptTag.src = "https://unpkg.com/pdfobject";
      scriptTag.id = "pdf-object";
      document.getElementsByTagName('head')[0].appendChild(scriptTag);
    }
    if(props.modelValue?.url) upload.value = props.modelValue;
    else {
      const id = props.modelValue?.uploadId || route.params.id;
      upload.value = await store.get(id);
    }

    const type = getFileType(upload.value, upload.value.url);
    fileType.value = type;
    if(['video', 'image'].includes(type)) {
      canPreview.value = true;
      url.value = upload.value.url;
    } else if (['pdf'].includes(type)) {
      canPreview.value = true;
      nextTick(async () => {
        const res = await fetch(upload.value.url, { method: 'GET', headers: { 'Content-Type': upload.value.info.type } })
        const blob = await res.blob();
        const reader = new FileReader();
        reader.onload = (e) => {
          const base64String = e.target.result;
          const mimeType = blob.type;
          const dataURL = `data:${mimeType};base64,${base64String}`;
          url.value = dataURL;
        }
        reader.readAsDataURL(blob)
      })

    } else download.value = true
  })
  onBeforeUnmount(() => {
    if(url.value) window.URL.revokeObjectURL(url.value);
  })
</script>

<style lang="scss" scoped>
  .__fp {
    height: 100%;
  }
</style>
