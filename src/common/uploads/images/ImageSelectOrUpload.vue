<template>
  <div class="__logo" :style="{ width, height }" @click="dialog = true">

    <div v-if="!useImg || loading" :class="`_fa br5${round ? '50' : ''} flex flex-center bg-ir-bg2`">
      <q-spinner v-if="loading" color="primary" size="30px"></q-spinner>
      <q-icon v-else color="ir-mid" name="mdi-image" size="30px"></q-icon>
    </div>
    <q-img class="_fa" :src="useImg"></q-img>

    <common-dialog setting="smmd" v-model="dialog">
      <div class="_fw q-pa-md">
        <div class="row">
          <div>
            <div class="font-7-8r tw-six text-ir-mid q-pb-sm">Upload</div>

            <image-form title="Click or drop" height="60px" width="60px" :model-value="modelValue" @update:model-value="addImage" @update:display="display = $event"></image-form>
          </div>
        </div>
        <div class="q-py-sm">
          <div class="font-7-8r tw-six text-ir-mid">Select</div>

          <div class="w500 mw100 q-py-md">
            <q-input dense filled v-model="search.text">
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
            </q-input>
          </div>

          <div class="row items-center">
            <div v-if="!u$.data.length" class="q-pa-md text-italic text-xs">You have not uploaded any files</div>
            <div class="__c" v-for="(upload,i) in u$.data" :key="`u-${i}`">
              <div @click="selectImage(upload)">
                <q-img class="_fa" :src="upload.url"></q-img>
              </div>
            </div>
          </div>
        </div>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import ImageForm from 'components/common/uploads/images/ImageForm.vue';

  import {computed, ref} from 'vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {loginPerson} from 'stores/utils/login';
  import {useUploads} from 'stores/uploads';
  import {getFileSchema} from 'components/common/uploads/services';

  const { login } = loginPerson()
  const uploadStore = useUploads();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    width: { default: '80px' },
    height: { default: '80px' },
    round: Boolean
  })

  const dialog = ref(false);
  const loading = ref(false);

  const display = ref('')

  const useImg = computed(() => {
    const obj = props.modelValue
    if(!obj) return display.value
    if(typeof obj === 'object') return obj.url;
    return obj || display.value
  })

  const { search, searchQ } = HQuery({ keys: ['name', 'info.name']})

  const limit = ref(10);
  const { h$:u$ } = HFind({
    store: uploadStore,
    limit,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          'createdBy.login': login.value._id,
          $sort: { createdAt: -1 },
          'info.type': { $regex: 'image|png|jpg|jpeg|svg', $options: 'i' }
        }
      }
    })
  })

  const selectImage = (upload) => {
    emit('update:model-value', getFileSchema(upload));
    dialog.value = false
  }

  const addImage = (v) => {
    emit('update:model-value', v);
    dialog.value = false
  }
</script>

<style lang="scss" scoped>

  .__image_select_upload {
    cursor: pointer;
    transition: all .3s;
    position: relative;
    border-radius: 5px;
  }
    .__logo {
      height: 60px;
      width: 60px;
      cursor: pointer;
      transition: all .3s;
      position: relative;
      border-radius: 5px;

      &:hover {
        transform: translate(0, -3px);
      }
    }
    .__form {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      overflow: hidden;
      opacity: 0;
      z-index: 2;
    }

    .__c {
      padding: 8px;

      > div {
        height: 60px;
        width: 60px;
        overflow: hidden;
        border-radius: 5px;
        box-shadow: 1px 1px 4px var(--ir-light);
        cursor: pointer;
        transition: all .3s;

        &:hover {
          transform: translate(0, -2px);
        }
      }
    }
</style>
