<template>
  <div v-bind="divAttrs">
    <q-tabs v-model="tab" @update:model-value="touched = true">
      <q-tab name="library" label="Uploads" icon="mdi-upload"></q-tab>
      <q-tab name="upload" label="Add New" icon="mdi-plus"></q-tab>
    </q-tabs>
    <q-tab-panels animated v-model="tab" style="padding: 0">
      <q-tab-panel name="library" style="padding:0">
        <div class="row items-center">
          <q-img
              v-for="(image, i) in h$.data" :key="`image-${i}`"
              :src="getImg(image)"
              @click="handleInput(image, true)"
              v-bind="{
                style: 'width: 100px; height: 100px; max-width: 100%; border-radius: 5px',
                fit: 'cover',
                class: 'q-ma-xs cursor-pointer',
                ...imgAttrs
              }"
          ></q-img>
        </div>

        <div class="row justify-end">
          <q-pagination
              @update:model-value="h$.toPage($event)"
              :model-value="pagination.currentPage"
              :min="1"
              :max="pagination.pageCount"
              direction-links
              boundary-numbers
          ></q-pagination>
        </div>

      </q-tab-panel>
      <q-tab-panel name="upload" style="padding:0">
        <div class="row justify-center q-py-md">
          <image-form
              :storage="storage"
              @update:display="updateDisplay"
              :model-value="modelValue"
              @update:model-value="handleInput"
          ></image-form>
        </div>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import ImageForm from 'src/components/common/uploads/images/ImageForm.vue';

  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useUploads} from 'src/stores/uploads';
  import {watch} from 'vue';

  const uploadStore = useUploads();
  import {getFile} from 'src/utils/fs-utils';
  import {_get as lget} from 'symbol-syntax-utils';
  // import { useWnfs } from 'src/stores/wnfs';
  // const wnStore = useWnfs();

  const props = defineProps({
    divAttrs: {
      type: Object,
      default: () => {
        return {
          class: '__is q-pa-md'
        };
      }
    },
    imgAttrs: Object,
    id: { type: String, default: 'image-form' },
    smSize: { type: Number, default: 300 },
    modelValue: { type: [String, Array, Object], required: true },
    multiple: Boolean,
    storage: { type: String, default: 'storj' },
    filePath: { type: [String, Array], default: 'images' },
    fileName: String,
    params: Object
  });

  const touched = ref(false);
  const tab = ref('library');

  const params = computed(() => {
    return props.params ? props.params : { query: { createdBy: null } };
  });


  const {
    h$,
    pagination
  } = HFind({
    store: uploadStore,
    params
  });


  const haveLoaded = computed(() => h$.haveLoaded)
  watch(haveLoaded, (nv) => {
    if (nv && !touched.value && !h$.total) {
      tab.value = 'upload';
    }
  });

  const emit = defineEmits(['update:model-value', 'update:display']);

  const handleInput = (val, select) => {
    tab.value = 'library';
    const getEmitVl = fl => {
      const { _id, fileId, storage, appPath, subPath, url } = fl;
      return { _id, fileId, storage, appPath, subPath, url };
    };
    let payload;
    let mult = Array.isArray(val);
    if (props.multiple) {
      if (!mult) {
        val = [val];
        mult = true;
      }
      payload = val.map(a => getEmitVl(a));
    } else payload = getEmitVl(val);
    emit('update:model-value', payload);

    if (select) {
      const getDisplay = v => {
        return lget(v, '_fastjoin.files.file');
      };
      if (mult) updateDisplay(val.map(a => getDisplay(a)));
      else updateDisplay(getDisplay(val));
    }

  };

  const updateDisplay = urls => {
    emit('update:display', urls);
  };

  const getImg = file => {
    return getFile({ obj: file, path: ['file'] });
  };


</script>

<style scoped>
  .__is {
    width: 600px;
    max-width: 100%;
  }
</style>
