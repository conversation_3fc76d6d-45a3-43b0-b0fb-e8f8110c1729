<template>
  <div v-bind="{class: '__if', ...attrs.divAttrs, style: { position: 'relative', height, width, cursor: 'pointer' }}">
    <div class="__wrap">
      <div class="relative-position _fw _fh">
        <div v-if="_get(fileUrls, [1])" class="__a __l">
          <q-btn dense size="sm" flat icon="mdi-chevron-left" @click.stop="prev()"></q-btn>
        </div>
        <div v-if="_get(fileUrls, [1])" class="__a __r">
          <q-btn dense size="sm" flat icon="mdi-chevron-right" @click.stop="next()"></q-btn>
        </div>

        <div id="UploadUi" class="__img_form_d" :style="{height: height, width: width}">
          <upload-ui
              :multiple="multiple"
              :log="log"
              :id="`${id}`"
              :sm-size="smSize"
              :title="title"
              :max-size="maxSize"
              :upload="configs[storage].upload"
              :model-value="activeUrl"
          ></upload-ui>
        </div>

        <q-btn v-if="_get(fileUrls, [1])" class="t-r-f" flat size="xs" color="negative" dense icon="mdi-close"
               @click="remove(active)"></q-btn>
      </div>
    </div>
  </div>
</template>

<script setup>
  import UploadUi from '../components/UploadUi.vue';

  import {useAttrs, computed, ref, watch} from 'vue';
  import {getFile} from 'src/utils/fs-utils';
  import {_get, _set} from 'symbol-syntax-utils';
  import { storjUpload } from '../services';
  import {idGet} from 'src/utils/id-get';
  import {useUploads} from 'stores/uploads';
  const attrs = useAttrs();
  const fileUrls = ref([]);
  const active = ref(0);
  const uploadedUrls = ref([]);
  const uploadStore = useUploads();

  const emit = defineEmits(['uploading', 'done', 'update:model-value', 'update:display']);
  const props = defineProps({
    expires: String,
    id: { type: String, default: 'image-form' },
    smSize: { type: Number, default: 300 },
    maxSize: { type: Number, default: 150000 },
    modelValue: { type: [String, Array, Object], required: true },
    multiple: Boolean,
    uploadPath: { default: 'sm' }, //or raw
    storage: { type: String, default: 'storj' },
    filePath: { type: [String, Array], default: 'images' },
    fileName: String,
    emitValue: { default: true },
    height: { type: String, default: '200px'},
    width: { type: String, default: '200px'},
    title: { type: String, default: 'Upload File'},
    log: Boolean
  });

  const mv = computed(() => {
    return props.modelValue;
  });

  const { item:upload } = idGet({
    store: uploadStore,
    value: computed(() => mv.value?.uploadId)
  })

  const activeUrl = computed(() => {
    return uploadedUrls.value?.length ? uploadedUrls.value[active.value] : upload.value?.url || _get(fileUrls.value, [active.value]) || mv.value?.url;
  });

  const next = () => {
    if (active.value < fileUrls.value.length - 1) {
      active.value++;
    } else active.value = 0;
  };

  const prev = () => {
    if (active.value > 0) active.value--;
    else active.value = fileUrls.value.length - 1;
  };

  const emitWrap = () => {
    return (evt, args) => {
      const fns = {
        'update:display': () => {
          uploadedUrls.value = args;
          emit('update:display', args);
        }
      };

      if(fns[evt]) fns[evt](args)
      else emit(evt, args);
    }
  }


  const configs = computed(() => {
    // const getCreatedBy = () => {
    //   return JSON.stringify({
    //       login: authStore.user?._id,
    //       fingerprint: LocalStorage.getItem('fpId'),
    //       origin: SessionStorage.getItem('fqdn'),
    //       longtail: window.location.href
    //   })
    // }
    return {
      'storj': {
        upload: storjUpload(_get(mv.value, props.filePath), { expires: props.expires, emit:emitWrap(), multiple: props.multiple, log: props.log, uploadPath: props.uploadPath }),
        promise: false,
        get: val => {
          const path = Array.isArray(props.filePath) ? props.filePath : [props.filePath];
          if (props.multiple) {
            fileUrls.value = val.map(a => {
              getFile({ obj: a, path });
            });
          }
          const url = getFile({ obj: val, name: 'storj_get', path });
          fileUrls.value = [url];
        }
      }
    };
  });

  const remove = i => {
    fileUrls.value.splice(i, 1);
    let arr = _get(mv.value, props.filePath, []).map(a => a);
    arr.splice(i, 1);
    if(props.emitValue) emit('update:model-value', arr);
    else emit('update:model-value', _set(mv.value, props.filePath, arr));
  };

  const retryUrl = (tries = 0) => {
    if(tries < 6) {
      setTimeout(() => {
        if(!activeUrl.value){
        configs.value[props.storage].get(mv.value);
        retryUrl(tries+1);
        }
      }, 1000);
    }
  }
  watch(mv, async (nv) => {
    if (nv) {
      configs.value[props.storage].get(nv);
      retryUrl(0);
    }
  }, { immediate: true });

</script>

<style scoped lang="scss">
  .__if {
    height: 200px;
    width: 200px;
  }

  .__wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .__a {
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    z-index: 10;
  }

  .__l {
    left: 0;
  }

  .__r {
    right: 0;
  }

  .__img_form_d {
    max-width: 100%;
  }
</style>
