<template>
  <q-card :square="square" :flat="flat" :style="styleIn" class="fill_size">
    <slot name="default"></slot>
    <q-img :contain="contain" style="border-radius: inherit; height: 100%; width: 100%;" :src="imageSrc">
      <!--        <div class="b-l-a" v-if="contentSlot">-->
      <slot name="content"></slot>
      <!--        </div>-->
    </q-img>
    <template v-if="multiLength > 1">
      <q-btn size="xs" round flat
             class="__right-arrow text-dark bg-shade-2 text-white" @click.stop="forward"
             :style="!btnPosition ? {} : btnPosition === 'top' ? { top: '150px' } : btnPosition === 'bottom' ? { bottom: '150px'} : {}"
      >
        <q-icon name="mdi-menu-right" size="25px"/>
      </q-btn>
      <q-btn
        size="xs" round flat
        class="__left-arrow text-dark bg-shade-2 text-white" @click.stop="backward"
        :style="!btnPosition ? {} : btnPosition === 'top' ? { top: '150px' } : btnPosition === 'bottom' ? { bottom: '150px'} : {}"
      >
        <q-icon name="mdi-menu-left" size="25px"/>
      </q-btn>
    </template>
  </q-card>
</template>

<script>
  import { getFile } from 'src/utils/fs-utils';
  import { _get } from 'symbol-syntax-utils';

  export default {
    name: 'MultiImageViewer',
    props: {
      store: { required: false },
      square: Boolean,
      contain: Boolean,
      btnPosition: String,
      flat: Boolean,
      modelValue: [Array, String, Object],
      avatarPath: {
        type: [String, Array],
        default: () => {
          return ['avatar'];
        }
      },
      imgSize: {
        type: String,
        default: 'medium'
      },
      styleIn: [Object, String],
      defaultImg: String
    },
    data() {
      return {
        tab: 0
      };
    },
    computed: {
      multiLength() {
        return this.modelValue && Array.isArray(this.modelValue[this.avatarPath]) ? this.modelValue[this.avatarPath].length : 0;
      },
      contentSlot() {
        if (this.$slots.content) return true;
        else return false;
      },
      imageSrc() {
        const path = Array.isArray(this.avatarPath) ? this.avatarPath : [this.avatarPath];
        if (Array.isArray(_get(this.modelValue, [this.avatarPath]))) {
          return getFile({obj: this.modelValue, path, index: this.tab, def: this.defaultImg});
        } else {
          return getFile({obj: this.modelValue, path, def: this.defaultImg});
        }
      }
    },
    methods: {
      forward() {
        let i = this.tab;
        if (i < this.multiLength - 1) this.tab++;
        else this.tab = 0;
      }
      ,
      backward() {
        let i = this.tab;
        if (i > 0) this.tab--;
        else this.tab = this.multiLength - 1;
      }
    }
  }
  ;
</script>

<style scoped lang="scss">
  .__right-arrow {
    position: absolute;
    top: 50%;
    right: 2%;
    transform: translateY(-100%);
    transition: transform .2s ease-out;

    &:hover {
      transform: translateY(-80%) scale(1.3)
    }
  }

  .__left-arrow {
    position: absolute;
    top: 50%;
    left: 2%;
    transform: translateY(-100%);
    transition: transform .2s ease-out;

    &:hover {
      transform: translateY(-80%) scale(1.3)
    }
  }
</style>
