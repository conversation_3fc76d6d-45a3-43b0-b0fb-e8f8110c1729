<template>
  <div class="_fw">
    <user-file-chip
        :login-id="loginId"
        :use-uploads="useUploads"
        :max-size="maxSize"
        :allow-types="allowTypes"
        @update:modelValue="emitFile"
    ></user-file-chip>
    <div class="row items-center q-py-xs">
      <div class="q-pa-xs cursor-pointer relative-position" v-for="(file, i) in modelValue" :key="`file-${i}`">
        <q-badge v-if="isSelected(file)" floating color="white">
          <q-icon name="mdi-check-circle-outline" color="green"></q-icon>
        </q-badge>
        <file-type-handler
            :file="file"
            :url="file?.url"
            :height="size"
            :width="size"
            :icon-attrs="{ size: size }"
        ></file-type-handler>
        <div v-bind="{ class: '__l', ...labelAttrs }">
          {{$limitStr(file.info?.name, labelLimit, '...', true)}}
          <q-tooltip>{{file.info?.name}}</q-tooltip>
        </div>
        <q-menu>
          <slot name="menu" v-bind="{file, index: i}">
            <div class="w250 br10 bg-white q-pa-sm">
              <q-list separator>
                <q-item v-if="selectLabel" clickable @click="$emit('update:selected', isSelected(file) ? undefined : file, i)">
                  <q-item-section>
                    <q-item-label>{{isSelected(file) ? 'Undo ' : ''}}{{selectLabel}}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-icon name="mdi-image"></q-icon>
                  </q-item-section>
                </q-item>
                <remove-item name="File" @remove="emit('remove', file, i)"></remove-item>
              </q-list>
            </div>
          </slot>
        </q-menu>
      </div>
    </div>
  </div>
</template>

<script setup>
  import UserFileChip from 'src/components/common/uploads/files/UserFileChip.vue';
  import FileTypeHandler from 'src/components/common/uploads/file-types/fileTypeHandler.vue';
  import RemoveItem from 'components/common/buttons/RemoveItem.vue';

  import {$limitStr} from 'src/utils/global-methods';

  const emit = defineEmits(['update:model-value', 'remove', 'update:selected']);
  const props = defineProps({
    loginId: { required: true, type: String },
    useUploads: { required: true, type: Function },
    selected: Object,
    modelValue: Array,
    maxSize: Number,
    labelAttrs: Object,
    labelLimit: { default: 13 },
    size: { default: '60px' },
    selectLabel: String,
    allowTypes: { default: 'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,image/*,text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }
  })

  const emitFile = (args) => {
    const v = [...(props.modelValue || []), args];
    emit('update:model-value', v)
  }

  const isSelected = (file) => {
    return props.selected?.uploadId === (file?.uploadId || '*')
  }

</script>

<style lang="scss" scoped>
  .__l {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 5px;
    background: rgba(0,0,0,.7);
    color: white;
    font-weight: 500;
    font-size: .7rem;
  }
</style>
