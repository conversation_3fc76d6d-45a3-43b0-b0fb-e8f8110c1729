<template>
  <div class="_fw">
    <div class="__csv_upload font-1r">
      <upload-ui
          :max-size="maxFileSize"
          :upload="stage"
          bg="linear-gradient(180deg, #eee, #f0f0f0)"
          text-color="black"
          allow-types="text/csv,.xls,.xlsx,.xlsm,spreadsheet">
        <template v-slot:text="scope">
          <span class="font-1r" v-if="!scope.displayText && !scope.errorText">Select or drop file 📁</span>
          <span v-else-if="!!scope.displayText">{{ scope.displayText }}</span>
          <span v-else style="color: #b80000">{{ scope.errorText }}</span>
        </template>
      </upload-ui>
    </div>
    <div v-if="tooBig" class="q-pa-md text-center _fw font-1r tw-six">
      <div class="_fw text-accent">Whoa! Your file is too big</div>
      <div class="_fw">Max Size: <span class="alt-font text-accent">{{bytes(maxFileSize)}}</span></div>
      <div class="_fw">Your File Size: <span class="alt-font text-secondary">{{bytes(raw?.size)}}</span></div>
    </div>
    <q-item v-if="file">
      <q-item-section avatar>
        <file-type-handler v-if="raw?.type" :file="raw"></file-type-handler>
      </q-item-section>
      <q-item-section>
        <q-item-label><b>{{ raw?.name }}</b></q-item-label>
      </q-item-section>
      <q-item-section side>
        <q-btn dense flat icon="mdi-close" color="red" @click="clearFile"></q-btn>
      </q-item-section>
    </q-item>
    <q-separator class="q-my-md"></q-separator>
    <div class="row items-center">
      <q-tabs v-if="!response?.updated && data?.length" active-color="primary" dense no-caps v-model="tab">
        <q-tab v-if="data?.length > 1" name="worksheet">
          <span class="text-weight-bold">Choose Sheet</span>
        </q-tab>
        <q-tab :disable="!data" name="headers">
          <span class="text-weight-bold">Choose Headers</span>
        </q-tab>
      </q-tabs>
      <q-space></q-space>
      <q-chip clickable color="transparent" @click="downloadHeaders">
        <q-icon name="mdi-download" color="primary"></q-icon>
        <span class="q-ml-sm font-7-8r">Download Headers</span>
      </q-chip>
    </div>

    <q-tab-panels v-if="data?.length" class="_panel" v-model="tab">

      <q-tab-panel class="_panel" name="worksheet">
        <div class="q-py-md font-1r text-weight-medium">
          Select a worksheet to use
        </div>
        <div class="row _fw __w">
          <q-btn v-for="(sh, i) in data" :key="`sheet-${i}`" square no-caps color="ir-grey-2" text-color="black"
                 :label="sh.name" @click="selectSheet(sh)"></q-btn>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="headers">

        <div class="q-pa-sm font-3-4r">
          Map data from
          <span v-if="data?.length > 1"><b>{{ sheet.name || '' }}</b></span>
          <span v-else>sheet</span>
        </div>
        <div class="row items-center">
        <q-checkbox label="First row is header row" :model-value="startIdx === 1"
                    @update:model-value="(val) => startIdx = val ? 1 : 0"></q-checkbox>
        </div>
        <div class="q-pa-sm font-1r text-weight-medium text-red" v-if="isErr">There are problems with your data, see
          below
        </div>
        <div class="row _fw __w">
          <table class="__extable font-7-8r">
            <thead>
            <tr>
              <td v-if="isErr">row</td>
              <td v-for="(head, i) in headerOptions" :key="`item-${i}`">
                <div class="flex items-center">
                  <div>{{ headerLabels ? headerLabels[head] || head : head }}</div>
                  <q-icon size="20px" :color="(exampleData || []).filter(a => a.header === head)[0]?.required ? 'red' : 'primary'" name="mdi-menu-down"></q-icon>
                  <q-menu>
                    <div class="__menu">
                      <div class="row justify-end" v-if="typeof selectedHeaders[head] === 'number'">
                        <q-btn dense flat size="sm" no-caps @click="delete selectedHeaders[head]">
                          <span class="tw-six">Remove Header</span>
                          <q-icon name="mdi-close" color="red"></q-icon>
                        </q-btn>
                      </div>
                      <q-list separator dense>
                        <q-item-label header>Select <span class="text-primary text-weight-bold">{{ head }}</span> column
                        </q-item-label>
                        <q-input dense filled v-model="headerSearch">
                          <template v-slot:prepend>
                            <q-icon name="mdi-magnify"></q-icon>
                          </template>
                        </q-input>
                        <q-item v-for="(item, idx) in filterHeaders(sheet.data[0])" :key="`item-${i}-${idx}`" clickable
                                @click="selectHeader(head, item.idx)">
                          <q-item-section>
                            <q-item-label>{{ item.text || '-' }}</q-item-label>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </div>
                  </q-menu>
                </div>
              </td>
            </tr>
            </thead>
            <tbody>
            <template v-if="!isErr">
              <tr v-for="i in 3" :key="`sample-${i}`">
                <td v-for="(item, idx) in headerOptions.filter(a => typeof selectedHeaders[a] === 'number')"
                    :key="`sample-${i}-${idx}`">
                  {{ _get(sheet, ['data', startIdx + i - 1, selectedHeaders[item]], '-') }}
                </td>
              </tr>
            </template>
            <template v-else>
              <tr v-for="(error, i) in Object.keys(errs)" :key="`error-${i}`">
                <td>{{ error }}</td>
                <td v-for="(item, idx) in Object.keys(selectedHeaders)" :key="`error-${i}-${idx}`" class="font-3-4r">
                  {{ _get(sheet, ['data', startIdx + i, selectedHeaders[item]], '-') }}<span
                    class="text-red">&nbsp;{{ _get(errs, [error, item, 0], '') }}</span>
                </td>
              </tr>
            </template>
            </tbody>
          </table>
        </div>
        <div class="row q-py-md">
          <q-btn :disable="!Object.keys(selectedHeaders).length" no-caps color="white" glossy push text-color="black" @click="save">
            <span v-if="!Object.keys(selectedHeaders).length" class="q-mr-sm tw-six">Select Headers</span>
            <span v-else class="q-mr-sm tw-six">Upload</span>
            <q-icon name="mdi-upload" color="accent"></q-icon>
          </q-btn>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="done">
        <div class="_fw text-center q-pa-md font-1-1-4r text-weight-bold">
          {{ !response?.status || response?.status === 200 ? 'Done!' : `Error uploading - ${response.err || 'Unknown Error'}` }}
        </div>
        <slot name="response" :response="response"></slot>
        <div class="row justify-center q-py-md">
          <q-btn @click="clearFile()" no-caps class="text-weight-bold" color="white" push>
            <q-icon color="primary" name="mdi-restart"></q-icon>
            <span class="text-black q-px-sm">Try Again</span>
          </q-btn>
        </div>
        <div class="row _fw __tw">
          <table class="__extable _fw font-7-8r">
            <thead>
            <tr>
              <td>Row</td>
              <td>Column</td>
              <td>Data</td>
              <td>Status</td>
              <td>Error</td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(er, i) in res.errors || []" :key="`er-${i}`">
              <td>{{ er.row + 1 }}</td>
              <td>{{ er.key }}</td>
              <td>{{ JSON.stringify(er.data) }}</td>
              <td>{{ `${er.throw ? 'Not added' : 'Still added'}` }}</td>
              <td :class="er.throw ? 'text-red' : ''">{{ er.err }}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </q-tab-panel>
    </q-tab-panels>

    <div v-else class="q-pa-md text-1r">
      <div class="text-center _fw text-weight-bold">Upload spreadsheet with the following data</div>
      <div class="text-center _fw font-3-4"><span class="text-red tw-six">&nbsp;&#42;</span> indicates required</div>
      <div class="__tw q-py-md">
        <table v-if="exampleData" class="__extable font-2-3r">
          <thead>
          <tr>
            <td v-for="(h, i) in exampleData" :key="`h-${i}`">
              {{ h.header || '' }}<span class="text-red tw-six" v-if="h.required">&nbsp;&#42;</span>
            </td>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td v-for="(b, i) in exampleData" :key="`b-${i}`">{{ b.ex || '' }}</td>
          </tr>
          </tbody>
        </table>
      </div>
      <slot name="bottom"></slot>
    </div>

  </div>
</template>

<script setup>
  import UploadUi from 'src/components/common/uploads/components/UploadUi.vue';
  import {computed, ref, watch} from 'vue';
  import {_get, _set} from 'symbol-syntax-utils';
  import {axiosFeathers, restCore} from '../services/utils';
  import FileTypeHandler from 'src/components/common/uploads/file-types/fileTypeHandler.vue';
  import VStatic from 'src/stores/validate';
  import {dollarString} from 'src/utils/global-methods';
  import {formatDate} from 'src/utils/date-utils';

  const emit = defineEmits(['ready', 'clear']);
  const props = defineProps({
    headerLabels: Object,
    headers: { required: true, type: [Array, String] },
    required: { type: [Array, String] },
    response: Object,
    exampleData: Array,
    maxFileSize: Number,
    exactHeaders: Boolean
  })

  const reqs = computed(() => Array.isArray(props.required) ? props.required : props.required ? props.required.split(',').map(a => a.trim()) : []);

  const { vCheck } = VStatic();

  const selectedHeaders = ref({});
  const selectHeader = (header, col) => {
    selectedHeaders.value[header] = col;
  }

  const headerSearch = ref('')
  const filterHeaders = (v) => {
    if(headerSearch.value) return (v || []).map((a, idx) => ({text: a, idx})).filter(a => !!a.text && String(a.text).toLowerCase().includes(headerSearch.value.toLowerCase()));
    else return v?.map((a, idx) => ({text: a, idx}));
  }

  const getHeaders = (h) => {
    if (Array.isArray(h)) return h;
    else if (h) return h.split(', ').map(a => a.trim()).join('').split(',');
  }
  const headerOptions = computed(() => {
    const hdrs = getHeaders(props.headers);
    return hdrs.sort((a, b) => {
      const A = selectedHeaders.value[a];
      const B = selectedHeaders.value[b];
      if(typeof A !== 'number') return 1;
      if(typeof B !== 'number') return -1;
      return A - B;
    })
  })

  const data = ref({});
  const tab = ref('headers');
  const file = ref(undefined);
  const raw = ref(undefined);
  const sheet = ref({});
  const startIdx = ref(1);
  const tooBig = ref(false);

  const bytes = (bytes, dec = 1) => {
    if (bytes < 1024) return `${bytes} B`;
    else if (bytes < 1048576) return `${dollarString(bytes / 1024, '', dec)} KB`;
    else if (bytes < 1073741824) return `${dollarString(bytes / 1048576, '', dec)} MB`;
    else if (bytes < 1099511627776) return `${dollarString(bytes / 1073741824, '', dec)} GB`;
    else if (bytes < 1125899906842624) return `${dollarString(bytes / 1099511627776, '', dec)} TB`;
    else return `${dollarString(bytes / 1125899906842624, '', dec)} PB`;
  }


  // Utility: tokenize string into lowercase underscore-separated array
  const tokenize = (str) =>
      String(str)
          .toLowerCase()
          .replace(/[^a-z0-9]+/gi, '_')
          .split('_')
          .filter(Boolean);
  const selectSheet = (sh) => {
    sheet.value = sh;
    tab.value = 'headers';

    const headerRow = (sheet.value.data || [])[0];
    const usedIndexes = new Set();

    headerOptions.value.forEach((expectedHeader) => {
      const normalizedExpected = props.exactHeaders
          ? String(expectedHeader).toLowerCase()
          : tokenize(String(expectedHeader));

      for (let idx = 0; idx < headerRow.length; idx++) {
        const cell = headerRow[idx];
        if (!cell || usedIndexes.has(idx)) continue;

        const normalizedCell = props.exactHeaders
            ? String(cell).toLowerCase()
            : tokenize(String(cell));

        if (props.exactHeaders) {
          if (normalizedCell === normalizedExpected) {
            selectedHeaders.value[expectedHeader] = idx;
            usedIndexes.add(idx);
            break;
          }
        } else {
          const match = normalizedExpected.some((token) =>
              normalizedCell.includes(token)
          );
          if (match) {
            selectedHeaders.value[expectedHeader] = idx;
            usedIndexes.add(idx);
            break;
          }
        }
      }
    });
  };
  const stage = async (files) => {
    const payload = new FormData();
    raw.value = files[0].raw;
    if(files[0].raw.size > (props.maxFileSize || Infinity)) return tooBig.value = true;
    const f = files[0];
    payload.append('name', _get(f, 'name'));
    payload.append('storage', 'parse');
    payload.append('file', f.sm);
    const info = {
      name: f.raw.name,
      size: f.raw.size,
      type: f.raw.type,
      lastModifiedDate: f.raw.lastModifiedDate
    }
    payload.set('info', JSON.stringify(info));
    file.value = payload;
    const fmt = await axiosFeathers().post('/uploads', payload, { params: { core: restCore() } })
    data.value = fmt.data;
    if (data.value?.length > 1) tab.value = 'worksheet';
    else selectSheet(data.value[0]);
  };

  const clearFile = () => {
    data.value = {};
    file.value = undefined;
    sheet.value = {};
    tab.value = 'headers';
    raw.value = undefined;
    emit('clear');
  }

  const errs = ref({});
  const isErr = ref(false);

  const res = computed(() => props.response);
  watch(res, (nv, ov) => {
    if (nv && nv.updated !== ov?.updated) {
      tab.value = 'done'
    }
  }, { immediate: true });

  const save = () => {
    //light validation
    isErr.value = false;
    sheet.value.data.forEach((a, i) => {
      // console.log('data', i, a);
      if (i >= startIdx.value) {
        (reqs.value || []).forEach(opt => {
          const idx = selectedHeaders.value[opt.field]

          if(opt.format){
            a[idx] = opt.format(a[idx])
            sheet.value.data[i][idx] = a[idx]
          }
          if (opt.v) {
            const checkErrs = vCheck({ 'field': opt.format ? opt.format(a[idx], a, selectedHeaders.value) : a[idx] }, {

              'field': {
                name: opt.field,
                v: opt.v
              }
            });
            // console.log(checkErrs);
            if (checkErrs?.length) isErr.value = true;
            if (!errs.value[`${i + 1}`]) errs.value[`${i + 1}`] = { [opt.field]: checkErrs }
            else errs.value[`${i + 1}`] = _set(errs.value[`${i + 1}`] || {}, opt.field, checkErrs);
          }
        })
      }
    })
    if (!isErr.value) {
      errs.value = {};
      emit('ready', file.value, {
        sheet: sheet.value?.name,
        headers: selectedHeaders.value,
        omitFirstRow: startIdx.value !== 0
      }, raw.value.size)
    }
  };

  const downloadHeaders = () => {
    const hdrs = getHeaders(props.headers);
    const reqr = getHeaders(props.required || []);

   const csvData = [hdrs.map(a => reqr.includes(a) ? `${a} *` : a)]

    const content = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob([content], { type: 'text/csv'});
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'CommonCareMemberHeaders.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

</script>

<style lang="scss" scoped>
  .__csv_upload {
    height: 50px;
    border-radius: 8px;
    width: 100%;
  }

  .__menu {
    width: 240px;
    padding: 10px;
    border-radius: 6px;
    background: white;
  }

  .__w {
    overflow-x: scroll;
    width: 100%;
  }

  .__tw {
    width: 100%;
    overflow-x: scroll;
    display: flex;
    justify-content: flex-start;
  }

  .__extable {
    text-align: left;
    font-weight: 600;
    width: 100%;

    border-collapse: collapse;

    td {
      padding: 5px 10px;
      border: solid .4px #999;
    }

    thead {
      border-collapse: collapse;
    }

    tbody {
      border-collapse: collapse;

      td {
        min-width: 60px;
        font-weight: 400;
        border-radius: 5px;
      }
    }
  }

  @media screen and (max-width: 1023px) {
    .__tw {
      justify-content: flex-start;
    }
  }
</style>
