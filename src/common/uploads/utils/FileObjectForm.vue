<template>
  <div class="_fw">
    <slot name="drop" v-bind="{upload, allowTypes}">
      <file-uploader-chip
          :allow-types="allowTypes"
          :upload="upload"
      ></file-uploader-chip>
    </slot>
    <div
        v-for="(k, i) in Object.keys(files)"
        :key="`k-${i}`"
    >
      <div class="row items-center">

        <q-input
            dense
            borderless
            style="width: 100px;"
            :model-value="k"
            input-class="text-right"
            @update:model-value="setKey(k, $event)" @blur="finishKey(k)"></q-input>
        <div class="q-mx-sm tw-seven">:</div>
        <div class="text-blue tw-five">{{ files[k].info?.name || files[k].originalname || 'Preparing File' }}
          <q-popup-proxy>
            <div class="w200 h280 bg-white br10">
              <file-preview :model-value="files[k]"></file-preview>
            </div>
          </q-popup-proxy>
        </div>
        <remove-proxy-btn name="file" icon="mdi-close" @remove="removeFile(k)"></remove-proxy-btn>
      </div>
    </div>
  </div>
</template>

<script setup>
  import FilePreview from 'components/common/uploads/pages/FilePreview.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import FileUploaderChip from 'components/common/uploads/files/FileUploaderChip.vue';

  import {computed} from 'vue';
  import {_get} from 'symbol-syntax-utils';
  import {storjUpload} from 'components/common/uploads/services';
  import {objKeyEdit} from 'components/common/uploads/utils/index';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object,
    allowTypes: { default: 'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,image/*,text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}
  })

  const files = computed(() => {
    const keys = Object.keys(props.modelValue || {});
    const obj = {};
    keys.forEach(key => {
      obj[key] = _get(props.modelValue, ['_fastjoin', 'files', 'files', key], {
        ...props.modelValue[key],
        name: 'Optimizing upload'
      });
    })
    return obj;
  });

  const {
    setKey,
    finishKey
  } = objKeyEdit(computed(() => props.modelValue), { emit: (val) => emit('update:model-value', val) })

  const uploadEmit = (evt, args) => {
    const obj = {
      'update:model-value': () => {
        const len = Object.keys(props.modelValue || {}).length || 0;
        const key = `Untitled_File${len ? len + 1 : ''}`
        let obj = { ...props.modelValue };
        if(obj) obj[key] = args
        else obj = { [key]: args }
        emit('update:model-value', obj);
      }
    };
    if(obj[evt]) obj[evt]()
    else console.log('no evt handler for event ', evt);
  };

  const removeFile = (path) => {
    const obj = { ...props.modelValue };
    delete obj[path];
    emit('update:model-value', obj);
  }

  const upload = storjUpload(undefined, { emit: uploadEmit })

</script>

<style lang="scss" scoped>

</style>
