import {extFromMime} from 'src/utils/fs-utils/mime-types';
import {ref} from 'vue';
import {allowedVideoPlatforms} from 'components/common/uploads/video/utils/index';

const timeout = ref(false);
export const downloadFile = async (val:any) => {
    if (!timeout.value) {
        const res = await fetch(val.url, { method: 'GET', headers: { 'Content-Type': val.info.type } })
        const blob = await res.blob();
        const newBlob = new Blob([blob]);

        const blobUrl = window.URL.createObjectURL(newBlob);
        const link = document.createElement('a');
        link.href = blobUrl;
        const name = (val.info?.name || val.originalname || '');
        const splitName = name.split('.')[0]
        link.setAttribute('download', `${name}${name === splitName ? '.' + extFromMime()[val.info?.type] : ''}`);
        document.body.appendChild(link);
        link.click();
        link.parentNode?.removeChild(link);

        // clean up Url
        window.URL.revokeObjectURL(blobUrl);
    } else {
        timeout.value = true;
        setTimeout(() => {
            timeout.value = false
        }, 5000);
    }
}

export const openFile = (val:any, router:any) => {
    const { href } = router.resolve({ name: 'file-preview', params: { id: val.uploadId || val._id } });
    window.open(href, '_blank')
}

export const getFileType = (file:any, url:string) => {
    const type = file?.info?.type || file?.type;
    if (type) {
        if(type?.indexOf('image') > -1) return 'image';
        else if(type.indexOf('video') > -1) return 'video';
        else if (type.indexOf('pdf') > -1) return 'pdf';
        else if (type.indexOf('xls') > -1) return 'xls';
        else if (type.indexOf('sheet') > -1) return 'xls';
        else if (type.indexOf('csv') > -1) return 'csv';
        else if (type.indexOf('txt') > -1) return 'txt';
        else return 'other';
    } else if (typeof url === 'string') return url.includes('embed') || Object.keys(allowedVideoPlatforms).some(a => url.includes(a)) ? 'video' : 'image';
    else return undefined;
}
