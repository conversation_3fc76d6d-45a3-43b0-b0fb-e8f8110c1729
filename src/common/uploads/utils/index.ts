import {computed, ComputedRef, Ref, ref} from 'vue';
import { dollarString } from 'src/utils/global-methods';

type AnyObj = { [key:string]: any}

export const fileScale = computed(() => {
    return {
        names: {
            'b': 1,
            'kb': 1000,
            'mb': 1000000,
            'gb': 1000000000,
            'tb': 1000000000000,
            'pb': 1000000000000000
        },
        indexes: {
            0: 1,
            1: 1000,
            2: 1000000,
            3: 1000000000,
            4: 1000000000000,
            5: 1000000000000000
        },
        indexByKey: {
            'b': 0,
            'kb': 1,
            'mb': 2,
            'gb': 3,
            'tb': 4,
            'pb': 5
        }
    }
});

export const bytes = (bytes: number, dec = 1) => {
    if (bytes < 1024) return `${bytes} B`
    else if (bytes < 1048576) return `${dollarString(bytes / 1024, '', dec)} KB`
    else if (bytes < 1073741824) return `${dollarString(bytes / 1048576, '', dec)} MB`
    else if (bytes < 1099511627776) return `${dollarString(bytes / 1073741824, '', dec)} GB`
    else if (bytes < 1125899906842624) return `${dollarString(bytes / 1099511627776, '', dec)} TB`
    else return 'PB'
}

export const objKeyEdit = (obj:Ref<AnyObj>|ComputedRef<AnyObj>, options?:{ emit?:(args:any) => void}) => {
    const fluxKeys:Ref<AnyObj> = ref({});
    const setKey = (key:string, newKey:string) => {
        fluxKeys.value[key] = newKey;
    };
    const finishKey = (key:string) => {
        const newVal = fluxKeys.value[key];
        if (newVal && newVal !== key) {
            const newObj = Object.assign({}, obj.value);
            delete newObj[key];
            newObj[fluxKeys.value[key]] = obj.value[key];
            if(options?.emit) options.emit(newObj);
            fluxKeys.value = {};
        }
    }
    return { fluxKeys, setKey, finishKey }
}
