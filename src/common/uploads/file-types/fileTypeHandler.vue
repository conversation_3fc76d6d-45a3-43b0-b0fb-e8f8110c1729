<template>
  <div v-bind="{ class: '_fw flex flex-center', ...divAttrs }">
    <template v-if="fileType === 'image'">
      <img  :id="`${elementId}_img`" class="__fileImg" :src="src" :style="{height, width, ...imgStyle, display: src ? undefined : 'none'}">
    </template>
    <template v-else-if="fileType === 'video'">
      <q-video
          class="_fa"
          v-bind="{
            ratio,
            src,
            ...videoAttrs
          }"
      ></q-video>
    </template>
    <template v-else>
      <q-icon v-bind="{ size: '30px', ...getIcon, ...iconAttrs }"></q-icon>
    </template>

  </div>
</template>

<script>
  import { getFileType } from 'components/common/uploads/utils/files';

  export default {
    name: 'fileTypeHandler',
    props: {
      divAttrs: Object,
      imgStyle: Object,
      videoAttrs: Object,
      ratio: { type: String, default: '1.7777' },
      height: { type: String },
      width: { type: String },
      file: { required: false },
      url: { type: String },
      elementId: { type: String, default: 'file-type-comp' },
      iconAttrs: Object
    },
    computed: {
      src(){
        if(this.url) return this.url
        if(this.file){
          if(Array.isArray(this.file)) return this.file;
          if(typeof this.file === 'object') return this.file.url || this.file;
          return this.file;
        }
        return undefined
      },
      fileType() {
       return getFileType(this.file, this.url);
      },
      type(){
        return this.file?.info?.type || this.file?.type;
      },
      icons() {
        return {
          pdf: {
            color: 'red',
            name: 'mdi-file-pdf-box'
          },
          csv: {
            color: 'ir-green-10',
            name: 'mdi-file-delimited'
          },
          xls: {
            color: 'green',
            name: 'mdi-file-excel'
          },
          png: {
            color: 'blue',
            name: 'mdi-file-image'
          },
          svg: {
            color: 'purple',
            name: 'mdi-svg'
          },
          default: {
            color: 'ir-blue-grey-6',
            name: 'mdi-file'
          }
        }
      },
      getIcon() {
        if (this.fileType && this.icons[this.fileType]) return this.icons[this.fileType];
        else return this.icons['default'];
      }
    }
  };
</script>

<style scoped>
  .__fileImg {
    height: 100%;
    width: 100%;
    overflow: hidden;
    object-fit: cover;
  }
</style>
