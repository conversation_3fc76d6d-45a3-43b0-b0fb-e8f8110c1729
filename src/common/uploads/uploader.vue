<template>
  <div
    @pointerenter="hover = true"
    @pointerleave="hover = false"
    v-bind="{
    id: id,
    class: '__uploader',
    style: { cursor: 'pointer', position: 'relative', height: height, width: width, maxWidth: '100%' },
    ...divAttrs
  }"
    @drop="dropHandler($event)"
  >
    <div class="__cover flex flex-center" :style="display && !hover ? 'top: 100%; opacity: 0;' : !hover ? ' background: transparent; color: dark' : ''">
      <div class="__upload_text">
        <p v-if="!displayText && !errorText">🗄 Upload Image</p>
        <p v-else-if="!!displayText">{{displayText}}</p>
        <p v-else style="color: #b80000">{{errorText}}</p>
      </div>
    </div>
    <div v-if="loading" class="__loader">
      <q-circular-progress
        size="30px"
        color="primary"
        :indeterminate="!progress"
        :modelValue="progress"
      ></q-circular-progress>
    </div>
    <slot name="display">
      <file-type-handler :file="raw" :url="display"></file-type-handler>
    </slot>
    <slot name="input">
      <input @change="dropHandler($event)" type="file"
             style="opacity: 0; position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
    </slot>
  </div>
</template>

<script>
  import FileTypeHandler from './file-types/fileTypeHandler.vue';

  export default {
    name: 'uploader',
    components: { FileTypeHandler },
    props: {
      db: { type: String },
      storageConfig: { required: false },
      allowTypes: {
        default: () => {
          return ['image'];
        }
      },
      log: Boolean,
      smSize: { type: Number, default: 200 },
      id: { type: String, default: 'uploader_drop' },
      height: { type: String, default: '150px' },
      width: { type: String, default: '150px' },
    },
    mounted() {
      let el = document.getElementById(this.id);
      if (el) {
        el.addEventListener('dragover', evt => {
          evt.preventDefault();
        });
      }
    },
    data() {
      return {
        progress: 0,
        hover: false,
        loading: false,
        raw: undefined,
        sm: undefined,
        display: undefined,
        displayText: undefined,
        errorText: undefined
      };
    },
    computed: {
      divAttrs() {
        return this.$attrs.divAttrs;
      },
      attrs() {
        return this.$attrs;
      },
      typeChecks(){
        return this.allowTypes.map(a => `/${a}/`);
      }
    },
    methods: {
      dataURLToBlob(dataURL) {
        if (this.log) console.log('datarultoblob');
        let BASE64_MARKER = ';base64,';
        if (dataURL.indexOf(BASE64_MARKER) === -1) {
          let parts = dataURL.split(',');
          let contentType = parts[0].split(':')[1];
          let raw = parts[1];

          return new Blob([raw], { type: contentType });
        }

        let parts = dataURL.split(BASE64_MARKER);
        let contentType = parts[0].split(':')[1];
        let raw = window.atob(parts[1]);
        let rawLength = raw.length;

        let uInt8Array = new Uint8Array(rawLength);

        for (let i = 0; i < rawLength; ++i) {
          uInt8Array[i] = raw.charCodeAt(i);
        }

        return new Blob([uInt8Array], { type: contentType });
      },
      // for more info on this method https://stackoverflow.com/questions/23945494/use-html5-to-resize-an-image-before-upload
      handleImage(file) {

        let self = this;
        let log = this.log;
        this.raw = file;
        if (this.log) console.log('handling image');
        // Load the image
        let reader = new FileReader();
        reader.onload = function (readerEvent) {
          if (log) console.log('reader loaded');
          let image = document.createElement('img');
          if (log) console.log('image', image);
          image.onload = function (imageEvent) {
            if (log) console.log('image loaded');
            // Resize the image
            let canvas = document.createElement('canvas'),
                max_size = self.maxSize,
                width = image.width,
                height = image.height;
            if (width > height) {
              if (width > max_size) {
                height *= max_size / width;
                width = max_size;
              }
            } else {
              if (height > max_size) {
                width *= max_size / height;
                height = max_size;
              }
            }
            canvas.width = width;
            canvas.height = height;
            if(this.log) console.log('before draw', width, height);
            canvas.getContext('2d').drawImage(image, 0, 0, width, height);
            let dataUrl = canvas.toDataURL('image/jpeg');
            let resizedImage = self.dataURLToBlob(dataUrl);
            self.display = dataUrl;
            self.handleResize(resizedImage, file);
          };
          let src = readerEvent.target.result;
          image.src = src;
        };
        reader.readAsDataURL(file);

      },
      handleResize(sm, file) {
        if (this.log) console.log('handle resize', sm);
        this.sm = sm;
        this.loading = false;
        this.handleUploads();
      },
      handleUploads(){
        let fileObj = { raw: this.raw, sm: this.sm };
        this.storageConfig.method(this, fileObj);
      },
      handleAllTypes(file){
        const runTypeMethods = type => {
          if(type.indexOf('image') > -1){
            this.handleImage(file);
          }
        };

        if(this.log) console.log('handling type');
        if(file) {
          let type = file.type;
          let truths = this.allowTypes.filter(a => {
            return type.indexOf(a) > -1;
          });
          if(this.log) console.log('truth filter', type, this.typeChecks, truths);
          if(!truths.length){
            let typeString = '';
            this.allowTypes.forEach(t => typeString += `${t}, `);
            this.errorText = `Supported types: ${typeString.substring(0, typeString.length - 2)}`;
          } else runTypeMethods(type);
        }
      },
      dropHandler(ev) {
        this.loading = true;
        // setTimeout(() => {
        //   this.loading = false;
        // }, 10000);
        if(this.log) console.log('File(s) dropped', ev);

        // Prevent default behavior (Prevent file from being opened)
        ev.preventDefault();

        if (ev.dataTransfer && ev.dataTransfer.items) {
          // Use DataTransferItemList interface to access the file(s)
          for (let i = 0; i < ev.dataTransfer.items.length; i++) {
            // If dropped items aren't files, reject them
            if (ev.dataTransfer.items[i].kind === 'file') {
              let file = ev.dataTransfer.items[i].getAsFile();
              if(this.log) console.log('... file[' + i + '].name = ' + file.name);
              this.handleAllTypes(file);
            }
          }
        } else {
          // Use DataTransfer interface to access the file(s)
          let fileList = ev.target.files || ev.dataTransfer.files;
          for (let i = 0; i < fileList.length; i++) {
            let file = fileList[i];
            if (this.log) console.log('file', file);
            this.handleAllTypes(file);
          }
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .__uploader {
    width: 150px;
    height: 150px;
    overflow: hidden;
    border-radius: 5px;
    //box-shadow: 1px 1px 6px rgba(0, 0, 0, .14);
  }
  .__loader {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
  }
  .__cover {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(229, 229, 229, 0.81);
    transition: all .2s ease-out;
    pointer-events: none;
  }

  .__upload_text {
    font-size: 16px;
    font-weight: 500;
    width: 100%;
    text-align: center;
  }
</style>
