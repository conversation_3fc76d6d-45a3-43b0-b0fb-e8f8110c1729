import {_get} from 'symbol-syntax-utils';
import { localUuid, defEmit, UploadOptions, axiosFeathers } from './utils';

export const gcUpload = (existing:Array<any>, options?: UploadOptions) => {
    return async (file:any) => {
        // upload({ fieldName, file, metadata, load, error, progress, abort, transfer, options }) {
        const emit = options?.emit || defEmit;
        emit('uploading');
        let files: any[] = [];
        if(options?.log) console.log('upload', file, file[0].raw);
        const fl = Array.isArray(file) ? file.map(a => a.raw) : [file.raw];
        if(options?.log) console.log('upload fl', fl);
        Promise.all(fl.map(f => {
            const payload = new FormData();
            payload.append('name', _get(f, 'name', localUuid()) as string);
            payload.append('storage', 'google-cloud');
            payload.append('file', f);
            // console.log('f', f);
            const info = {
                name: f.name,
                size: f.size,
                type: f.type,
                lastModifiedDate: f.lastModifiedDate
            };
            payload.set('info', JSON.stringify(info));
            return axiosFeathers().post('/uploads', payload)
                .then(res => {
                    let val;
                    if (Array.isArray(res.data)) val = res.data.map(a => {
                        const {_id, url, fileId, storage} = a;
                        return {_id, url, fileId, storage};
                    });
                    else {
                        const {_id, url, fileId, storage} = res.data;
                        val = {_id, url, fileId, storage};
                    }
                    if(options?.log) console.log('upload response', res);
                    if (options?.multiple) {
                        if (!Array.isArray(val)) val = [val];
                        if (existing && Array.isArray(existing)) {
                            existing.forEach(file => {
                                val.push(file);
                            });
                        }
                        files = val;
                    } else {
                        // console.log('emit input', val);
                        files ? files.push(val) : files = [val];
                    }
                })
                .catch(err => {
                    console.error('image upload error', err);
                });
        }))
            .then(() => {
                emit('done');
                // console.log('promise.all res', res);
                const getEmitVl = (fl:any) => {
                    const {_id, fileId, storage, appPath, subPath, url} = fl;
                    return {_id, fileId, storage, appPath, subPath, url};
                };
                const getDisplay = (v:any) => {
                    return _get(v, '_fastjoin.files.file');
                };
                if (options?.multiple) {
                    emit('update:model-value', files.map(a => getEmitVl(a)));
                    emit('update:display', files.map(a => getDisplay(a)));
                } else {
                    // console.log('looping', files);
                    files.forEach(f => {
                        emit('update:model-value', getEmitVl(f));
                        emit('update:display', getDisplay(f));
                    });
                }
            })
            .catch(err => {
                emit('done');
                console.log('promise.all error', err);
            });

        // }
    };
};
