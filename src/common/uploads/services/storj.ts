import {_get} from 'symbol-syntax-utils';
import { localUuid, defEmit, UploadOptions, axiosFeathers, restCore } from './utils';

export const getFileSchema = (fl:any) => {
    const {_id, fileId, info, storage, appPath, subPath, url} = fl;
    return {uploadId: _id, info, fileId, storage, appPath, subPath, url};
}

export const storjUpload = (existing:any, options?: UploadOptions) => {
    return async (file:any) => {
        // upload({ fieldName, file, metadata, load, error, progress, abort, transfer, options }) {
        const emit = options?.emit || defEmit;
        emit('uploading', true);
        let files: any[] = [];
        if(options?.log) console.log('upload', file);
        const fl = Array.isArray(file) ? file : [file];
        if(options?.log) console.log('upload fl', fl);
        Promise.all(fl.map(f => {
            const payload = new FormData();
            const query:any = {
                name: _get(f, 'name', localUuid()) as string,
                storage: 'storj'
            }
            if(options?.expires) query.expires = String(options.expires)
            const fileToUpload = f[options?.uploadPath || 'sm'];
            // Ensure the file blob has the correct type
            if (fileToUpload instanceof Blob && !fileToUpload.type && f.raw?.type) {
                const typedBlob = new Blob([fileToUpload], { type: f.raw.type });
                payload.append('file', typedBlob, f.raw?.name || 'file');
            } else {
                payload.append('file', fileToUpload);
            }
            if(options?.log) console.log('file', f);
            const info = {
                name: f.raw?.name || 'Untitled',
                size: f[options?.uploadPath || 'sm'].size,
                type: f[options?.uploadPath || 'sm'].type,
                lastModifiedDate: f.raw?.lastModifiedDate || new Date()
            };
            query.info = info;
            payload.set('info', JSON.stringify(info));
            if(options?.log) console.log('before upload call', payload)
            return axiosFeathers().post('/uploads', payload, { params: { core: restCore(), ...query }})
                .then(res => {
                    let val;
                    if (Array.isArray(res.data)) val = res.data.map(a => {
                        const {_id, url, fileId, info, storage} = a;
                        return {_id, url, fileId, info, storage};
                    });
                    else {
                        const {_id, url, fileId, appPath, subPath, info, storage} = res.data;
                        val = {_id, url, fileId, appPath, subPath, info, storage};
                    }
                    if(options?.log) console.log('upload response', res);
                    if (options?.multiple) {
                        if (!Array.isArray(val)) val = [val];
                        // console.log('multiple indeed', existing, val)
                        if (existing && Array.isArray(existing)) {
                            files = [...existing.filter(a => !files.map(b => b.fileId).includes(a.fileId)), ...(files || []), ...val];
                        } else files = val;
                    } else {
                        // console.log('emit input', val);
                        if(files)files.push(val)
                        else files = [val];
                    }
                })
                .catch(err => {
                    console.error('image upload error', err);
                    emit('uploading', false);
                });
        }))
            .then(() => {
                emit('done');
                // console.log('promise.all res', res);

                const getDisplay = (v:any) => {
                    return _get(v, '_fastjoin.files.file');
                };
                const allFiles = files.map(a => getFileSchema(a))
                if (options?.multiple) {
                    emit('update:model-value', allFiles);
                    emit('update:display', files.map(a => getDisplay(a)));
                } else {
                    // console.log('looping', files);
                    files.forEach(f => {
                        emit('update:model-value', getFileSchema(f));
                        emit('update:display', getDisplay(f));
                    });
                }
                return allFiles
            })
            .catch(err => {
                emit('uploading', false);
                emit('done');
                console.log('promise.all error', err);
            });
        emit('uploading', false);

        // }
    };
};
