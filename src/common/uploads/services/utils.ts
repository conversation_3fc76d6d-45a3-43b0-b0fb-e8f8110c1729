import {LocalStorage, SessionStorage} from 'symbol-auth-client';
import axios from 'axios';
import { feathersUrl } from 'src/api/feathers-connect';

export {localUuid} from 'src/utils/global-methods';
export const defEmit = (evt: string, args: any) => console.log('No emit passed for event', evt, args);
export type UploadOptions = {
    multiple?: boolean,
    uploadPath?: string,
    name?:string,
    expires?:number, //expiry in seconds for url
    getCreatedBy?: () => string,
        // login?: string,
        // fingerprint?: string,
        // origin?: string,
        // longtail?: string
    emit?: (evt: string, args?: any) => void,
    log?: boolean
};

//TODO: process.env

export const uploadUrl = feathersUrl();

export const axiosFeathers = () => {
    const token = SessionStorage.getItem('client_ucan');
    return axios.create({
        baseURL: uploadUrl,
        headers: {
            ContentType: 'application/x-www-form-urlencoded',
            Accept: 'application/json',
            Authorization: 'Bearer ' + token
        }
    });
};

export const restCore = () => {
    return {
        fp: LocalStorage.getItem('fpId'),
        client_ucan: SessionStorage.getItem('client_ucan'),
        ucan_aud: LocalStorage.getItem('ucan_aud'),
        ltail: window.location.href
    }
}
