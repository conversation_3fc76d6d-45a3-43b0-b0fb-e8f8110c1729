import {_get} from 'symbol-syntax-utils';
import { localUuid, defEmit, UploadOptions, axiosFeathers } from './utils';

export const web3Upload = (existing:Array<any>, options?: UploadOptions) => {
    return async (file:any) => {
        // upload({ fieldName, file, metadata, load, error, progress, abort, transfer, options }) {
        console.log('web 3 upload', file);
        const emit = options?.emit || defEmit;
        emit('uploading');
        let files: any[] = [];
        console.log('upload', file, file[0].raw, file[0].sm);
        //TODO: add workflow for raw file
        const fl = Array.isArray(file) ? file.map(a => {
            const obj = { ...a.raw };
            obj.size = a.sm.size;
            obj.blob = a.sm;
            return {
                size: a.sm.size,
                blob: a.sm,
                type: a.raw.type,
                lastModifiedDate: a.raw.lastModifiedDate,
                name: a.raw.name
            }
        }) : [{...file.raw, size: file.sm.size, blob: file.sm.blob}];
        console.log('upload fl', fl);
        Promise.all(fl.map(f => {
            console.log('ffffff', f, typeof f.blob);
            const payload = new FormData();
            payload.append('name', f.name || localUuid());
            payload.append('storage', 'web3');
            payload.append('file', f.blob, `${f.name}`);
            // console.log('f', f);
            const info = {
                name: f.name,
                size: f.blob.size,
                type: f.type,
                lastModifiedDate: f.lastModifiedDate
            };
            payload.append('info', JSON.stringify(info));
            console.log('axios feathers call', payload.get('file'), info);
            const createdBy = options?.getCreatedBy ? options.getCreatedBy() : ''
            payload.append('createdBy', createdBy );
            return axiosFeathers().post('/uploads', payload)
                .then(res => {
                    let val;
                    if (Array.isArray(res.data)) val = res.data.map(a => {
                        const {_id, url, fileId, storage} = a;
                        return {_id, url, fileId, storage};
                    });
                    else {
                        const {_id, url, fileId, storage} = res.data;
                        val = {_id, url, fileId, storage};
                    }
                    // console.log('upload response', res);
                    if (options?.multiple) {
                        if (!Array.isArray(val)) val = [val];
                        if (existing && Array.isArray(existing)) {
                            existing.forEach(file => {
                                val.push(file);
                            });
                        }
                        files = val;
                    } else {
                        // console.log('emit input', val);
                        files ? files.push(val) : files = [val];
                    }
                })
                .catch(err => {
                    console.error('image upload error', err);
                });
        }))
            .then(() => {
                emit('done');
                // console.log('promise.all res', res);
                const getEmitVl = (fl:any) => {
                    const {_id, fileId, storage, appPath, subPath, url} = fl;
                    return {_id, fileId, storage, appPath, subPath, url};
                };
                const getDisplay = (v:any) => {
                    return _get(v, '_fastjoin.files.file');
                };
                if (options?.multiple) {
                    emit('update:model-value', files.map(a => getEmitVl(a)));
                    emit('update:display', files.map(a => getDisplay(a)));
                } else {
                    // console.log('looping', files);
                    files.forEach(f => {
                        emit('update:model-value', getEmitVl(f));
                        emit('update:display', getDisplay(f));
                    });
                }
            })
            .catch(err => {
                emit('done');
                console.log('promise.all error', err);
            });

        // }
    };
};
