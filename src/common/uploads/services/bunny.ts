import {_get} from 'symbol-syntax-utils';
import {localUuid, defEmit, UploadOptions, axiosFeathers, restCore} from './utils';

export const bunnyUpload = async (file: any, options?: UploadOptions) => {
    // upload({ fieldName, file, metadata, load, error, progress, abort, transfer, options }) {
    const emit = options?.emit || defEmit;
    emit('uploading');
    if (options?.log) console.log('upload', file);
    const payload = new FormData();
    const name = _get(file, 'name', localUuid()) as string;
    payload.append('file', file);
    if (options?.log) console.log('file', file);
    const info = {
        name: options?.name || file.name,
        size: file.size,
        type: file.type,
        lastModifiedDate: file.lastModifiedDate
    };
    payload.set('info', JSON.stringify(info));
    if (options?.log) console.log('before upload call', payload)
    return axiosFeathers().post('/uploads', payload, {params: {core: restCore(), info, storage: 'bunny', name}})
        .then(res => {
            console.log('response after upload', res);
            if (options?.log) console.log('upload response', res);
            const {_id, fileId, storage, appPath, subPath, info, url} = res.data;
            const payload = {uploadId: _id, info, fileId, storage, appPath, subPath, url};
            emit('update:model-value', payload);
            return payload;
        })
        .catch(err => {
            emit('done');
            console.log('video upload error', err);
        });

};
