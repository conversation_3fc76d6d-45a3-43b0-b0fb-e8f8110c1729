import {splitUrl} from 'src/utils/domain-handler';

export const allowedVideoPlatforms = {
    'YouTube': {
        domain: 'youtube.com',
        oembed: (url: string) => `https://www.youtube.com/oembed?format=json&url=${encodeURIComponent(url)}`
    },
    'YouTube Unlisted': {
        domain: 'youtu.be',
        oembed: (url: string) => `https://www.youtube.com/oembed?format=json&url=${encodeURIComponent(url)}`
    },
    'Vimeo': {
        domain: 'vimeo.com',
        oembed: (url: string) => `https://vimeo.com/api/oembed.json?url=${encodeURIComponent(url)}`
    },
    'Loom': {
        domain: 'loom.com',
        oembed: (url: string) => `https://www.loom.com/v1/oembed?url=${encodeURIComponent(url)}`
    },
    // 'Facebook': {
    //     domain: 'facebook.com',
    //     oembed: (url:string) => `https://www.facebook.com/plugins/video/oembed.json/?url=${encodeURIComponent(url)}`
    // },
    // 'Instagram': {
    //     domain: 'instagram.com',
    //     oembed: (url:string) => `https://graph.facebook.com/v12.0/instagram_oembed?url=${encodeURIComponent(url)}`
    // },
    'Wistia': {
        domain: 'wistia.com',
        oembed: (url: string) => `https://fast.wistia.com/oembed?url=${encodeURIComponent(url)}`
    },
    // 'Vidyard': {
    //     domain: 'vidyard.com',
    //     oembed: (url: string) => `https://api.vidyard.com/dashboard/v1.1/oembed.json?url=${encodeURIComponent(url)}`,
    //     extract: (res:any) => {
    //         if(!res?.html) return '';
    //         const tempDiv = document.createElement('div');
    //         tempDiv.innerHTML = res.html;
    //
    //         // Find the img tag with the Vidyard embed data
    //         const vidyardImg = tempDiv.querySelector('.vidyard-player-embed');
    //
    //         if (vidyardImg) {
    //             const videoUUID = vidyardImg.getAttribute('data-uuid');
    //             if (videoUUID) {
    //                 return `https://play.vidyard.com/${videoUUID}.html`;
    //             }
    //         }
    //         return '';
    //     }
    // },
    // 'SproutVideo': {
    //     domain: 'sproutvideo.com',
    //     oembed: (url: string) => `https://sproutvideo.com/oembed.json?url=${encodeURIComponent(url)}`
    // }
}

export const handleVideoUrl = async (url: string) => {
    const parsed = splitUrl(url);

    let platformKey;
    for (const k in allowedVideoPlatforms) {
        if (parsed.domain.includes(allowedVideoPlatforms[k].domain)) {
            platformKey = k;
            break;
        }
    }
    if (!platformKey) return {url: '', code: 404, message: 'Unsupported video hosting platform'}
    const embedUrl = allowedVideoPlatforms[platformKey].oembed(url)
    let res: any = {}
    try {
        res = await fetch(embedUrl)
    } catch (error) {
        return {url: '', code: error.code, message: error.message};
    }
    const data = await res.json();
    let src;
    if(allowedVideoPlatforms[platformKey].extract) src = allowedVideoPlatforms[platformKey].extract(data);
    else {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = data.html;
        const iframe: any = tempDiv.querySelector('iframe');
        if (!iframe?.src) return {
            url: '',
            code: 400,
            message: `Unsupported url - platform ${parsed.domain} did not return a valid response for this url`
        }
        src = iframe.src
    }
    if(!src) return { url: '', code: 400, message: `Unsupported url according to ${platformKey}` };
    return {
        code: 200,
        data,
        status: res.status,
        url: encodeURI(src.split('?')[0])
    }

}
