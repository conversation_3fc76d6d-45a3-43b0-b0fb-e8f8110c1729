<template>
  <div
      @pointerenter="hover = true"
      @pointerleave="hover = false"
      class="__video-uploader"
  >
    <div class="__upload_text">
      <slot name="text" :displayText="displayText" :errorText="errorText">
        <slot name="display" :displayText="displayText" :errorText="errorText">
          <span v-if="!!displayText">{{ displayText }}</span>
          <span v-else-if="!!errorText" style="color: #b80000">{{ errorText }}</span>
        </slot>
      </slot>
    </div>
    <div v-if="loading" class="__loader">
      <q-circular-progress
          size="30px"
          color="primary"
          :indeterminate="!progress"
          :modelValue="progress"
      ></q-circular-progress>
    </div>
    <div :class="`__over ${(!raw && !displayUrl) || hover ? '' : '__off'}`">

      <div class="_fa flex flex-center">
        <q-chip clickable color="transparent" text-color="white" class="tw-six">
          <q-icon name="mdi-cloud-upload" color="white"></q-icon>
          <span class="q-ml-sm">Upload Video</span>
        </q-chip>
      </div>

    </div>
    <div :class="`__over ${removing ? '' : '__off'}`">
      <div class="_fa flex flex-center">
        <div>
          <div class="text-white text-center tw-five">Remove Video?</div>
          <div class="row justify-center">
            <q-btn @click="remove()" no-caps flat color="white" label="Cancel" icon-right="mdi-close"
                   class="tw-six q-mx-sm"></q-btn>
            <q-btn @click="remove(true)" no-caps push color="ir-red-4" label="Yes" icon-right="mdi-delete"
                   class="tw-six q-mx-sm"></q-btn>
          </div>
        </div>
      </div>
    </div>
    <div class="t-r" style="z-index: 100">
      <q-btn dense flat color="white" icon="mdi-dots-vertical">
        <q-popup-proxy>
          <div class="w200 mw100 bg-white br5">
            <q-list separator>
              <q-item clickable @click="remove()">
                <q-item-section>
                  <q-item-label>Remove Video</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-popup-proxy>
      </q-btn>
    </div>
    <template v-if="!url">
      <file-type-handler
          :ratio="ratio"
          :file="raw"
          :url="useUrl"
      ></file-type-handler>
    </template>
    <template v-else>
      <div class="_fa flex flex-center">
        <div class="font-1r tw-six">Video Processing</div>
      </div>
    </template>
    <input
        v-if="!removing"
        @change="dropFile($event)"
        type="file"
        style="opacity: 0; position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 2"
        :accept="allowTypes"
    >
    <q-slide-transition>
      <div class="q-px-sm __input" v-if="raw && !url">
        <q-input label="Video Title" :model-value="vidTitle" @update:model-value="title = $event"></q-input>
        <div class="row justify-end q-pt-sm">
          <q-btn label="Upload" icon-right="mdi-upload" flat color="primary" @click="startUpload"></q-btn>
        </div>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import FileTypeHandler from '../file-types/fileTypeHandler.vue';

  import {computed, ref} from 'vue';
  import {uploadFiles} from '../index';
  import {bunnyUpload} from '../services/bunny'

  const emit = defineEmits(['uploading', 'done', 'remove', 'update:model-value']);
  const props = defineProps({
    ratio: { default: '1.777' },
    log: Boolean,
    id: { type: String, default: 'video-form' },
    allowTypes: { default: 'video/mp4,video/x-m4v,video/*' },
    storage: { default: 'bunny' },
    displayUrl: { required: false },
  })

  const title = ref('');
  const hover = ref(false);

  const vidTitle = computed(() => {
    return title.value || raw.value?.name
  })

  const url = ref('');
  const removing = ref(false);
  const useUrl = computed(() => url.value || props.displayUrl)

  const remove = (val) => {
    removing.value = !removing.value;
    if (val) emit('remove')
  }

  const configs = computed(() => {
    return {
      'bunny': {
        upload: bunnyUpload
      }
    }
  })

  const emitWrap = () => {
    return (evt, args) => {
      const fns = {
        'done': () => {
          loading.value = false;
          emit('done');
        },
        'update:model-value': () => {
          console.log('emitting file', args);
          loading.value = false;
          emit('update:model-value', args);
        }
      };

      fns[evt] ? fns[evt](args) : emit(evt, args);
    }
  }

  const startUpload = async () => {
    loading.value = true;
    const res = await configs.value[props.storage].upload(raw.value, {
      log: props.log,
      name: title.value,
      emit: emitWrap()
    })
    url.value = res.url;
  }

  const { dropHandler, displayText, errorText, progress, loading, raw } = uploadFiles({
    log: props.log,
    smSize: props.smSize,
    allowTypes: Array.isArray(props.allowTypes) ? props.allowTypes : (props.allowTypes || 'image').split(' ').join('').split(','),
    maxSize: props.maxSize
  });

  const dropFile = (ev) => {
    url.value = '';
    dropHandler(ev);
  }


</script>

<style lang="scss" scoped>
  .__video-uploader {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: inherit;
  }

  .__upload_text {
    text-align: center;
    display: block;
  }

  .__loader {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 4;
  }

  .__input {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, .8);
    z-index: 3;
  }

  .__over {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
    border-radius: inherit;
    background: rgba(99, 99, 99, .9);
    transition: all .2s;
  }

  .__off {
    height: 0;
    opacity: 0;
  }


</style>
