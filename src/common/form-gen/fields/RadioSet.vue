<template>
  <div>
    <div :class="titleClass">{{$attrs.title||''}}</div>
    <q-radio
        v-for="(opt, i) in $attrs.options||''" :key="`opt-${idx}-${i}`"
        :model-value="modelValue"
        @update:model-value="$emit('update:model-value', $event)"
        v-bind="opt"
    ></q-radio>
  </div>
</template>

<script setup>

  const props = defineProps({
    titleClass: { type: String, default: 'font-7-8r text-weight-bold' },
    modelValue: { required: true },
    idx: { default: 0 }
  })
</script>

<style lang="scss" scoped>

</style>
