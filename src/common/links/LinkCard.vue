<template>
  <div class="q-pa-sm">
    <div :class="titleClass">{{ title }}</div>

    <div class="__link_grid" @click.stop="copyLink">
      <q-btn class="q-mr-sm" color="primary" flat round icon="mdi-content-copy"/>
      <input style="border: none; width: 100%; color: darkblue" :value="url" :id="id">
    </div>

  </div>
</template>

<script>
  export default {
    name: 'LinkCard',
    props: {
      titleClass: { type: String, default: 'text-primary text-xs text-mb-xs text-weight-medium'},
      url: String,
      title: String,
      id: { default: 'link_chip' }
    },
    methods: {
      copyLink() {
        this.$emit('update:copy', this.url);
        this.$nextTick(() => {
          let text = document.getElementById(this.id);
          text.select();
          text.setSelectionRange(0, 99999);
          document.execCommand('copy');
          this.$q.notify({
            message: 'Link Copied to Clipboard',
            color: 'info',
            icon: 'mdi-content-copy'
          });
        });
      },
    }
  };
</script>

<style>
  .__link_grid {
    display: grid;
    width: 100%;
    height: 40px;
    grid-template-rows: 100%;
    grid-template-columns: auto 1fr;
  }
</style>
