<template>
  <q-btn v-bind="{ icon: 'mdi-dots-vertical', flat: true, dense: true, ...btnAttrs}" icon="mdi-dots-vertical" flat dense color="white" size="sm">
    <q-popup-proxy @update:model-value="toggleScreen">
      <div class="w400 mw100 bg-ir-bg text-ir-text">
        <q-tab-panels class="_panel" :model-value="confirm > -1" animated>
          <q-tab-panel :name="false" class="_panel">
            <q-list separator>
              <q-item v-for="(item, i) in items" :key="`item-${i}`" clickable @click="item.confirm ? confirm = i : item.on(subject)">
                <q-item-section avatar>
                  <q-icon v-bind="item.icon"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{item.label}}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>
          <q-tab-panel :name="true" class="_panel">
            <div class="font-7-8r tw-five q-pa-sm">{{items[confirm].confirm}}</div>
            <div class="q-pa-sm row justify-end">
              <q-btn @click="confirm = -1" flat no-caps>
                <span class="q-mr-xs tw-six">Cancel</span>
                <q-icon name="mdi-cancel" color="red"></q-icon>
              </q-btn>
              <q-btn @click="items[confirm].on(subject)" flat no-caps>
                <span class="q-mr-xs tw-six">Confirm</span>
                <q-icon name="mdi-check" color="green"></q-icon>
              </q-btn>
            </div>
          </q-tab-panel>
        </q-tab-panels>

      </div>
    </q-popup-proxy>
  </q-btn>
</template>

<script setup>

  import {ref} from 'vue';

  const props = defineProps({
    subject: Object,
    items: Array,
    btnAttrs: Object
  })

  const confirm = ref(-1)
  const toggleScreen = () => {
    confirm.value = -1
  }
</script>

<style lang="scss" scoped>

</style>
