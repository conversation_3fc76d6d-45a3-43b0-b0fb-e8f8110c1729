<template>
  <div class="__col">
    <div>
      <q-btn color="ir-grey-6" v-if="!bottom" size="sm" dense flat icon="mdi-menu-up"
             @click="$emit('up')">
        <q-tooltip>{{upMessage || 'Move Up'}}</q-tooltip>
      </q-btn>
    </div>
    <div>
      <q-btn color="ir-grey-6" @click="$emit('down')" v-if="!top" size="sm" dense flat
             icon="mdi-menu-down">
        <q-tooltip>{{downMessage || 'Move Down'}}</q-tooltip>
      </q-btn>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    top: Boolean,
    bottom: Boolean,
    upMessage: String,
    downMessage: String
  })
</script>

<style lang="scss" scoped>
  .__col {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
</style>
