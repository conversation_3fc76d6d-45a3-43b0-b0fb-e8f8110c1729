<template>
  <div class="__ht">
    <div v-for="(item, i) in tabs" :key="`${id}-${i}`" :class="`__tab ${modelValue === item.name ? '__active' : ''}`" @click="$emit('update:model-value', item.name)">
      <slot name="default" :item="item">
        {{item.label}}
      </slot>
    </div>
    <div class="__mask"></div>
  </div>
</template>

<script setup>
  import {ref} from 'vue';

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: String,
    tabs: Array
  })

  const id = ref(String(new Date().getTime()))

</script>

<style lang="scss" scoped>
  .__ht {
    padding: 3px;
    max-width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow-x: scroll;
    cursor:pointer;
    background: white;
    position: relative;
  }
  .__tab {
    background: #f6f6f6;
    //background: linear-gradient(12deg, #f5f5f5, #eee, #f5f5f5);
    padding: 10px 10px;
    transition: all .2s;
    border-radius: 5px 5px 0 0;

    &:hover {
      background: white;
    }
  }
  .__active {
    font-weight: 600;
    background: white;
    box-shadow: 0px 2px 6px -2px #9e9e9e;
    z-index: 2;
  }
  .__mask {
    position: absolute;
    z-index: 3;
    bottom: 0;
    left: 3px;
    right: 3px;
    height: 5px;
    background: white
  }
</style>
