<template>
  <div class="_fw">
    <div class="font-1r text-ir-mid tw-six q-pa-md">{{form._id ? 'Edit' : 'Add'}} Calendar</div>
    <div class="_form_grid">
      <div class="_form_label">Name</div>
      <div class="q-pa-sm">
        <q-input filled @update:model-value="autoSave('name')" v-model="form.name" dense></q-input>
      </div>
      <div class="_form_label">Schedule</div>
      <div class="q-pa-sm">
        <schedule-picker v-model="form.schedule" @update:model-value="autoSave('schedule')"></schedule-picker>
      </div>
    </div>
    <div class="q-pa-md">
      <q-btn v-if="!form._id" class="_pl_btn tw-six" no-caps push label="Save Calendar" @click="save"></q-btn>
      <q-btn v-else-if="Object.keys(patchObj).length" class="_pl_btn tw-six" no-caps push label="Save Changes" @click="update"></q-btn>
    </div>
  </div>
</template>

<script setup>
  import SchedulePicker from 'components/common/calendar/forms/SchedulePicker.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {computed, ref} from 'vue';
  import {useCalendars} from 'stores/calendars';
  import {$errNotify} from 'src/utils/global-methods';

  const calendarStore = useCalendars();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    ownerId: { required: true },
    ownerService: { required: true }
  })

  const { form, save } = HForm({
    store: calendarStore,
    value: computed(() => props.modelValue),
    validate: true,
    vOpts: ref({
      name: { name: 'Name', v: ['notEmpty'] },
      owner: { name: 'Owner', v: ['notEmpty'] },
      ownerService: { name: 'OwnerService', v: ['notEmpty'] },
    }),
    beforeFn: (val) => {
      return {
        owner: props.ownerId,
        ownerService: props.ownerService,
        ...val
      }
    },
    afterFn: (val) => {
      emit('update:modelValue', val);
    }
  })

  const { autoSave, patchObj } = HSave({
    store: calendarStore,
    form,
    save,
    pause: ref(true)
  })

  const update = () => {
    calendarStore.patch(form.value._id, patchObj.value)
        .catch(err => $errNotify(`Error saving changes: ${err.message}`))
  }
</script>

<style lang="scss" scoped>

</style>
