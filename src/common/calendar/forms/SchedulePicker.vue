<template>
  <div class="_fa">


    <div class="row items-end q-mb-md">
      <div class="col-12 q-pa-sm">

        <div class="q-pa-md" v-if="!hideBlackout">
          <div class="text-xxs text-mb-xxs text-weight-medium">
            <div class="flex items-center">
              <div>Unavailable Dates</div>
              <q-btn size="sm" dense :color="dark ? 'white' : 'dark'" push icon="mdi-plus" class="q-mx-lg"
                     @click="blackoutDialog = true">
                <q-dialog @close="addingBlackout = defaultBlackout()" v-model="blackoutDialog"
                          :maximized="$q.screen.lt.sm">
                  <q-card :dark="dark" class="q-pa-md" style="width: 800px; max-width: 100%">
                    <q-btn class="t-r-f bg-dark text-light" dense flat size="sm" icon="mdi-close"
                           @click="blackoutDialog = false"/>

                    <div class="text-xs text-mb-xs text-weight-bold q-mb-lg">Choose Date Range</div>

                    <inline-picker
                        require-save
                        :dark="dark"
                        v-model="addingBlackout"
                        @input="newBlackout"
                    ></inline-picker>

                  </q-card>
                </q-dialog>
              </q-btn>
            </div>
          </div>
          <div class="row items-center">
            <template v-for="(range, i) in blackoutList">
              <div class="flex items-center relative-position q-pa-sm" v-if="(form.blackoutDates || []).length"
                   :key="`date-${i}`">
                <q-chip
                    @click="editBlackout(range, i)"
                    icon="mdi-calendar"
                    dark
                    removable
                    @remove="form.blackoutDates.splice(i , 1)"
                    :label="range.recurrence ? $recurrenceString(range.recurrence) : formatDate(range.start, 'MMM-DD h:mm a') + ' - ' + formatDate(range.end, 'MMM-DD h:mm a')"
                >
                </q-chip>
              </div>
            </template>
          </div>
        </div>
      </div>

    </div>

    <div class="__days">
      <div class="__day" v-for="(day, i) in [0,1,2,3,4,5,6]" :key="daysoftheweek[i]">
        <div>{{ daysoftheweek[day].substring(0, 3) }}</div>
        <div class="__times">
          <div class="__time" v-for="(time, idx) in days[day].times?.length ? days[day].times : [{}]"
               :key="`time-${i}_${idx}`">
            <div>
              <q-input
                  dense
                  filled
                  :dark="dark"
                  :model-value="time.start ? displayHour(time.start) : undefined"
                  @update:model-value="pendingTime = $event"
                  @blur="addTime(day, idx, 'start', pendingTime)"
                  :error="!!errors[day][idx]"
                  :hide-bottom-space="!errors[day][idx]"
                  :error-message="errCodes[errors[day][idx]]"
                  no-error-icon
              >
              </q-input>

            </div>
            <div class="text-center _fw">-</div>
            <div>
              <q-input
                  dense
                  filled
                  :dark="dark"
                  :model-value="time.end ? displayHour(time.end) : undefined"
                  @update:model-value="pendingTime = $event"
                  @blur="addTime(day, idx, 'end', pendingTime)"
                  :error="!!errors[day][idx]"
                  :hide-bottom-space="!errors[day][idx]"
                  no-error-icon

              ></q-input>

            </div>
            <div>
              <q-btn @click="removeTime(day, idx)" dense flat size="sm" color="ir-mid" icon="mdi-cancel"></q-btn>
            </div>
          </div>
        </div>
        <div>
          <q-btn @click="newTimeSlot(day)" dense flat size="sm" color="ir-mid" icon="mdi-plus-thick"></q-btn>
        </div>
        <div>
          <q-btn dense flat size="sm" color="ir-mid" icon="mdi-content-copy">
            <q-popup-proxy>
              <div class="w200 bg-ir-bg1 text-ir-text">
                <q-list separator>
                  <q-item-label header>Copy From</q-item-label>
                  <q-item v-for="i in 7" :key="`daycopy-${i}`" clickable @click="copy(i-1, day)">
                    <q-item-section>
                      <q-item-label>{{ daysoftheweek[i - 1] }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-popup-proxy>
          </q-btn>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
  import InlinePicker from 'components/common/dates/InlinePicker.vue';
  import {computed, ref, watch} from 'vue';
  import {formatDate} from 'src/utils/date-utils';
  import { parseTime, displayHour } from '../utils';

  const defaultBlackout = () => {
    return { start: new Date(), end: new Date(), recurrence: undefined };
  };

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    dark: Boolean,
    modelValue: Object,
    hideBlackout: Boolean
  })

  const blackoutDialog = ref(false);
  const addingBlackout = ref(defaultBlackout());

  const formFn = (defs) => {
    return {
      days: {},
      blackoutDates: [],
      ...defs
    }
  }
  const form = ref(formFn())
  const genDay = (defs = {}) => {
    return {
      all: false,
      times: (defs.times || []).sort((a, b) => a.start - b.start),
      ...defs
    }
  }

  const days = computed(() => {
    const obj = { ...form.value.days }
    for (const i of [0, 1, 2, 3, 4, 5, 6]) {
      obj[i] = genDay(obj[i])
    }
    return obj;
  })

  const startMenu = ref(false);
  const endMenu = ref(false);


  const errCodes = {
    'u': 'Start after end time',
    'o': 'Time slots overlap'
  }
  const blankErrs = () => {
    const obj = {}
    for (let i = 0; i < 7; i++) {
      obj[i] = {}
    }
    return obj;
  }
  const errors = ref(blankErrs())
  const errState = ref(false);

  const checkTime = (day, rerun) => {
    startMenu.value = false;
    endMenu.value = false;
    const times = days.value[day]?.times || []
    let err;
    for (let i = 0; i < times.length; i++) {
      if (times[i].start >= times[i].end) {
        errors.value[day] = { [i]: 'u' }
        err = true;
      } else if (times[i + 1] && times[i].end > times[i + 1].start) {
        errors.value[day][i] = 'o'
        err = true
      } else errors.value[day][i] = undefined
    }
    console.log('first check', errState.value, err);
    if (!errState.value && !err) {
      emit('update:model-value', form.value);
      return err;
    } else if (err) {
      errState.value = true;
      return err
    } else if (!rerun) {
      for (const k in days.value) {
        const subErr = checkTime(k, true);
        if (subErr) err = true;
      }
      errState.value = err;
      console.log('second check', errState.value, err);
      if (!err) {
        errState.value = false;
        emit('update:model-value', form.value);
      }
    } else {
      errState.value = err;
      return err;
    }
  }

  const pendingTime = ref('')


  const addTime = (day, idx, path, time) => {
    pendingTime.value = '';
    if (!time) return;
    if (!form.value.days) form.value.days = { [day]: { times: [{ [path]: parseTime(time, path) }] } }
    else if (!form.value.days[day]) form.value.days[day] = { times: [{ [path]: parseTime(time, path) }] };
    else if (!form.value.days[day].times?.length) form.value.days[day].times = [{ [path]: parseTime(time, path) }];
    else form.value.days[day].times[idx] = { ...form.value.days[day].times[idx], [path]: parseTime(time, path) }
    startMenu.value = false;
    checkTime(day)
  }

  const removeTime = (day, idx) => {
    if ((form.value.days || {})[day]?.times) {
      form.value.days[day].times.splice(idx, 1);
      endMenu.value = false;
      startMenu.value = false;
      checkTime(day)
    }
  }

  const newTimeSlot = (day) => {
    if (days.value[day].times) {
      const last = days.value[day].times.slice(-1) || {};
      form.value.days[day].times.push({ start: last.end, end: Math.max(last.end || 1700, 1700) })
    } else {
      const def = { start: 900, end: 1700 }
      if (!form.value.days) form.value.days = { [day]: { times: [def] } }
      else if (!form.value.days[day]) form.value.days[day] = { times: [def] }
      else form.value.days[day].times = [def]
    }
  }

  const daysoftheweek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  const blackoutList = computed(() => [...form.value.blackoutDates || []].sort((a, b) => a - b))


  const copy = (from, to) => {
    const times = form.value.days[from]?.times || [];
    form.value.days[to] = { ...form.value.days[to], times }
  }
  const newBlackout = (val) => {
    const { start, end, recurrence } = val;
    if (!form.value.blackoutDates) form.value.blackoutDates = { start, end, recurrence }
    else form.value.blackoutDates.unshift({ start, end, recurrence })
    addingBlackout.value = defaultBlackout()
  }
  const editBlackout = (val) => {
    addingBlackout.value = Object.assign({}, val);
    blackoutDialog.value = true;
  }


  watch(() => props.modelValue, (nv) => {
    if (nv) {
      form.value = formFn(nv);
      [0, 1, 2, 3, 4, 5, 6].forEach(num => {
        if (form.value.days) form.value.days[num] = genDay(form.value.days[num]);
        else form.value.days = { [num]: genDay() }
      });
    }
  }, { immediate: true })

</script>

<style scoped lang="scss">
  .__days {
    font-family: var(--alt-font);
    width: 100%;
    overflow-x: scroll;
    display: grid;
    grid-template-rows: repeat(7, auto);
    grid-template-columns: 100%;
    grid-column-gap: 1px;
    background: var(--ir-light);

    .__day {
      background: var(--ir-bg);
      font-size: .8rem;
      display: grid;
      grid-template-columns: 40px 230px 30px 30px;
      align-items: start;

      > div {
        font-size: .8rem;
        font-weight: 500;
        padding-top: 18px;
      }

      .__times {
        padding-top: 0;

        .__time {
          min-height: 40px;
          padding: 8px 4px;
          display: grid;
          grid-template-columns: 90px 10px 90px 30px;
          align-items: center;
          justify-content: center;
          justify-items: center;
        }
      }
    }
  }

</style>
