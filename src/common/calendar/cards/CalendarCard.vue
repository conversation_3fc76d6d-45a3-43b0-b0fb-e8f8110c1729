<template>
  <div class="_fw">
    <div class="q-pa-sm tw-six font-1r">{{calendar.name || 'Untitled'}}</div>
    <q-separator></q-separator>
    <div class="_fw">
      <div class="tw-six font-3-4r text-ir-mid q-pa-sm">Availability</div>
      <q-chip color="transparent" dense square size="sm" v-for="(d, i) in ['Su','M','T','W','Th','F','S']" :key="`day-${d}`">
          <span class="tw-six text-a4 font-3-4r">{{d}}:&nbsp;</span>
          <span class="font-3-4r" v-for="(t, idx) in days[i].times" :key="`t-${i}-${idx}`">{{displayHour(t.start, { map, displayFn })}}-{{displayHour(t.end, { map, displayFn })}}<span v-if="idx < days[i].times.length - 1">&nbsp;|&nbsp;</span>
          </span>
      </q-chip>
    </div>
  </div>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {displayHour} from 'components/common/calendar/utils';


  const props = defineProps({
    modelValue: { required: true },
    useAptcStore: Function,
    useCalendars: Function
  })

  const { item:calendar } = idGet({
    store: props.useCalendars(),
    value: computed(() => props.modelValue),
    useAtcStore: props.useAptcStore
  })

  const map = {'am':'a','pm':'p'}
  const displayFn = (h, m, p) => `${h}${m > 0 ? m.toString().padStart(2, '0') : ''}${p}`

  const days = computed(() => {
    const obj = {
      '0': { times: [] },
      '1': { times: [] },
      '2': { times: [] },
      '3': { times: [] },
      '4': { times: [] },
      '5': { times: [] },
      '6': { times: [] },
    }
    const d = calendar.value.schedule?.days || {};
    for(const k in d){
      obj[k] = { ...obj[k], ...d[k]}
    }
    return obj;
  })
</script>

<style lang="scss" scoped>

</style>
