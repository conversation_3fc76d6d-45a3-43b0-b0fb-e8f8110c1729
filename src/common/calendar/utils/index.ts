export const parseTime = (t:any, path?:string) => {
    if (!t) return undefined;
    if (typeof t === 'number') return t;
    const spl = t.split(' ');
    let num = Number(spl[0].split(':').join('').split('a')[0].split('p')[0])
    if (t.includes('a')) {
        if (num > 1159) return num - 1200
        return num;
    } else {
        if (num < 13) num = num * 100
        return t.includes('p') || path === 'end' ? num + 1200 : num
    }
}

type DisplayOpts = {
    map?: { 'am':string,'pm':string },
    displayFn?: (hours:number, minutes:number, period:string) => string
}
/** military hours or just number into time string */
export const displayHour = (time:number, opts?:DisplayOpts) => {

    const fn = opts?.displayFn ? opts.displayFn : (h, m, p) => `${h}:${m.toString().padStart(2, '0')} ${p}`
    const map = opts?.map || {'am':'am', 'pm':'pm' }
    // Extract hours and minutes
    let hours = Math.floor(time / 100);
    const minutes = Math.min(59, time % 100);

    // Convert to 12-hour format
    const period = hours >= 12 ? map['pm'] : map['am'];
    if (hours > 12) {
        hours -= 12;
    } else if (hours < 1) {
        hours = 12; // Midnight case
    }

    // Format minutes properly
    return fn(hours,minutes,period);
}
