<template>
  <q-chip v-bind="{clickable: true, ...$attrs}">
    <q-icon name="mdi-calendar" color="a3" class="q-mr-xs"></q-icon>
    <slot name="default">
      <span v-if="cal._id" class="tw-six">{{cal.name}}</span>
      <span v-else>{{picker ? 'Select Calendar' : emptyLabel}}</span>
      <q-icon v-if="picker" class="q-ml-sm" name="mdi-menu-down"></q-icon>
    </slot>
    <q-popup-proxy v-if="picker">
      <div class="bg-ir-bg1 text-ir-text w400 mw100 q-pa-sm">
        <q-input dense filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>

          <q-list separator>
            <q-item v-for="(c, i) in c$.data" :key="`t-${i}`" clickable @click="handleSelect(c)">
              <q-item-section>
                <q-item-label>{{c.name}}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
      </div>
    </q-popup-proxy>
  </q-chip>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';

  const emit = defineEmits(['update:model-value']);

  const props = defineProps({
    emptyLabel: { default: 'No Calendar' },
    modelValue: { required: true },
    emitValue: Boolean,
    picker: Boolean,
    ownerId: { required: true },
    useCalendars: Function,
    useAptcStore: Function
  })

  const { item:cal } = idGet({
    store: props.useCalendars(),
    value: computed(() => props.modelValue),
    useAtcStore: props.useAptcStore
  })

  const handleSelect = (v) => {
    if(props.emitValue) emit('update:model-value', v._id)
    else emit('update:model-value', v)
  }

  const { search, searchQ } = HQuery({})
  const limit = ref(10)
  const { h$:c$ } = HFind({
    store: calendarStore,
    limit,
    pause: computed(() => !props.picker || !props.ownerId),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          owner: props.ownerId
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
