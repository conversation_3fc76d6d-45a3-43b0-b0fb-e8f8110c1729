import {parsePhoneNumber} from 'awesome-phonenumber';
import {_get} from 'symbol-syntax-utils';
import {allCountries} from 'components/common/phone/all-countries';
import {computed, ref} from 'vue';
import {AnyRef} from 'src/utils/types';
import {flag} from 'components/common/phone/flag-emojis';

type Options = {
    emit:(name:string, ...args:any) => void,
    preferredCountries?:AnyRef<Array<string>>,
    displayPath?:string,
    emitValue?:boolean,
    optionValue?:string
}

export const phoneInput = ({ emit, optionValue, preferredCountries, displayPath, emitValue }:Options) => {
    const input = ref('')
    const valid = ref();
    const country = ref({
        iso2: 'US',
        name: 'United States',
        dialCode: '1'
    })
    const favorites = computed(() => (preferredCountries.value || []).map(a => handleCountryCode({ iso2: a }, true)))

    const search = ref('');
    const countries = computed(() => {
        return allCountries.filter(a => a.name.toLowerCase().includes(search.value.toLowerCase())).slice(0, 20)
    })
    const handleCountryCode = ({ dialCode, iso2, name }:{dialCode?:string, iso2:string, name?:string}, ret?:boolean) => {
        if (dialCode || iso2 || name) {
            let v;
            let path = 'dialCode';
            if (dialCode) v = dialCode;
            else if (iso2) {
                path = 'iso2';
                v = iso2;
            } else if (name) {
                path = 'name';
                v = name;
            }
            const idx = allCountries.map(a => a[path]).indexOf(v);
            if (idx > -1) {
                if (!ret) country.value = Object.assign({}, allCountries[idx]);
                else return allCountries[idx];
            }
        }
    }
    const handleInput = (val:string) => {
        const pn = parsePhoneNumber(val, { regionCode: _get(country.value, 'iso2') });
        const phoneNumberObj = pn;
        emit('update:input', phoneNumberObj);
        valid.value = phoneNumberObj.valid;
        if (phoneNumberObj.valid) {

            const payload = emitValue ? _get(phoneNumberObj, optionValue) : pn
            emit('update:valid', true);
            emit('update:model-value', payload);
            emit('update:full', phoneNumberObj)
            input.value = (_get(payload, displayPath || 'number.national', payload) || payload) as string;
        } else emit('update:valid', false);
    }

    return {
        valid,
        getFlag:flag,
        favorites,
        input,
        country,
        search,
        countries,
        handleCountryCode,
        handleInput
    }
}
