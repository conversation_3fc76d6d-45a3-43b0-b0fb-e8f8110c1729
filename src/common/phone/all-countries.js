// Array of country objects for the flag dropdown.

// Here is the criteria for the plugin to support a given country/territory
// - It has an iso2 code: https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
// - It has it's own country calling code (it is not a sub-region of another country): https://en.wikipedia.org/wiki/List_of_country_calling_codes
// - It has a flag in the region-flags project: https://github.com/behdad/region-flags/tree/gh-pages/png
// - It is supported by libphonenumber (it must be listed on this page): https://github.com/googlei18n/libphonenumber/blob/master/resources/ShortNumberMetadata.xml

// Each country array has the following information:
// [
//    Country name,
//    iso2 code,
//    International dial code
// ]
export const countriesObj= {
	'United States': { name: 'United States', iso2: 'US', dialCode: '1' },
	Canada: { name: 'Canada', iso2: 'CA', dialCode: '1' },
	'Afghanistan (‫افغانستان‬‎)': { name: 'Afghanistan (‫افغانستان‬‎)', iso2: 'AF', dialCode: '93' },
	'Albania (Shqipëri)': { name: 'Albania (Shqipëri)', iso2: 'AL', dialCode: '355' },
	'Algeria (‫الجزائر‬‎)': { name: 'Algeria (‫الجزائر‬‎)', iso2: 'DZ', dialCode: '213' },
	'American Samoa': { name: 'American Samoa', iso2: 'AS', dialCode: '1684' },
	Andorra: { name: 'Andorra', iso2: 'AD', dialCode: '376' },
	Angola: { name: 'Angola', iso2: 'AO', dialCode: '244' },
	Anguilla: { name: 'Anguilla', iso2: 'AI', dialCode: '1264' },
	'Antigua and Barbuda': { name: 'Antigua and Barbuda', iso2: 'AG', dialCode: '1268' },
	Argentina: { name: 'Argentina', iso2: 'AR', dialCode: '54' },
	'Armenia (Հայաստան)': { name: 'Armenia (Հայաստան)', iso2: 'AM', dialCode: '374' },
	Aruba: { name: 'Aruba', iso2: 'AW', dialCode: '297' },
	Australia: { name: 'Australia', iso2: 'AU', dialCode: '61' },
	'Austria (Österreich)': { name: 'Austria (Österreich)', iso2: 'AT', dialCode: '43' },
	'Azerbaijan (Azərbaycan)': { name: 'Azerbaijan (Azərbaycan)', iso2: 'AZ', dialCode: '994' },
	Bahamas: { name: 'Bahamas', iso2: 'BS', dialCode: '1242' },
	'Bahrain (‫البحرين‬‎)': { name: 'Bahrain (‫البحرين‬‎)', iso2: 'BH', dialCode: '973' },
	'Bangladesh (বাংলাদেশ)': { name: 'Bangladesh (বাংলাদেশ)', iso2: 'BD', dialCode: '880' },
	Barbados: { name: 'Barbados', iso2: 'BB', dialCode: '1246' },
	'Belarus (Беларусь)': { name: 'Belarus (Беларусь)', iso2: 'BY', dialCode: '375' },
	'Belgium (België)': { name: 'Belgium (België)', iso2: 'BE', dialCode: '32' },
	Belize: { name: 'Belize', iso2: 'BZ', dialCode: '501' },
	'Benin (Bénin)': { name: 'Benin (Bénin)', iso2: 'BJ', dialCode: '229' },
	Bermuda: { name: 'Bermuda', iso2: 'BM', dialCode: '1441' },
	'Bhutan (འབྲུག)': { name: 'Bhutan (འབྲུག)', iso2: 'BT', dialCode: '975' },
	Bolivia: { name: 'Bolivia', iso2: 'BO', dialCode: '591' },
	'Bosnia and Herzegovina (Босна и Херцеговина)': {
		name: 'Bosnia and Herzegovina (Босна и Херцеговина)',
		iso2: 'BA',
		dialCode: '387'
	},
	Botswana: { name: 'Botswana', iso2: 'BW', dialCode: '267' },
	'Brazil (Brasil)': { name: 'Brazil (Brasil)', iso2: 'BR', dialCode: '55' },
	'British Indian Ocean Territory': {
		name: 'British Indian Ocean Territory',
		iso2: 'IO',
		dialCode: '246'
	},
	'British Virgin Islands': { name: 'British Virgin Islands', iso2: 'VG', dialCode: '1284' },
	Brunei: { name: 'Brunei', iso2: 'BN', dialCode: '673' },
	'Bulgaria (България)': { name: 'Bulgaria (България)', iso2: 'BG', dialCode: '359' },
	'Burkina Faso': { name: 'Burkina Faso', iso2: 'BF', dialCode: '226' },
	'Burundi (Uburundi)': { name: 'Burundi (Uburundi)', iso2: 'BI', dialCode: '257' },
	'Cambodia (កម្ពុជា)': { name: 'Cambodia (កម្ពុជា)', iso2: 'KH', dialCode: '855' },
	'Cameroon (Cameroun)': { name: 'Cameroon (Cameroun)', iso2: 'CM', dialCode: '237' },
	'Cape Verde (Kabu Verdi)': { name: 'Cape Verde (Kabu Verdi)', iso2: 'CV', dialCode: '238' },
	'Caribbean Netherlands': { name: 'Caribbean Netherlands', iso2: 'BQ', dialCode: '599' },
	'Cayman Islands': { name: 'Cayman Islands', iso2: 'KY', dialCode: '1345' },
	'Central African Republic (République centrafricaine)': {
		name: 'Central African Republic (République centrafricaine)',
		iso2: 'CF',
		dialCode: '236'
	},
	'Chad (Tchad)': { name: 'Chad (Tchad)', iso2: 'TD', dialCode: '235' },
	Chile: { name: 'Chile', iso2: 'CL', dialCode: '56' },
	'China (中国)': { name: 'China (中国)', iso2: 'CN', dialCode: '86' },
	'Christmas Island': { name: 'Christmas Island', iso2: 'CX', dialCode: '61' },
	'Cocos (Keeling) Islands': { name: 'Cocos (Keeling) Islands', iso2: 'CC', dialCode: '61' },
	Colombia: { name: 'Colombia', iso2: 'CO', dialCode: '57' },
	'Comoros (‫جزر القمر‬‎)': { name: 'Comoros (‫جزر القمر‬‎)', iso2: 'KM', dialCode: '269' },
	'Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)': {
		name: 'Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)',
		iso2: 'CD',
		dialCode: '243'
	},
	'Congo (Republic) (Congo-Brazzaville)': {
		name: 'Congo (Republic) (Congo-Brazzaville)',
		iso2: 'CG',
		dialCode: '242'
	},
	'Cook Islands': { name: 'Cook Islands', iso2: 'CK', dialCode: '682' },
	'Costa Rica': { name: 'Costa Rica', iso2: 'CR', dialCode: '506' },
	'Côte d’Ivoire': { name: 'Côte d’Ivoire', iso2: 'CI', dialCode: '225' },
	'Croatia (Hrvatska)': { name: 'Croatia (Hrvatska)', iso2: 'HR', dialCode: '385' },
	Cuba: { name: 'Cuba', iso2: 'CU', dialCode: '53' },
	'Curaçao': { name: 'Curaçao', iso2: 'CW', dialCode: '599' },
	'Cyprus (Κύπρος)': { name: 'Cyprus (Κύπρος)', iso2: 'CY', dialCode: '357' },
	'Czech Republic (Česká republika)': {
		name: 'Czech Republic (Česká republika)',
		iso2: 'CZ',
		dialCode: '420'
	},
	'Denmark (Danmark)': { name: 'Denmark (Danmark)', iso2: 'DK', dialCode: '45' },
	Djibouti: { name: 'Djibouti', iso2: 'DJ', dialCode: '253' },
	Dominica: { name: 'Dominica', iso2: 'DM', dialCode: '1767' },
	'Dominican Republic (República Dominicana)': {
		name: 'Dominican Republic (República Dominicana)',
		iso2: 'DO',
		dialCode: '1'
	},
	Ecuador: { name: 'Ecuador', iso2: 'EC', dialCode: '593' },
	'Egypt (‫مصر‬‎)': { name: 'Egypt (‫مصر‬‎)', iso2: 'EG', dialCode: '20' },
	'El Salvador': { name: 'El Salvador', iso2: 'SV', dialCode: '503' },
	'Equatorial Guinea (Guinea Ecuatorial)': {
		name: 'Equatorial Guinea (Guinea Ecuatorial)',
		iso2: 'GQ',
		dialCode: '240'
	},
	Eritrea: { name: 'Eritrea', iso2: 'ER', dialCode: '291' },
	'Estonia (Eesti)': { name: 'Estonia (Eesti)', iso2: 'EE', dialCode: '372' },
	Ethiopia: { name: 'Ethiopia', iso2: 'ET', dialCode: '251' },
	'Falkland Islands (Islas Malvinas)': {
		name: 'Falkland Islands (Islas Malvinas)',
		iso2: 'FK',
		dialCode: '500'
	},
	'Faroe Islands (Føroyar)': { name: 'Faroe Islands (Føroyar)', iso2: 'FO', dialCode: '298' },
	Fiji: { name: 'Fiji', iso2: 'FJ', dialCode: '679' },
	'Finland (Suomi)': { name: 'Finland (Suomi)', iso2: 'FI', dialCode: '358' },
	France: { name: 'France', iso2: 'FR', dialCode: '33' },
	'French Guiana (Guyane française)': {
		name: 'French Guiana (Guyane française)',
		iso2: 'GF',
		dialCode: '594'
	},
	'French Polynesia (Polynésie française)': {
		name: 'French Polynesia (Polynésie française)',
		iso2: 'PF',
		dialCode: '689'
	},
	Gabon: { name: 'Gabon', iso2: 'GA', dialCode: '241' },
	Gambia: { name: 'Gambia', iso2: 'GM', dialCode: '220' },
	'Georgia (საქართველო)': { name: 'Georgia (საქართველო)', iso2: 'GE', dialCode: '995' },
	'Germany (Deutschland)': { name: 'Germany (Deutschland)', iso2: 'DE', dialCode: '49' },
	'Ghana (Gaana)': { name: 'Ghana (Gaana)', iso2: 'GH', dialCode: '233' },
	Gibraltar: { name: 'Gibraltar', iso2: 'GI', dialCode: '350' },
	'Greece (Ελλάδα)': { name: 'Greece (Ελλάδα)', iso2: 'GR', dialCode: '30' },
	'Greenland (Kalaallit Nunaat)': { name: 'Greenland (Kalaallit Nunaat)', iso2: 'GL', dialCode: '299' },
	Grenada: { name: 'Grenada', iso2: 'GD', dialCode: '1473' },
	Guadeloupe: { name: 'Guadeloupe', iso2: 'GP', dialCode: '590' },
	Guam: { name: 'Guam', iso2: 'GU', dialCode: '1671' },
	Guatemala: { name: 'Guatemala', iso2: 'GT', dialCode: '502' },
	Guernsey: { name: 'Guernsey', iso2: 'GG', dialCode: '44' },
	'Guinea (Guinée)': { name: 'Guinea (Guinée)', iso2: 'GN', dialCode: '224' },
	'Guinea-Bissau (Guiné Bissau)': { name: 'Guinea-Bissau (Guiné Bissau)', iso2: 'GW', dialCode: '245' },
	Guyana: { name: 'Guyana', iso2: 'GY', dialCode: '592' },
	Haiti: { name: 'Haiti', iso2: 'HT', dialCode: '509' },
	Honduras: { name: 'Honduras', iso2: 'HN', dialCode: '504' },
	'Hong Kong (香港)': { name: 'Hong Kong (香港)', iso2: 'HK', dialCode: '852' },
	'Hungary (Magyarország)': { name: 'Hungary (Magyarország)', iso2: 'HU', dialCode: '36' },
	'Iceland (Ísland)': { name: 'Iceland (Ísland)', iso2: 'IS', dialCode: '354' },
	'India (भारत)': { name: 'India (भारत)', iso2: 'IN', dialCode: '91' },
	Indonesia: { name: 'Indonesia', iso2: 'ID', dialCode: '62' },
	'Iran (‫ایران‬‎)': { name: 'Iran (‫ایران‬‎)', iso2: 'IR', dialCode: '98' },
	'Iraq (‫العراق‬‎)': { name: 'Iraq (‫العراق‬‎)', iso2: 'IQ', dialCode: '964' },
	Ireland: { name: 'Ireland', iso2: 'IE', dialCode: '353' },
	'Isle of Man': { name: 'Isle of Man', iso2: 'IM', dialCode: '44' },
	'Israel (‫ישראל‬‎)': { name: 'Israel (‫ישראל‬‎)', iso2: 'IL', dialCode: '972' },
	'Italy (Italia)': { name: 'Italy (Italia)', iso2: 'IT', dialCode: '39' },
	Jamaica: { name: 'Jamaica', iso2: 'JM', dialCode: '1876' },
	'Japan (日本)': { name: 'Japan (日本)', iso2: 'JP', dialCode: '81' },
	Jersey: { name: 'Jersey', iso2: 'JE', dialCode: '44' },
	'Jordan (‫الأردن‬‎)': { name: 'Jordan (‫الأردن‬‎)', iso2: 'JO', dialCode: '962' },
	'Kazakhstan (Казахстан)': { name: 'Kazakhstan (Казахстан)', iso2: 'KZ', dialCode: '7' },
	Kenya: { name: 'Kenya', iso2: 'KE', dialCode: '254' },
	Kiribati: { name: 'Kiribati', iso2: 'KI', dialCode: '686' },
	Kosovo: { name: 'Kosovo', iso2: 'XK', dialCode: '383' },
	'Kuwait (‫الكويت‬‎)': { name: 'Kuwait (‫الكويت‬‎)', iso2: 'KW', dialCode: '965' },
	'Kyrgyzstan (Кыргызстан)': { name: 'Kyrgyzstan (Кыргызстан)', iso2: 'KG', dialCode: '996' },
	'Laos (ລາວ)': { name: 'Laos (ລາວ)', iso2: 'LA', dialCode: '856' },
	'Latvia (Latvija)': { name: 'Latvia (Latvija)', iso2: 'LV', dialCode: '371' },
	'Lebanon (‫لبنان‬‎)': { name: 'Lebanon (‫لبنان‬‎)', iso2: 'LB', dialCode: '961' },
	Lesotho: { name: 'Lesotho', iso2: 'LS', dialCode: '266' },
	Liberia: { name: 'Liberia', iso2: 'LR', dialCode: '231' },
	'Libya (‫ليبيا‬‎)': { name: 'Libya (‫ليبيا‬‎)', iso2: 'LY', dialCode: '218' },
	Liechtenstein: { name: 'Liechtenstein', iso2: 'LI', dialCode: '423' },
	'Lithuania (Lietuva)': { name: 'Lithuania (Lietuva)', iso2: 'LT', dialCode: '370' },
	Luxembourg: { name: 'Luxembourg', iso2: 'LU', dialCode: '352' },
	'Macau (澳門)': { name: 'Macau (澳門)', iso2: 'MO', dialCode: '853' },
	'Macedonia (FYROM) (Македонија)': {
		name: 'Macedonia (FYROM) (Македонија)',
		iso2: 'MK',
		dialCode: '389'
	},
	'Madagascar (Madagasikara)': { name: 'Madagascar (Madagasikara)', iso2: 'MG', dialCode: '261' },
	Malawi: { name: 'Malawi', iso2: 'MW', dialCode: '265' },
	Malaysia: { name: 'Malaysia', iso2: 'MY', dialCode: '60' },
	Maldives: { name: 'Maldives', iso2: 'MV', dialCode: '960' },
	Mali: { name: 'Mali', iso2: 'ML', dialCode: '223' },
	Malta: { name: 'Malta', iso2: 'MT', dialCode: '356' },
	'Marshall Islands': { name: 'Marshall Islands', iso2: 'MH', dialCode: '692' },
	Martinique: { name: 'Martinique', iso2: 'MQ', dialCode: '596' },
	'Mauritania (‫موريتانيا‬‎)': { name: 'Mauritania (‫موريتانيا‬‎)', iso2: 'MR', dialCode: '222' },
	'Mauritius (Moris)': { name: 'Mauritius (Moris)', iso2: 'MU', dialCode: '230' },
	Mayotte: { name: 'Mayotte', iso2: 'YT', dialCode: '262' },
	'Mexico (México)': { name: 'Mexico (México)', iso2: 'MX', dialCode: '52' },
	Micronesia: { name: 'Micronesia', iso2: 'FM', dialCode: '691' },
	'Moldova (Republica Moldova)': { name: 'Moldova (Republica Moldova)', iso2: 'MD', dialCode: '373' },
	Monaco: { name: 'Monaco', iso2: 'MC', dialCode: '377' },
	'Mongolia (Монгол)': { name: 'Mongolia (Монгол)', iso2: 'MN', dialCode: '976' },
	'Montenegro (Crna Gora)': { name: 'Montenegro (Crna Gora)', iso2: 'ME', dialCode: '382' },
	Montserrat: { name: 'Montserrat', iso2: 'MS', dialCode: '1664' },
	'Morocco (‫المغرب‬‎)': { name: 'Morocco (‫المغرب‬‎)', iso2: 'MA', dialCode: '212' },
	'Mozambique (Moçambique)': { name: 'Mozambique (Moçambique)', iso2: 'MZ', dialCode: '258' },
	'Myanmar (Burma) (မြန်မာ)': { name: 'Myanmar (Burma) (မြန်မာ)', iso2: 'MM', dialCode: '95' },
	'Namibia (Namibië)': { name: 'Namibia (Namibië)', iso2: 'NA', dialCode: '264' },
	Nauru: { name: 'Nauru', iso2: 'NR', dialCode: '674' },
	'Nepal (नेपाल)': { name: 'Nepal (नेपाल)', iso2: 'NP', dialCode: '977' },
	'Netherlands (Nederland)': { name: 'Netherlands (Nederland)', iso2: 'NL', dialCode: '31' },
	'New Caledonia (Nouvelle-Calédonie)': {
		name: 'New Caledonia (Nouvelle-Calédonie)',
		iso2: 'NC',
		dialCode: '687'
	},
	'New Zealand': { name: 'New Zealand', iso2: 'NZ', dialCode: '64' },
	Nicaragua: { name: 'Nicaragua', iso2: 'NI', dialCode: '505' },
	'Niger (Nijar)': { name: 'Niger (Nijar)', iso2: 'NE', dialCode: '227' },
	Nigeria: { name: 'Nigeria', iso2: 'NG', dialCode: '234' },
	Niue: { name: 'Niue', iso2: 'NU', dialCode: '683' },
	'Norfolk Island': { name: 'Norfolk Island', iso2: 'NF', dialCode: '672' },
	'North Korea (조선 민주주의 인민 공화국)': { name: 'North Korea (조선 민주주의 인민 공화국)', iso2: 'KP', dialCode: '850' },
	'Northern Mariana Islands': { name: 'Northern Mariana Islands', iso2: 'MP', dialCode: '1670' },
	'Norway (Norge)': { name: 'Norway (Norge)', iso2: 'NO', dialCode: '47' },
	'Oman (‫عُمان‬‎)': { name: 'Oman (‫عُمان‬‎)', iso2: 'OM', dialCode: '968' },
	'Pakistan (‫پاکستان‬‎)': { name: 'Pakistan (‫پاکستان‬‎)', iso2: 'PK', dialCode: '92' },
	Palau: { name: 'Palau', iso2: 'PW', dialCode: '680' },
	'Palestine (‫فلسطين‬‎)': { name: 'Palestine (‫فلسطين‬‎)', iso2: 'PS', dialCode: '970' },
	'Panama (Panamá)': { name: 'Panama (Panamá)', iso2: 'PA', dialCode: '507' },
	'Papua New Guinea': { name: 'Papua New Guinea', iso2: 'PG', dialCode: '675' },
	Paraguay: { name: 'Paraguay', iso2: 'PY', dialCode: '595' },
	'Peru (Perú)': { name: 'Peru (Perú)', iso2: 'PE', dialCode: '51' },
	Philippines: { name: 'Philippines', iso2: 'PH', dialCode: '63' },
	'Poland (Polska)': { name: 'Poland (Polska)', iso2: 'PL', dialCode: '48' },
	Portugal: { name: 'Portugal', iso2: 'PT', dialCode: '351' },
	'Puerto Rico': { name: 'Puerto Rico', iso2: 'PR', dialCode: '1' },
	'Qatar (‫قطر‬‎)': { name: 'Qatar (‫قطر‬‎)', iso2: 'QA', dialCode: '974' },
	'Réunion (La Réunion)': { name: 'Réunion (La Réunion)', iso2: 'RE', dialCode: '262' },
	'Romania (România)': { name: 'Romania (România)', iso2: 'RO', dialCode: '40' },
	'Russia (Россия)': { name: 'Russia (Россия)', iso2: 'RU', dialCode: '7' },
	Rwanda: { name: 'Rwanda', iso2: 'RW', dialCode: '250' },
	'Saint Barthélemy': { name: 'Saint Barthélemy', iso2: 'BL', dialCode: '590' },
	'Saint Helena': { name: 'Saint Helena', iso2: 'SH', dialCode: '290' },
	'Saint Kitts and Nevis': { name: 'Saint Kitts and Nevis', iso2: 'KN', dialCode: '1869' },
	'Saint Lucia': { name: 'Saint Lucia', iso2: 'LC', dialCode: '1758' },
	'Saint Martin (Saint-Martin (partie française))': {
		name: 'Saint Martin (Saint-Martin (partie française))',
		iso2: 'MF',
		dialCode: '590'
	},
	'Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)': {
		name: 'Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)',
		iso2: 'PM',
		dialCode: '508'
	},
	'Saint Vincent and the Grenadines': {
		name: 'Saint Vincent and the Grenadines',
		iso2: 'VC',
		dialCode: '1784'
	},
	Samoa: { name: 'Samoa', iso2: 'WS', dialCode: '685' },
	'San Marino': { name: 'San Marino', iso2: 'SM', dialCode: '378' },
	'São Tomé and Príncipe (São Tomé e Príncipe)': {
		name: 'São Tomé and Príncipe (São Tomé e Príncipe)',
		iso2: 'ST',
		dialCode: '239'
	},
	'Saudi Arabia (‫المملكة العربية السعودية‬‎)': {
		name: 'Saudi Arabia (‫المملكة العربية السعودية‬‎)',
		iso2: 'SA',
		dialCode: '966'
	},
	'Senegal (Sénégal)': { name: 'Senegal (Sénégal)', iso2: 'SN', dialCode: '221' },
	'Serbia (Србија)': { name: 'Serbia (Србија)', iso2: 'RS', dialCode: '381' },
	Seychelles: { name: 'Seychelles', iso2: 'SC', dialCode: '248' },
	'Sierra Leone': { name: 'Sierra Leone', iso2: 'SL', dialCode: '232' },
	Singapore: { name: 'Singapore', iso2: 'SG', dialCode: '65' },
	'Sint Maarten': { name: 'Sint Maarten', iso2: 'SX', dialCode: '1721' },
	'Slovakia (Slovensko)': { name: 'Slovakia (Slovensko)', iso2: 'SK', dialCode: '421' },
	'Slovenia (Slovenija)': { name: 'Slovenia (Slovenija)', iso2: 'SI', dialCode: '386' },
	'Solomon Islands': { name: 'Solomon Islands', iso2: 'SB', dialCode: '677' },
	'Somalia (Soomaaliya)': { name: 'Somalia (Soomaaliya)', iso2: 'SO', dialCode: '252' },
	'South Africa': { name: 'South Africa', iso2: 'ZA', dialCode: '27' },
	'South Korea (대한민국)': { name: 'South Korea (대한민국)', iso2: 'KR', dialCode: '82' },
	'South Sudan (‫جنوب السودان‬‎)': {
		name: 'South Sudan (‫جنوب السودان‬‎)',
		iso2: 'SS',
		dialCode: '211'
	},
	'Spain (España)': { name: 'Spain (España)', iso2: 'ES', dialCode: '34' },
	'Sri Lanka (ශ්‍රී ලංකාව)': { name: 'Sri Lanka (ශ්‍රී ලංකාව)', iso2: 'LK', dialCode: '94' },
	'Sudan (‫السودان‬‎)': { name: 'Sudan (‫السودان‬‎)', iso2: 'SD', dialCode: '249' },
	Suriname: { name: 'Suriname', iso2: 'SR', dialCode: '597' },
	'Svalbard and Jan Mayen': { name: 'Svalbard and Jan Mayen', iso2: 'SJ', dialCode: '47' },
	Swaziland: { name: 'Swaziland', iso2: 'SZ', dialCode: '268' },
	'Sweden (Sverige)': { name: 'Sweden (Sverige)', iso2: 'SE', dialCode: '46' },
	'Switzerland (Schweiz)': { name: 'Switzerland (Schweiz)', iso2: 'CH', dialCode: '41' },
	'Syria (‫سوريا‬‎)': { name: 'Syria (‫سوريا‬‎)', iso2: 'SY', dialCode: '963' },
	'Taiwan (台灣)': { name: 'Taiwan (台灣)', iso2: 'TW', dialCode: '886' },
	Tajikistan: { name: 'Tajikistan', iso2: 'TJ', dialCode: '992' },
	Tanzania: { name: 'Tanzania', iso2: 'TZ', dialCode: '255' },
	'Thailand (ไทย)': { name: 'Thailand (ไทย)', iso2: 'TH', dialCode: '66' },
	'Timor-Leste': { name: 'Timor-Leste', iso2: 'TL', dialCode: '670' },
	Togo: { name: 'Togo', iso2: 'TG', dialCode: '228' },
	Tokelau: { name: 'Tokelau', iso2: 'TK', dialCode: '690' },
	Tonga: { name: 'Tonga', iso2: 'TO', dialCode: '676' },
	'Trinidad and Tobago': { name: 'Trinidad and Tobago', iso2: 'TT', dialCode: '1868' },
	'Tunisia (‫تونس‬‎)': { name: 'Tunisia (‫تونس‬‎)', iso2: 'TN', dialCode: '216' },
	'Turkey (Türkiye)': { name: 'Turkey (Türkiye)', iso2: 'TR', dialCode: '90' },
	Turkmenistan: { name: 'Turkmenistan', iso2: 'TM', dialCode: '993' },
	'Turks and Caicos Islands': { name: 'Turks and Caicos Islands', iso2: 'TC', dialCode: '1649' },
	Tuvalu: { name: 'Tuvalu', iso2: 'TV', dialCode: '688' },
	'U.S. Virgin Islands': { name: 'U.S. Virgin Islands', iso2: 'VI', dialCode: '1340' },
	Uganda: { name: 'Uganda', iso2: 'UG', dialCode: '256' },
	'Ukraine (Україна)': { name: 'Ukraine (Україна)', iso2: 'UA', dialCode: '380' },
	'United Arab Emirates (‫الإمارات العربية المتحدة‬‎)': {
		name: 'United Arab Emirates (‫الإمارات العربية المتحدة‬‎)',
		iso2: 'AE',
		dialCode: '971'
	},
	'United Kingdom': { name: 'United Kingdom', iso2: 'GB', dialCode: '44' },
	Uruguay: { name: 'Uruguay', iso2: 'UY', dialCode: '598' },
	'Uzbekistan (Oʻzbekiston)': { name: 'Uzbekistan (Oʻzbekiston)', iso2: 'UZ', dialCode: '998' },
	Vanuatu: { name: 'Vanuatu', iso2: 'VU', dialCode: '678' },
	'Vatican City (Città del Vaticano)': {
		name: 'Vatican City (Città del Vaticano)',
		iso2: 'VA',
		dialCode: '39'
	},
	Venezuela: { name: 'Venezuela', iso2: 'VE', dialCode: '58' },
	'Vietnam (Việt Nam)': { name: 'Vietnam (Việt Nam)', iso2: 'VN', dialCode: '84' },
	'Wallis and Futuna (Wallis-et-Futuna)': {
		name: 'Wallis and Futuna (Wallis-et-Futuna)',
		iso2: 'WF',
		dialCode: '681'
	},
	'Western Sahara (‫الصحراء الغربية‬‎)': {
		name: 'Western Sahara (‫الصحراء الغربية‬‎)',
		iso2: 'EH',
		dialCode: '212'
	},
	'Yemen (‫اليمن‬‎)': { name: 'Yemen (‫اليمن‬‎)', iso2: 'YE', dialCode: '967' },
	Zambia: { name: 'Zambia', iso2: 'ZM', dialCode: '260' },
	Zimbabwe: { name: 'Zimbabwe', iso2: 'ZW', dialCode: '263' },
	'Åland Islands': { name: 'Åland Islands', iso2: 'AX', dialCode: '358' }
}


export const allCountries =  Object.keys(countriesObj).map((a) => {
	return { ...countriesObj[a] }
});
