<template>
  <q-item v-bind="{
     clickable: true,
     class: '_fw',
     ...itemAttrs
  }" @click="emit('update:model-value', val)">
    <q-item-section avatar v-if="!hideAvatar">
      <slot name="avatar" :item="val">
        <default-avatar
            v-bind="{
              modelValue: val,
              descriptionPath,
              defaultAvatar,
              square,
              namePath,
              defaultCharacter: character,
              backupNamePath,
              icon,
              avatarPath,
              sizeIn,
              bgIn,
              bordered,
              ...avatarAttrs
            }"
        ></default-avatar>
      </slot>
    </q-item-section>
    <q-item-section>
      <slot name="top" :item="val" :itemName="_get(val, namePath, defaultName)">
        <q-item-label :class="titleClass">
          {{ prefix }}{{ limitStr(_get(val, namePath, defaultName), limit, '...') }}
        </q-item-label>
      </slot>
<!--      <q-item-label :class="subtitleClass" caption>-->
<!--        {{ $limitStr(_get(val, descriptionPath, defaultDescription), 200, '...') }}-->
<!--      </q-item-label>-->
      <slot name="bottom" :item="val"></slot>
    </q-item-section>
    <q-item-section side>
      <slot name="side" :item="val"></slot>
    </q-item-section>
    <slot name="menu" :item="val"></slot>
  </q-item>
</template>

<script setup>
  import DefaultAvatar from './DefaultAvatar.vue';
import {idGet} from 'src/utils/id-get';
import {computed} from 'vue';
import { limitStr } from 'symbol-syntax-utils';
import { _get } from 'symbol-syntax-utils';

const emit = defineEmits(['update:model-value'])
const props = defineProps({
  prefix: { type: String, default: '' },
  tooltip: Boolean,
  titleClass: String,
  limit: { type: Number, default: 40 },
  hideAvatar: Boolean,
  defaultName: String,
  itemAttrs: Object,
  avatarAttrs: Object,
  modelValue: { required: true },
  descriptionPath: { type: String, default: 'description' },
  defaultAvatar: String,
  square: Boolean,
  namePath: {
    type: String,
    default: 'name'
  },
  defaultDescription: String,
  defaultCharacter: String,
  backupNamePath: {
    type: String,
    default: 'username'
  },
  icon: String,
  avatarPath: {
    type: String,
    default: 'avatar'
  },
  store: { required: false },
  dark: Boolean,
  sizeIn: String,
  bgIn: String,
  bordered: Boolean
});

const mv = computed(() => {
  return props.modelValue;
});

const { item: val } = idGet({
  store: props.store,
  value: mv
});

const character = computed(() => {
  let v = val ? val.value : val;
  let p = _get(v, props.namePath, _get(v, props.backupNamePath));
  if (p) return p.charAt(0);
  else return props.defaultCharacter;
});

</script>

<style scoped>

</style>
