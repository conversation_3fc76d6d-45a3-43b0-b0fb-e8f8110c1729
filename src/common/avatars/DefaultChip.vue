<template>
  <q-chip class="chip_class" @click="$emit('update:model-value', modelValue)"
          v-bind="{dark: dark, clickable: true, color: 'ir-bg2', ...chipAttrs}" @remove="$emit('remove', modelValue)">
    <slot name="avatar">
      <default-avatar
          v-show="!hideAvatar"
          :modelValue="val"
          :descriptionPath="descriptionPath"
          :defaultAvatar="defaultAvatar"
          :square="square"
          :namePath="avatarNamePath"
          :defaultCharacter="character"
          :backupNamePath="backupNamePath"
          :icon="icon"
          :avatarPath="avatarPath"
          :dark="dark"
          :sizeIn="sizeIn"
          :bgIn="bgIn"
          :bordered="bordered"
      >
      </default-avatar>
    </slot>

    {{ prefix }}{{ name }}

    <slot name="right"></slot>

    <slot name="menu" :item="val"></slot>
    <q-tooltip v-if="tooltip">{{ _get(val, namePath, defaultName || _get(val, backupNamePath)) }}</q-tooltip>
  </q-chip>
</template>

<script setup>
  import DefaultAvatar from './DefaultAvatar.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import { _get } from 'symbol-syntax-utils';

  const limitStr = (string, limit, append, mid) => {
    const apnd = append ? append : '...';
    const appendLength = apnd ? JSON.stringify(apnd).length : 0;
    const stringLength = string?.length || 0;
    if (limit && stringLength && stringLength > limit) {
      return string.substring(0, limit - appendLength - (mid ? 3 : 0)) + apnd + (mid ? string.substring(string.length-4, string.length) : '')
    } else return string;
  }

  const props = defineProps({
    useAtcStore: Function,
    prefix: { type: String, default: '' },
    tooltip: Boolean,
    limit: { type: Number, default: 40 },
    hideAvatar: Boolean,
    defaultName: String,
    chipAttrs: Object,
    params: Object,
    refresh: Boolean,
    modelValue: { required: true },
    descriptionPath: { type: String, default: 'description' },
    defaultAvatar: String,
    square: Boolean,
    namePath: {
      type: String,
      default: 'name'
    },
    avatarLetterPath: String,
    defaultDescription: String,
    defaultCharacter: String,
    backupNamePath: {
      type: String,
      default: 'username'
    },
    icon: String,
    avatarPath: {
      type: String,
      default: 'avatar'
    },
    store: { required: false },
    dark: Boolean,
    sizeIn: String,
    bgIn: String,
    bordered: Boolean
  });

  const mv = computed(() => {
    return props.modelValue || {};
  });

  const { item:val } = idGet({
    store: props.store,
    log: false,
    value: mv,
    params: computed(() => props.params),
    refresh: props.refresh,
    useAtcStore: props.useAtcStore
  });

  const character = computed(() => {
    let v = val ? val.value : val;
    let p = _get(v, props.namePath, _get(v, props.backupNamePath));
    if (p) return p.charAt(0);
    else return props.defaultCharacter;
  });

  const avatarNamePath = computed(() => props.avatarLetterPath || props.namePath)

  const name = computed(() => {
    const str = val.value ? _get(val.value, (props.namePath || props.defaultName), props.defaultName) || props.defaultName : props.defaultName || ''
    if(!props.limit) return str;
    else return limitStr(str, props.limit, '...')
  })

</script>

<style scoped lang="scss">
  .chip_class {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
</style>
