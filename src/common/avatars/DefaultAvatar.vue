<template>
  <q-avatar
      v-bind="{
        size: sizeIn,
        class: `${avatar?.url && !bgIn ? 'bg-transparent' : dark ? bgIn ? 'bg-' + bgIn + ' text-dark' : 'bg-light' + ' text-dark' : bgIn ? 'bg-' + bgIn + ' text-light' : 'bg-dark' + ' text-light'} ${classIn}`,
        style: {...computedStyle,...avatarAttrs?.style},
    ...avatarAttrs
      }"
  >
    <template v-if="avatar.url">
      <img :style="{objectFit: 'cover', ..._get(attrs, 'imageStyle')}" :src="avatar.url">
    </template>
    <template v-else-if="icon">
      <span>
      <q-icon :name="icon" :size="(parseFloat(sizeIn ? sizeIn : 60) / 1.5) + 'px'"
              :color="dark ? 'light' : ''"></q-icon>
      </span>
    </template>
    <template v-else>
      <slot name="no-avatar">
        <span :class="`text-${dark ? 'dark' : 'white' }`">{{ character }}</span>
      </slot>
    </template>
    <slot name="menu" :item="val">
      <q-menu>
        <default-card v-bind="{...passAttrs, modelValue: val}"></default-card>
      </q-menu>
    </slot>
  </q-avatar>
</template>

<script setup>
  import DefaultCard from './DefaultCard.vue';

  import {computed, useAttrs} from 'vue';
  import {_get} from 'symbol-syntax-utils';
  import {idGet} from 'src/utils/id-get';
  import {useUploads} from 'stores/uploads';

  const uploadStore = useUploads();

  const props = defineProps({
    defaultAvatar: String,
    square: Boolean,
    namePath: {
      type: String,
      default: 'name'
    },
    wnStore: { required: false },
    classIn: String,
    defaultCharacter: String,
    backupNamePath: {
      type: String,
      default: 'username'
    },
    avatarAttrs: Object,
    icon: String,
    avatarPath: {
      type: [String, Array],
      default: () => {
        return ['avatar'];
      }
    },
    service: String,
    imgIndex: { type: Number, default: 0 },
    store: { required: false },
    dark: Boolean,
    sizeIn: String,
    modelValue: { required: true },
    bgIn: String,
    bordered: Boolean,
    uploadIdPath: { default: 'uploadId' }
  });

  const attrs = useAttrs();
  const passAttrs = { ...attrs, ...props };

  const mv = computed(() => {
    return props.modelValue;
  });

  const { item: val } = idGet({
    store: props.store,
    name: 'val',
    value: mv
  });

  const { item: avatarUpload } = idGet({
    store: uploadStore,
    value: computed(() => {
      const path = Array.isArray(props.avatarPath) ? props.avatarPath.join('.') : props.avatarPath;
      return _get(val.value, `_fastjoin.files.${path}`) || _get(val.value, `${path}.${props.uploadIdPath}`);
    })
  })
  const avatar = computed(() => {
    return avatarUpload.value?._id ? avatarUpload.value : { url: props.defaultAvatar };
  })

  const computedStyle = computed(() => {
    return {
      borderRadius: props.square ? '4px' : '',
      boxShadow: props.bordered ? `0 0 0 3px ${props.bgIn ? 'var(--q-' + props.bgIn + ')' : props.dark ? '#fafafa' : '#101010'}` : 'none', ..._get(attrs.value, 'divStyle'),
      backgroundColor: props.bgIn ? 'var(--q-' + props.bgIn + ')' : avatar.value.url ? 'transparent' : props.dark ? 'var(--ir-text)' : 'var(--ir-bg)',
      color: props.dark ? 'var(--ir-bg)' : 'var(--ir-text)'
    }
  })


  const character = computed(() => {
    let v = val ? val.value : val;
    let p = _get(v, props.namePath, _get(v, props.backupNamePath));
    if (p) return p.charAt(0);
    else return props.defaultCharacter;
  });


</script>
