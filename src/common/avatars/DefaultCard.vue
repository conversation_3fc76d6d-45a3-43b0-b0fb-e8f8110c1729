<template>
  <div class="__dc _c q-pa-lg">
    <div class="row justify-center">
      <default-avatar v-bind="{...attrs, size: '60px'}"></default-avatar>
    </div>

    <div class="text-center q-pt-sm font-1-1-4r">
      <b>{{name}}</b>
    </div>
  </div>
</template>

<script setup>

  import DefaultAvatar from 'src/components/common/avatars/DefaultAvatar.vue';
  import {computed, onMounted, useAttrs} from 'vue';
  import {_get } from 'symbol-syntax-utils';

  const attrs = useAttrs();

  const namePath = computed(() => attrs.namePath || 'name');
  const backupNamePath = computed(() => attrs.backupNamePath || 'name');

  const name = computed(() => {
    let v = attrs.modelValue;
    return _get(v, namePath.value, _get(v, backupNamePath.value));
  })


</script>

<style lang="scss" scoped>
  .__dc {
    width: 250px;
    height: 150px;
  }
</style>
