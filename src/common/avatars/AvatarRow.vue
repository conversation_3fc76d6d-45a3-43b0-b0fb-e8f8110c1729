<template>
  <div class="__attr_row">
    <div v-if="useList?.length" class="relative-position"
         :style="{ width: useList ? size + (useList.length * size * .21) + (over ? size / 1.35 : 0) + 'px' : 'auto', maxWidth: '100%', height: size + 'px' }">
      <div
          v-for="(av, i) in useList" :key="`item-${i}`"
          @pointerenter="hover = i"
          @pointerleave="hover = -1"
          @click="reorder(i, av)"
          :class="`__attr_wrap ${hover === i ? '__highlight' : ''}`"
          :style="getStyle(i)"
      >
        <slot name="avatar" :item="av">
          <default-avatar
              :square="square"
              :model-value="av"
              :size-in="size + 'px'"
              :divStyle="{ boxShadow: '0 0 0 1px white'}"
              :store="avatarStore"
              :avatar-path="avatarPath"
              :name-path="namePath"
          >
            <template v-slot:menu="scope">
              <slot name="menu" v-bind="scope"></slot>
            </template>
          </default-avatar>
        </slot>
      </div>
    </div>
    <slot name="right" :list="useList" :first="useList ? useList[0] : undefined" :active="active" :over="over"
          :total="useTotal">
      <div class="__over text-weight-bold" v-if="over" :style="{ fontSize: Math.floor(size / 2.5) + 'px' }">
        +{{ over }}
      </div>
    </slot>
  </div>
</template>

<script setup>
  import DefaultAvatar from './DefaultAvatar.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {_get} from 'symbol-syntax-utils';

  const props = defineProps({
    avatarPath: String,
    namePath: String,
    path: String,
    nestedFn: Function,
    store: { required: false },
    avatarStore: { required: false },
    loadService: String,
    square: { type: Boolean },
    modelValue: { required: false },
    limit: { type: Number, default: 3 },
    alwaysLoad: Boolean,
    total: Number,
    size: { type: Number, default: 25 },
    useAptcStore:Function
  })

  const hover = ref(-1);
  const orderedItems = [];

  const { item } = idGet({
    store: props.store,
    value: computed(() => Array.isArray(props.modelValue) ? undefined : props.modelValue),
    useAtcStore: props.useAptcStore
  });

  const active = computed(() => hover.value > -1 ? _get(useList.value, [hover.value]) : undefined);

  const useTotal = computed(() => props.total || props.path ? (_get(props.modelValue, props.path) || []).length : props.modelValue?.length);

  const fullList = computed(() => {
    if (props.store) {
      if (props.path) {
        if (props.nestedFn) return props.nestedFn(item.value);
        else return (_get(item.value, props.path) || []);
      } else return [item.value];
    } else if (Array.isArray(props.modelValue)) return props.modelValue
    else if (props.path) return (_get(props.modelValue, props.path) || []);
    else return []
  })
  const useList = computed(() => {
    return fullList.value?.slice(0, props.limit || 3)
  })

  const over = computed(() => {
    return Math.max((useList.value?.length || 0) - props.limit, 0);
  })

  const getStyle = (i) => {
    return {
      zIndex: hover.value === i ? 100 : useList.value ? useList.value.length + 2 - i : 0,
      left: (i * (props.size / 2.5)) + 'px'
    };
  }

  const arrMove = (arr, fromIndex, toIndex, obj) => {
    if (arr) {
      let cloneArr = JSON.parse(JSON.stringify(arr));
      cloneArr.splice(fromIndex, 1);
      cloneArr.splice(toIndex, 0, obj);
      return cloneArr;
    }
  };

  const reorder = (idx, obj) => {
    if (idx > 0) {
      for (let i = idx; i > -1; i--) {
        if (i === idx) {
          arrMove(orderedItems.value, i, i - 1, obj);
          i--;
        } else {
          setTimeout(() => {
            arrMove(orderedItems.value, i, i - 1, obj);
            i--;
          }, 100);
        }
      }
    }
  }

</script>

<style scoped lang="scss">
  .__attr_row {
    display: flex;
    align-items: center;
    position: relative;
    padding: 3px 0;
  }

  .__attr_wrap {
    cursor: pointer;
    z-index: 1;
    position: absolute;
    top: 0;
    left: 0;
    transition: all .1s ease-out;
    transform: none;
  }

  .__highlight {
    z-index: 100;
    transform: scale(1.2) translate(0, -10%);
  }

</style>
