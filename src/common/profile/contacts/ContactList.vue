<template>
  <div class="_fw">
    <component
        :is="comps[path].component"
        v-bind="comps[path].attrs"
        v-model="inputs[path]"
        v-model:valid="valid"
    >
      <template v-slot:append="scope">
        <q-btn v-if="valid" dense round icon="mdi-plus" outline color="primary" @click="add(scope)"></q-btn>
      </template>
    </component>

    <div class="_fw">
      <div
          class="__ci q-pa-sm q-my-xs flex items-center"
          v-for="(item, i) in plural"
          :key="`item-${i}`"
      >
        <q-menu v-if="format(singular) !== format(item)">
          <q-btn
              @click="setPrimary(item)"
              color="white"
              text-color="black"
              icon="mdi-star"
              square
              :label="`Make primary ${path}`"
          >
          </q-btn>
        </q-menu>
        <div class="q-pa-sm">
          <q-icon v-if="format(singular) === format(item)" color="primary"
                  :name="format(item) === format(singular) ? 'mdi-circle-medium' : 'mdi-circle-medium-outline'">
            <q-tooltip>Primary {{ path }}</q-tooltip>
          </q-icon>
        </div>
        <div class="q-pa-sm">
          {{ format(item) }}
        </div>

        <q-space/>
        <q-btn size="sm" dense flat icon="mdi-close" color="red">
          <q-menu>
            <q-btn flat color="white" text-color="red" square icon="mdi-delete" :label="`Remove ${path}?`"
                   @click="remove(i)"></q-btn>
          </q-menu>
        </q-btn>
      </div>
    </div>

  </div>
</template>

<script setup>
  import EmailField from 'src/components/common/input/EmailField.vue';
  import PhoneInput from 'src/components/common/phone/PhoneInput.vue';
  import MultiAddress from 'src/components/common/address/tomtom/MultiAddress.vue';

  import {computed, ref} from 'vue';
  import {_get} from 'symbol-syntax-utils';

  const props = defineProps({
    modelValue: Object,
    path: String,
    store: { required: false }
  });

  const emit = defineEmits(['update:model-value'])

  const singular = computed(() => {
    return _get(props.modelValue, props.path);
  });

  const valid = ref(false);
  const inputs = ref({
    email: '',
    phone: {},
    address: {}
  });

  const pluralPath = computed(() => {
    const obj = {
      'email': 's',
      'phone': 's',
      'address': 'es'
    }
    return `${props.path}${obj[props.path]}`;
  });

  const plural = computed(() => {
    return (_get(props.modelValue, pluralPath.value) || []).map(a => a);
  });

  const comps = computed(() => {
    return {
      email: {
        component: EmailField,
        attrs: {
          hideBottomSpace: true,
          label: 'Add Email',
          icon: {
            name: 'mdi-email',
            color: 'primary'
          }
        }
      },
      phone: {
        component: PhoneInput,
        attrs: {
          inputAttrs: {
            label: 'Add Phone'
          }
        }
      },
      address: {
        component: MultiAddress,
        attrs: {
          label: 'Add Address',
          allowAdd: true,
          icon: {
            name: 'mdi-map-marker',
            color: 'primary'
          }
        }
      }
    }
  })

  const format = (val) => {
    const obj = {
      email: () => val,
      phone: () => _get(val, 'number.national', val),
      address: () => _get(val, 'formatted')
    }
    return obj[props.path]();
  }
  const patch = async (path, val) => {
    if(props.store) props.store.patch(props.modelValue._id, { [`${path}`]: val }, { ltail: window.location.href })
  }

  const setPrimary = (val) => {
    emit('update:model-value', { ...props.modelValue, [`${props.path}`]: val });
    patch(props.path, val);
  };

  const add = (scope) => {
    const val = inputs.value[props.path];
    if(scope) scope.clear ? scope.clear() : '';
    const payload = { ...props.modelValue };
    const list = [...plural.value, val]
    payload[`${pluralPath.value}}`] = list;
    emit('update:model-value', payload);
    patch(pluralPath.value, list);
  };

  const remove = (i) => {
    const arr = [...plural.value || []];
    arr.splice(i, 1);
    emit('update:model-value', { ...props.modelValue, [`${pluralPath.value}`]: arr });
    patch(pluralPath.value, arr);
  }

</script>

<style lang="scss" scoped>
  .__ci {
    border-radius: 10px;
    box-shadow: 0 0 0px rgba(0, 0, 0, .2);
    background: white;
    transition: all .3s ease;
    cursor: pointer;

    &:hover {
      background: linear-gradient(-15deg, white, #f0f0f0);
    }
  }
</style>
