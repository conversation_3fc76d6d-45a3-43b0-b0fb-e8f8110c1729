<template>
  <div class="_fw">
    <div class="row">
      <div class="col-12 col-md-4 q-pa-md">
        <div class="_fw q-py-sm" v-for="(type, idx) in Object.keys(types)" :key="`type-${idx}`">
          <q-btn
              :class="`_fw text-weight-bold text-left bg-${type === tab ? 'p0' : 'white'}`"
              flat
              no-caps
              text-color="ir-grey-8"
              @click="tab = type"
              rounded
          >
            <div class="_fw text-left">{{types[type].label}}</div>
          </q-btn>
        </div>
      </div>
      <div class="col-12 col-md-8 q-pa-md">
        <component
            :is="types[tab].component"
            v-bind="types[tab].attrs"
            @update:model-value="update"
        ></component>
      </div>
    </div>

  </div>
</template>

<script setup>
  import ContactList from 'src/components/common/profile/contacts/ContactList.vue';
  import {computed, ref} from 'vue';

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: Object,
    store: { required: false }
  });

  const tab = ref('email');

  const types = computed(() => {
    return {
      'email': {
        label: 'Email',
        component: ContactList,
        attrs: {
          modelValue: props.modelValue,
          store: props.store,
          path: 'email'
        }
      },
      'phone': {
        label: 'Phone',
        component: ContactList,
        attrs: {
          modelValue: props.modelValue,
          store: props.store,
          path: 'phone'
        }
      },
      'address': {
        label: 'Address',
        component: ContactList,
        attrs: {
          modelValue: props.modelValue,
          store: props.store,
          path: 'address'
        }
      }
    };
  })

  const update = (val) => {
    emit('update:model-value', val);
  }

</script>

<style lang="scss" scoped>
  .__ci {
    border-radius: 10px;
    box-shadow: 0 0 0px rgba(0,0,0,.2);
    background: white;
    transition: all .3s ease;
    cursor: pointer;

    &:hover {
      background: linear-gradient(-15deg, white, #f0f0f0);

    }
  }
</style>
