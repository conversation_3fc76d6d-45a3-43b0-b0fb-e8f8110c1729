<template>
  <div class="flex items-center">
    <q-btn v-bind="{
      size: 'sm',
      flat: true,
      dense: true,
      icon: 'mdi-plus',
      label: 'Add Badge',
      ...btnAttrs
    }">
      <q-menu>
        <q-card style="width: 250px" class="q-pa-sm">
          <q-list dense separator>
            <q-item dense class="_fw">
              <q-input dense v-model="search">
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
            </q-item>
            <q-item v-for="(badge, i) in Object.keys(profileBadges).filter(a => a.includes(search))" :key="`badge-opt-${i}`">
              <profile-badge @update:model-value="selectBadge" :model-value="badge"></profile-badge>
              <q-item-section side>
                <q-icon v-if="isSelected(badge)" color="green" name="mdi-check"></q-icon>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>
      </q-menu>
    </q-btn>
    <div v-for="(badge, i) in modelValue || []" :key="`badge-${i}`">
      <profile-badge :model-value="badge" :removable="removable" @remove="selectBadge"></profile-badge>
    </div>
  </div>
</template>

<script setup>
  import ProfileBadge from 'src/components/common/profile/badges/ProfileBadge.vue';

  import { ref } from 'vue';
  import {useEnvStore} from 'src/stores/env';
  const envStore = useEnvStore();
  import {storeToRefs} from 'pinia';
  const { profileBadges } = storeToRefs(envStore);

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    removable: Boolean,
    modelValue: { required: true },
    multiple: Boolean,
    btnAttrs: Object
  })

  const search = ref('');
  const selectBadge = (val) => {
    if(props.multiple){
      const arr = [...new Set((props.modelValue || []))]
      const idx = arr.indexOf(val);
      if(idx > -1){
        arr.splice(idx, 1)
        emit('update:model-value', arr)
      }
      else emit('update:model-value', [...arr, val])
    } else emit('update:model-value', val);
  };

  const isSelected = (val) => {
    if(props.multiple){
      return (props.modelValue || []).includes(val)
    }
    else return props.modelValue === (val);
  }
</script>

<style lang="scss" scoped>
</style>
