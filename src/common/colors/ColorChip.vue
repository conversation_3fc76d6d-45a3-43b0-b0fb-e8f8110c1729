<template>
  <q-chip
    v-bind="{
      clickable: true,
      flat: true,
      ...$attrs.chipAttrs,
      label: undefined
    }">
    <q-avatar
      :size="size"
      class="__dot"
      :style="{
        background: modelValue ? (modelValue.indexOf('#') > -1 || modelValue.indexOf('rgb') > -1) ? modelValue : `var(--q-${modelValue})` : 'black'
         }"
    ></q-avatar>

    <span>{{ label || $attrs.chipAttrs?.label }}</span>
    <q-popup-proxy>
      <div style="width: 500px; max-width: 100%">
        <div class="row">
          <q-btn-group flat size="sm">
            <q-btn color="ir-grey-6" dense :flat="!namePick" size="sm" @click="namePick = !namePick"
                   icon="mdi-dots-grid"></q-btn>
            <q-btn color="ir-grey-6" dense :flat="!!namePick" size="sm" @click="namePick = !namePick"
                   icon="mdi-palette"></q-btn>
          </q-btn-group>
          <q-space></q-space>
          <div v-if="gradient" class="__grad_btn" @click="gradDialog = true">
            <common-dialog v-model="gradDialog">
              <ultra-gradient
                :no-gradient="!gradient"
                emit-string
                :modelValue="modelValue"
                @update:model-value="handleInput"
              ></ultra-gradient>
            </common-dialog>
          </div>
        </div>
        <template v-if="!namePick">
          <rgba-slider @update:model-value="handleInput" width="100%" :modelValue="modelValue"></rgba-slider>
        </template>
        <template v-else>
          <color-name-picker :modelValue="modelValue" @update:model-value="handleInput"></color-name-picker>
        </template>
      </div>
    </q-popup-proxy>
  </q-chip>
</template>

<script>
  import RgbaSlider from './RgbaSlider.vue';
  import ColorNamePicker from './ColorNamePicker.vue';
  import CommonDialog from '../dialogs/CommonDialog.vue';
  import UltraGradient from './UltraGradient.vue';

  export default {
    name: 'ColorChip',
    components: { UltraGradient, CommonDialog, ColorNamePicker, RgbaSlider },
    props: {
      gradient: Boolean,
      namePicker: Boolean,
      size: String,
      modelValue: { type: String, default: '#9e9e9e' },
      label: String
    },
    data() {
      return {
        namePick: false,
        gradDialog: false
      };
    },
    watch: {
      namePicker: {
        immediate: true,
        handler(newVal) {
          this.namePick = newVal;
        }
      }
    },
    methods: {
      handleInput(val) {
        // console.log('emit color', color);
        this.$emit('update:model-value', val);
      }
    }
  };
</script>

<style scoped lang="scss">
  .__grad_btn {
    height: 25px;
    width: 25px;
    background: linear-gradient(45deg, var(--q-primary), var(--q-secondary));
    cursor: pointer;
  }

  .__color_chip {
    display: grid;
    grid-template-rows: 100%;
    grid-template-columns: auto 1fr;
    align-items: center;
    justify-items: center;
  }
</style>
