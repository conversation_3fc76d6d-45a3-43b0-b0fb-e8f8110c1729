import {colors} from 'quasar';

export type HSVAObj = { h: number, s: number, v: number, a?: number };
export type RGBAObj = { r: number, g: number, b: number, a?: number };

export const combineColors = (color1: RGBAObj, color2: RGBAObj): string => {
    const hc1 = RGBtoHSV(color1);
    const hc2 = RGBtoHSV(color2);
    const h3 = {
        h: (hc1?.h || 0) + (hc2?.h || 0) / 2,
        s: (hc1?.s || 0) + (hc2?.s || 0) / 2,
        v: (hc1?.v || 0) + (hc2?.v || 0) / 2,
        a: (hc1?.a || 0) + (hc2?.a || 0) / 2,
    };
    const rgb = HSVtoRGB(h3);
    return `rgba(${rgb.r},${rgb.g},${rgb.b},${rgb.a})`;

    // // C = Ca*Aa*(1-Ab) + Cb*Ab
    // // Where Ca is the component (Red, Green or Blue in turn) of Colour A, Cb is the component of Colour B, and Aa and Ab are the alpha values of A and B respectively (in this case, Aa will be 1).
    // let base = [_get(color1, 'r', 255), _get(color1, 'g', 0), _get(color1, 'b', 0), _get(color1, 'a', 100)/100];
    // let added = [_get(color2, 'r', 255), _get(color2, 'g', 0), _get(color2, 'b', 0), _get(color2, 'a', 100)/100];
    //
    // console.log('mixing', base, added);
    // let mix = [];
    // if (typeof added[3] === 'undefined') {
    //   added[3] = 1;
    // }
    // // check if both alpha channels exist.
    // if (base[3] && added[3]) {
    //   mix = [0, 0, 0, 0];
    //   // alpha
    //   mix[3] = 1 - (1 - added[3]) * (1 - base[3]);
    //   // red
    //   mix[0] = Math.round((added[0] * added[3] / mix[3]) + (base[0] * base[3] * (1 - added[3] === 1 ? 0 : added[3]) / mix[3]));
    //   // green
    //   mix[1] = Math.round((added[1] * added[3] / mix[3]) + (base[1] * base[3] * (1 - added[3] === 1 ? 0 : added[3]) / mix[3]));
    //   // blue
    //   mix[2] = Math.round((added[2] * added[3] / mix[3]) + (base[2] * base[3] * (1 - added[3] === 1 ? 0 : added[3]) / mix[3]));
    //
    // } else if (added) {
    //   mix = added;
    // } else {
    //   mix = base;
    // }
    // base = mix;
    // console.log('returning mix', mix);
    // return `rgba(${mix[0]},${mix[1]},${mix[2]},${mix[3]})`;
};
export const HSVtoRGB = (hsvObj: HSVAObj) => {
    // if (arguments.length == 1) {
    //   var { h, s, v } = arguments[0];
    // }
    const obj = {
        h: hsvObj.h,
        s: hsvObj.s ? hsvObj.s : 100,
        v: hsvObj.v ? hsvObj.v : 100,
        a: hsvObj.a ? hsvObj.a : 1,
    };
    // console.log('obj', obj);
    return colors.hsvToRgb(obj);
};
export const RGBtoHSV = (rgbObj: RGBAObj) => {
    const hsv = colors.rgbToHsv(rgbObj);
    return {h: hsv.h, s: hsv.s, v: hsv.v, a: hsv.a};
};
