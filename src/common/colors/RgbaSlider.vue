<template>
  <div :id="`rgbaslider-${id}`" :style="{ width: width }">
    <div class="row justify-center">
      <div :class="`${stack ? 'col-12' : 'col-12 col-md-6'} q-pa-sm`">
        <div class="row justify-center">
          <div
            :id="id + 'value'"
            v-if="!noValue"
            class="__value"
            :style="{
              width: '100%',
              background: `linear-gradient(-45deg, ${useRgba} 70%, transparent)`
            }"
            @click="moveDot($event, true)"
          >
            <div
              class="__dot"
              :style="{ top: dTop, left: dLeft }"
              @mousedown="mousedown = 'd'"
              @touchstart="mousedown = 'd'"
            ></div>
            <div class="__value_white"></div>
            <div class="__value_black"></div>
          </div>
        </div>
      </div>
      <div :class="`${stack ? 'col-12' : 'col-12 col-md-6'}`">
        <!--        HUE SLIDER-->
        <div style="width: 100%" class="text-xxxs text-mb-xxxs text-weight-bolder text-uppercase text-ir-grey-6">
          <div class="q-pa-sm">Color Code</div>
          <div class="row">
            <div :style="{ width: wide > 500 ? '30%' : '90%' }">
              <q-input @update:model-value="setHex" v-model="hex"
                       input-class="text-center text-xxs text-mb-xxs text-weight-bold text-ir-grey-8" dense outlined
                       hide-bottom-space></q-input>
              <div class="row justify-center">hex</div>
            </div>
            <q-space v-if="wide > 500"></q-space>
            <div class="flex items-center" :style="{ width: wide > 500 ? '70%' : '90%' }">
              <div style="width: 25%" class="q-px-xs">
                <q-input @update:model-value="setRgba($event, 'r')" :modelValue="colorObj.r"
                         input-class="text-center text-xxs text-mb-xxs text-weight-bolder text-ir-grey-8" dense outlined
                         hide-bottom-space></q-input>
                <div class="row justify-center">r</div>
              </div>
              <div style="width: 25%" class="q-px-xs">
                <q-input @update:model-value="setRgba($event, 'g')" :modelValue="colorObj.g"
                         input-class="text-center text-xxs text-mb-xxs text-weight-bolder text-ir-grey-8" dense outlined
                         hide-bottom-space></q-input>
                <div class="row justify-center">g</div>
              </div>
              <div style="width: 25%" class="q-px-xs">
                <q-input @update:model-value="setRgba($event, 'b')" :modelValue="colorObj.b"
                         input-class="text-center text-xxs text-mb-xxs text-weight-bolder text-ir-grey-8" dense outlined
                         hide-bottom-space></q-input>
                <div class="row justify-center">b</div>
              </div>
              <div style="width: 25%" class="q-px-xs">
                <q-input @update:model-value="setRgba($event, 'a')" :modelValue="colorObj.a"
                         input-class="text-center text-xxs text-mb-xxs text-weight-bolder text-ir-grey-8" dense outlined
                         hide-bottom-space></q-input>
                <div class="row justify-center">a</div>
              </div>
            </div>
          </div>
        </div>
        <div :class="`q-pa-sm`">
          <div :id="id + 'hue'" class="__hue" :style="{ width: '100%', height: sliderHeight }"
               @click.stop="moveSlider($event, true)">
            <div
              :id="id + 'HueSlider'"
              class="__slider"
              @mousedown="mousedown = 'h'"
              @touchstart="mousedown = 'h'"
              :style="{ left: left }"
            >
              <div :style="{ background: useRgba, width: '40%', height: '80%' }"></div>
            </div>
          </div>
        </div>
        <!--       OPACITY SLIDER-->
        <div :class="`q-pa-sm`">
          <div :id="id + 'opacity'" class="__checker" :style="{ width: '100%', height: sliderHeight }"
               @click="moveOpacity($event, true)">
            <div class="__o_fill" :style="{ background: `linear-gradient(to right, ${useRgba}, transparent)` }"></div>
            <div
              :id="id + 'oSlider'"
              class="__slider"
              @mousedown="mousedown = 'o'"
              @touchstart="mousedown = 'o'"
              :style="{ left: oLeft }"
            >
              <div :style="{ background: useRgba, width: '40%', height: '80%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {colors} from 'quasar';
  import { RGBtoHSV, HSVtoRGB } from './utils';
  import { _get, _set } from 'symbol-syntax-utils';

  //  function rgbToHex(rgb: colorsRgba): string;
  // // function rgbToString (color: colorsRgba): string;
  // function hexToRgb(hex: string): colorsRgba;
  // function hsvToRgb(hsv: colorsHsva): colorsRgba;
  // function rgbToHsv(rgb: colorsRgba): colorsHsva;
  // function textToRgb(color: string): colorsRgba;
  // function lighten(color: string, percent: number): string;
  // function luminosity(color: string | colorsRgba): number;
  // function brightness(color: string | colorsRgba): number;
  // function blend(fgColor: string | colorsRgba, bColor: string | colorsRgba): string;
  // function changeAlpha(color: string, offset: number): string;
  // function setBrand(color: string, value: string, element?: Element): void;
  // function getBrand(color: string, element?: Element): string | null;
  // function getPaletteColor(colorName: string): string;
  const hsv_to_hsl = (hsv, alpha) => {
    let h = hsv.h;
    let s = hsv.s;
    let v = hsv.v;
    // both hsv and hsl values are in [0, 1]
    let l = (2 - s) * v / 2;

    if (l !== 0) {
      if (l === 1) {
        s = 0;
      } else if (l < 0.5) {
        s = s * v / (l * 2);
      } else {
        s = s * v / (2 - l * 2);
      }
    }
    let hsl = { h: h, s: s, l: l };
    if (alpha) hsl.a = alpha;
    return hsl;
  };

  export default {
    name: 'RgbaSlider',
    props: {
      startValue: [String, Object],
      emitValue: { type: Boolean, default: true },
      optionValue: { type: String, default: 'rgba' },
      modelValue: [String, Object],
      noValue: Boolean,
      stack: Boolean,
      id: { type: String, default: 'RgbaSlider' },
      width: { type: String, default: '350px' },
      sliderHeight: { type: String, default: '30px' },
    },
    mounted() {
      document.addEventListener('mouseup', () => {
        this.mousedown = false;
      });
      document.addEventListener('touchend', () => {
        this.mousedown = false;
      });
      document.getElementById(this.id + 'hue').addEventListener('mousemove', (e) => {
        this.moveSlider(e);
      });
      document.getElementById(this.id + 'hue').addEventListener('touchmove', (e) => {
        this.moveSlider(e);
      });
      document.getElementById(this.id + 'opacity').addEventListener('mousemove', (e) => {
        this.moveOpacity(e);
      });
      document.getElementById(this.id + 'opacity').addEventListener('touchmove', (e) => {
        this.moveOpacity(e);
      });
      document.getElementById(this.id + 'value').addEventListener('mousemove', (e) => {
        this.moveDot(e);
      });
      document.getElementById(this.id + 'value').addEventListener('touchmove', (e) => {
        this.moveDot(e);
      });
      this.findWide();
    },
    data() {
      return {
        wide: 400,
        valueTries: 0,
        left: 0,
        dTop: 0,
        dLeft: '100%',
        oLeft: 0,
        mousedown: false,
        color: 'red',
        hex: '#ff0000',
        colorObj: {
          r: 255,
          g: 0,
          b: 0,
          a: 1
        },
        hsv: {
          h: 1,
          s: 0,
          v: 0,
        },
        rgba: {
          r: 255,
          g: 0,
          b: 0,
          a: 1
        }
      };
    },
    watch: {
      startValue: {
        immediate: true,
        handler(newVal) {
          this.valueChange(newVal);
        }
      }
    },
    computed: {
      useRgba() {
        return `rgba(${this.rgba.r}, ${this.rgba.g}, ${this.rgba.b}, ${this.rgba.a})`;
      },
      emitRgba() {
        return `rgba(${this.colorObj.r}, ${this.colorObj.g}, ${this.colorObj.b}, ${this.colorObj.a})`;
      },
    },
    methods: {
      findWide(count) {
        let el = document.getElementById(`rgbaslider-${this.id}`);
        if (el) {
          this.wide = el.offsetWidth;
        } else {
          setTimeout(() => {
            if (!count || count < 10) {
              this.findWide(count + 1);
            }
            ;
          }, 100);
        }
      },
      assignRgb(rgbObj, assignHsv) {
        Object.assign(this.rgba, Object.assign({}, { ...rgbObj, a: 1 }));
        Object.assign(this.colorObj, Object.assign({}, rgbObj));
        if (assignHsv) {
          this.hsv = RGBtoHSV(rgbObj);
          // console.log('assign rgb hsv gen', this.hsv);
        }
        this.hex = colors.rgbToHex(rgbObj);
        this.invMoveDot();
        this.invMoveOpacity();
        this.invMoveSlider();
        this.emitChange();
      },
      setRgba(val, path) {
        if (path) {
          let v = Math.min(val, 255);
          _set(this.rgba, path, v);
          _set(this.colorObj, path, v);
        } else {
          this.rgba = Object.assign({}, { ...val, a: 1 });
          this.colorObj = Object.assign({}, val);
        }
        let hsv = colors.rgbToHsv(this.colorObj);
        // console.log('set hsv', hsv, rgbObj);
        this.hsv = { h: hsv.h, s: hsv.s, v: hsv.v };
        this.invMoveDot();
        this.invMoveOpacity();
        this.invMoveSlider();
        this.emitChange();
      },
      setHex(val) {
        let v = val;
        let hash = val.indexOf('#');
        if (v && !(hash > -1)) {
          v = '#' + val;
        }
        this.hex = v;
        if (v && v.charAt(6)) {
          let rgbObj = colors.hexToRgb(v);
          this.colorObj = rgbObj.a ? Object.assign({}, rgbObj) : Object.assign({}, { ...rgbObj, a: 1 });
          this.rgba = { ...rgbObj, a: 1 };
          let hsv = colors.rgbToHsv(rgbObj);
          console.log('set hsv', hsv, rgbObj);
          this.hsv = { h: hsv.h, s: hsv.s, v: hsv.v };
          this.invMoveDot();
          this.invMoveOpacity();
          this.invMoveSlider();
          this.emitChange();
        }
      },
      invMoveDot() {
        let rect = document.getElementById(this.id + 'value').getBoundingClientRect();
        let wide = rect.width;
        let high = rect.height;
        // console.log('high invmove dot', high, this.hsv.v, wide, this.hsv.s);
        let l = ((this.hsv.s / 100) * wide) + 'px';
        this.dLeft = l;
        this.dTop = ((1 - (this.hsv.v / 100)) * high) + 'px';
      },
      dotClick(e) {
        e.preventDefault();
        let rect = document.getElementById(this.id + 'value').getBoundingClientRect();
        let top = rect.top;
        let left = rect.left;
        let bottom = rect.bottom;
        let right = rect.right;
        let eLeft = e.clientX;
        let mLeft = eLeft - left;
        let eTop = e.clientY;
        let high = rect.height;
        let mTop = eTop - top;
        // console.log('mtop', high, mTop, eTop, (top - high));
        if (mTop > 0 && eTop <= bottom) {
          this.dTop = mTop + 'px';
          // console.log('got top', mTop / high);
          this.hsv.v = Math.min((((high - mTop) / high) * 100), 100);
        }
        if (mLeft > 0 && eLeft <= right) {
          this.dLeft = mLeft + 'px';
          let wide = rect.width;
          // console.log('got left', mLeft / wide);
          this.hsv.s = Math.min((mLeft / wide) * 100, 100);
        }
        this.colorObj = HSVtoRGB(this.hsv);
        this.hex = colors.rgbToHex(this.colorObj);
        this.emitChange();
      },
      moveDot(e, click) {
        e.preventDefault();
        if (this.mousedown === 'd' || click) {
          let rect = document.getElementById(this.id + 'value').getBoundingClientRect();
          let top = rect.top;
          let left = rect.left;
          let bottom = rect.bottom;
          let right = rect.right;
          let eLeft = e.clientX;
          let mLeft = eLeft - left;
          let eTop = e.clientY;
          let high = rect.height;
          let mTop = eTop - top;
          // console.log('mtop', high, mTop, eTop, (top - high));
          if (mTop > 0 && eTop <= bottom) {
            this.dTop = mTop + 'px';
            // console.log('got top', mTop / high);
            this.hsv.v = Math.min((((high - mTop) / high) * 100), 100);
          }
          if (mLeft > 0 && eLeft <= right) {
            this.dLeft = mLeft + 'px';
            let wide = rect.width;
            // console.log('got left', mLeft / wide);
            this.hsv.s = Math.min((mLeft / wide) * 100, 100);
          }
          this.colorObj = HSVtoRGB(this.hsv);
          this.hex = colors.rgbToHex(this.colorObj);
          this.emitChange();
        }
      },
      invMoveSlider() {
        let rect = document.getElementById(this.id + 'hue').getBoundingClientRect();
        let wide = rect.width;
        this.left = (this.hsv.h / 360) * wide + 'px';
        // console.log('high invmove slider', wide, this.hsv.h, this.left);
      },
      moveSlider(e, click) {
        e.preventDefault();
        // console.log('move', e.clientX, this.mousedown, this.rect.left);
        let rect = document.getElementById(this.id + 'hue').getBoundingClientRect();
        if ((this.mousedown === 'h') || click) {
          let left = rect.left;
          let right = rect.right;
          let eLeft = e.clientX;
          let mLeft = eLeft - left;
          // console.log('lefts', eLeft, left, mLeft, right);
          if (mLeft > 0 && eLeft <= right) {
            this.left = mLeft + 'px';
            let wide = rect.width;
            this.hsv.h = (mLeft / wide) * 360;
            // console.log('h', this.hsv.H);
            this.rgba = HSVtoRGB({ h: this.hsv.h, s: 100, v: 100 });
            this.colorObj = HSVtoRGB(this.hsv);
            this.hex = colors.rgbToHex(this.colorObj);
            this.emitChange();
          }
        }
      },
      invMoveOpacity() {
        let rect = document.getElementById(this.id + 'hue').getBoundingClientRect();
        let wide = rect.width;
        this.oLeft = wide - (wide * this.colorObj.a) + 'px';
        // console.log('high invmove opacity', wide, this.colorObj.a);
      },
      moveOpacity(e, click) {
        let rect = document.getElementById(this.id + 'hue').getBoundingClientRect();
        if (this.mousedown === 'o' || click) {
          let left = rect.left;
          let right = rect.right;
          let eLeft = e.clientX;
          let mLeft = eLeft - left;
          // console.log('lefts', eLeft, left, mLeft, right);
          if (mLeft > 0 && eLeft <= right) {
            let wide = rect.width;
            this.oLeft = mLeft + 'px';
            let opacity = (wide - mLeft) / wide;
            // this.rgba = opacity.toFixed(2);
            this.colorObj.a = opacity < 1 ? opacity.toFixed(2) : opacity.toFixed(0);
            // console.log('h', this.hsv.H);
            this.hex = colors.rgbToHex(this.colorObj);
            this.emitChange();
          }
        }
      },
      limitStr(string, limit) {
        let stringLength = (string && typeof string === 'string') ? string.length : 0;
        if (limit && stringLength && stringLength > limit) {
          return string.substring(0, limit);
        } else return string;
      },
      emitChange() {
        let hex = this.hex;
        if (hex && hex.charAt(7)) {
          hex = this.limitStr(this.hex, 7);
        }
        let alpha = _get(this.emitRgba, 'a', 1);
        let hsl = hsv_to_hsl(this.hsv);
        let val = {
          rgba: this.emitRgba,
          rgbObj: this.colorObj,
          hsv: this.hsv,
          hex: hex,
          hexa: hex.toString(16),
          hsl: hsl,
          hsla: { ...hsl, a: alpha },
          hue: hsl.h,
          hsva: { ...this.hsv, a: alpha },
          alpha: alpha
        };
        if (this.emitTheme) {
          let theme = {
            alpha: alpha,
            hex: hex,
            hexa: val.hexa,
            hsla: val.hsla,
            hsva: val.hsva,
            hue: val.hue,
            rgba: this.emitRgba
          };
          this.$emit('update:model-value', theme);
        } else if (this.emitValue) {
          this.$emit('update:model-value', _get(val, this.optionValue));
        } else this.$emit('update:model-value', val);
      },

      valueChange(val) {
        let newVal = val ? val : this.modelValue;
        // console.log('newVal', newVal);
        let d = document.getElementById(this.id + 'value');
        let dRect = d ? d.getBoundingClientRect() : null;
        let r = document.getElementById(this.id + 'hue');
        let rect = r ? r.getBoundingClientRect() : null;
        if (rect && dRect) {
          if (newVal && ['string', 'object'].includes(typeof newVal)) {
            if (typeof newVal === 'object' && Object.keys(newVal)) {
              let k = Object.keys(newVal);
              if (k.includes('h') && JSON.stringify(newVal) !== JSON.stringify(this.hsv)) {
                this.hsv = Object.assign(newVal);
                let rgbObj = HSVtoRGB(newVal);
                this.assignRgb(rgbObj);
              } else if (k.includes('r') && JSON.stringify(newVal) !== JSON.stringify(this.colorObj)) {
                this.hsv = RGBtoHSV(newVal);
                this.assignRgb(newVal, true);
              }
            } else {
              if (newVal.indexOf('rgb') > -1 && newVal !== this.rgba) {
                let rgbObj = colors.textToRgb(newVal);
                this.assignRgb(rgbObj, true);
              } else {
                let rgbObj;
                let hex = newVal;
                // console.log('hex value change', newVal);
                if (newVal.indexOf('#') > -1) {
                  rgbObj = colors.hexToRgb(newVal);
                  // console.log('rgbObj', rgbObj);
                } else {
                  hex = '#' + newVal;
                  rgbObj = colors.hexToRgb(hex);
                }
                if (rgbObj && hex !== this.hex) this.assignRgb(rgbObj, true);
              }
            }
          }
        } else {
          setTimeout(() => {
            this.valueTries++;
            if (this.valueTries < 6) this.valueChange(newVal);
          }, 300);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .__saturation {
    height: 200px;
    width: 200px;
    border-radius: 4px;
  }

  .__value {
    height: 200px;
    width: 200px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .2);
    position: relative;

    .__value_white {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      border-radius: inherit;
      background: linear-gradient(45deg, white, transparent);
    }

    .__value_black {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      border-radius: inherit;
      background: linear-gradient(0deg, black, transparent);
    }
  }

  .__hue {
    background: linear-gradient(to right, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: visible;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .2);
  }

  .__slider {
    z-index: 1;
    position: absolute;
    top: -5%;
    left: 0;
    cursor: pointer;
    height: 110%;
    max-height: 40px;
    width: 10px;
    border-radius: 4px;
    border: solid 2px black;
    background: #fafafa;
    box-shadow: 0 0 4px rgba(0, 0, 0, .4);
    display: grid;
    align-items: center;
    justify-items: center;
    transform: translate(-50%, 0);
  }

  .__dot {
    position: absolute;
    top: 0;
    left: 100%;
    z-index: 5;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    border: solid 2px white;
    box-shadow: 0 0 0 2px black;
    transform: translate(-50%, -50%);
    cursor: pointer;
  }

  .__checker {
    display: flex;
    align-items: center;
    position: relative;
    overflow: visible;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .2);
    height: 240px;
    background: -webkit-linear-gradient(45deg, rgba(0, 0, 0, 0.0980392) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.0980392) 75%, rgba(0, 0, 0, 0.0980392) 0), -webkit-linear-gradient(45deg, rgba(0, 0, 0, 0.0980392) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.0980392) 75%, rgba(0, 0, 0, 0.0980392) 0), white;
    background: -moz-linear-gradient(45deg, rgba(0, 0, 0, 0.0980392) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.0980392) 75%, rgba(0, 0, 0, 0.0980392) 0), -moz-linear-gradient(45deg, rgba(0, 0, 0, 0.0980392) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.0980392) 75%, rgba(0, 0, 0, 0.0980392) 0), white;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.0980392) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.0980392) 75%, rgba(0, 0, 0, 0.0980392) 0), linear-gradient(45deg, rgba(0, 0, 0, 0.0980392) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.0980392) 75%, rgba(0, 0, 0, 0.0980392) 0), white;
    background-repeat: repeat, repeat;
    background-position: 0px 0, 5px 5px;
    -webkit-transform-origin: 0 0 0;
    transform-origin: 0 0 0;
    -webkit-background-origin: padding-box, padding-box;
    background-origin: padding-box, padding-box;
    -webkit-background-clip: border-box, border-box;
    background-clip: border-box, border-box;
    -webkit-background-size: 10px 10px, 10px 10px;
    background-size: 10px 10px, 10px 10px;
    -webkit-box-shadow: none;
    text-shadow: none;
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
    -webkit-transform: scaleX(1) scaleY(1) scaleZ(1);
    transform: scaleX(1) scaleY(1) scaleZ(1);

    .__o_fill {
      position: absolute;
      top: 0;
      left: 0;
      border-radius: inherit;
      height: 100%;
      width: 100%;
      z-index: 0;
    }
  }

</style>
