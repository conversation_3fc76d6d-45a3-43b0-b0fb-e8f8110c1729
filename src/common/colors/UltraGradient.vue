<template>
  <div class="q-px-sm"
       :style="{ ...styleIn, position: 'relative', borderRadius: '10px', paddingTop: '100px', height: '100%' }">
    <div v-if="gradientOn" class="__display_bar" :style="{ background: gradient ? gradient : useGradient }"></div>
    <q-card class="q-pa-sm" style="width: 100%; height: 100%; border-radius: 10px">
      <div v-if="gradientOn" style="width: 100%; padding: 5px; background: rgba(255,255,255,.9)">
        <div :id="id + 'gradient'" class="__gradient_bar" :style="{ background: useGradient, borderRadius: '6px' }"
             @click.stop="colorClick">
          <div
            v-for="(sec, i) in form?.sections || []" :key="`sec-handle-${i}`"
            :id="`${id}-handle-${i}`"
            @click.stop=""
            class="__handle"
            @mousedown="mousedown = i, idx = i"
            @touchstart="mousedown = i, idx = i"
            :style="{
          background: sec.color,
          left: sec.stop + '%'
        }"
          >
            <div style="height: 100%; width: 100%; position: relative; overflow: visible">
              <div class="__counter text-xxs text-mb-xxs text-weight-bold"
                   :style="idx === i ? { boxShadow: '0 0 12px rgba(0,0,0,.35)' } : {}">{{ sec.stop.toFixed(0) }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <q-separator class="q-my-lg"></q-separator>
      <div class="row" style="width: 100%; background: white; border-radius: 8px">
        <div :class="`col-12 col-md-${!useGradient ? '12' : '6'}`">
          <div class="row justify-center">

            <rgba-slider
              stack
              :startValue="startValue"
              :modelValue="goGet(form, ['sections', idx, 'color'])"
              @update:model-value="gradientOn ? goSet(form, ['sections', idx, 'color'], $event) : $emit('input', $event)"
            ></rgba-slider>
          </div>
        </div>
        <div class="col-12 col-md-6" style="border-left: solid .4px rgba(0,0,0,.2)" v-if="gradientOn">
          <div class="__options q-pa-md">
            <div style="max-height: 100%; overflow-y: scroll">
              <div class="row">
                <div style="width: 20%"></div>
                <div style="width: 40%" class="q-px-xs">
                  <div class="text-center text-xxxs text-mb-xxxs text-weight-bolder text-uppercase text-ir-grey-6">hex
                  </div>
                </div>
                <div style="width: 20%" class="q-px-xs">
                  <div class="text-center text-xxxs text-mb-xxxs text-weight-bolder text-uppercase text-ir-grey-6">stop
                  </div>
                </div>
                <div style="width: 20%"></div>
              </div>
              <div v-for="(sec, i) in goGet(form, 'sections', [])" :key="`sec-${i}`" @click="idx = i">
                <hex-row
                  :on="idx === i"
                  v-model="sec.color"
                  gradient
                  @close="goGet(form, ['sections', 1]) ? form.sections.splice(i, 1) : ''" :stop-in="sec.stop"
                  @stop="sec.stop = $event"
                ></hex-row>
              </div>
            </div>
            <div style="border-top: solid .4px rgba(0,0,0,.2); height: 100%">
              <div class="row items-center">
                <div class="q-px-sm">
                  <degree-picker v-model="form.deg"></degree-picker>
                </div>
                <div style="width: 40px">
                  <q-input dense input-class="text-center text-xxs text-mb-xxs text-weight-bold text-ir-grey-8"
                           v-model.number="form.deg"></q-input>
                  <div class="text-center text-xxxs text-mb-xxxs text-weight-bolder text-uppercase text-ir-grey-6">Deg
                  </div>
                </div>
                <q-space></q-space>
                <q-btn-group rounded push>
                  <q-btn size="sm" :flat="form.type !== 'linear-gradient'" :push="form.type === 'linear-gradient'"
                         @click="form.type = 'linear-gradient'"
                         :color="form.type !== 'linear-gradient' ? 'ir-grey-6' : 'ir-grey-8'"
                         icon="mdi-trending-neutral"
                         label="linear">
                  </q-btn>
                  <q-btn :flat="form.type !== 'radial-gradient'" size="sm" :push="form.type === 'radial-gradient'"
                         @click="form.type = 'radial-gradient'"
                         :color="form.type !== 'radial-gradient' ? 'ir-grey-6' : 'ir-grey-8'" label="radial"
                         icon="mdi-circle-double"></q-btn>
                </q-btn-group>


              </div>

            </div>
          </div>
        </div>
      </div>
      <div style="height: 50px; border-top: solid .4px rgba(0,0,0,.2)" class="row items-center q-py-sm">
        <link-card :url="gradient" v-if="gradientOn"/>
        <q-space></q-space>

        <q-btn glossy style="background: #01014A;  color: white" v-if="!autoSave" push label="save"
               @click="emitChange(true)"></q-btn>

      </div>
    </q-card>
  </div>
</template>

<script>
  import RgbaSlider from './RgbaSlider.vue';
  import HexRow from './HexRow.vue';
  import {combineColors} from './utils';
  import {colors} from 'quasar';
  import DegreePicker from './DegreePicker.vue';
  import LinkCard from '../links/LinkCard.vue';
  import { _get, _set } from 'symbol-syntax-utils';

  const arrMove = (arr, fromIndex, toIndex, obj) => {
    let cloneArr = JSON.parse(JSON.stringify(arr));
    cloneArr.splice(fromIndex, 1);
    cloneArr.splice(toIndex, 0, obj);
    return cloneArr;
  };

  export default {
    name: 'UltraGradient',
    components: { LinkCard, DegreePicker, HexRow, RgbaSlider },
    props: {
      emitString: Boolean,
      autoSave: Boolean,
      id: { type: String, default: 'ultra' },
      styleIn: {
        type: Object, default: () => {
          return { width: '900px', 'max-width': '100%' };
        }
      },
      noGradient: Boolean,
      modelValue: { type: String, default: 'linear-gradient(90deg, #e35927, #00b4e9)' }
    },
    mounted() {
      document.addEventListener('mouseup', () => {
        this.mousedown = -1;
      });
      document.addEventListener('touchend', () => {
        this.mousedown = -1;
      });
      const setElListeners = (el, count = 0) => {
        if(el) {
          el.addEventListener('mousemove', (e) => {
            this.moveSlider(e);
          });
          el.addEventListener('touchmove', (e) => {
            this.moveSlider(e);
          });
        } else setTimeout(() => {
          console.log('no el', count);
          if(!count || count < 10){
            setElListeners(el, count ? count++ : 1);
          }
        }, 200);
      };

      setElListeners(document.getElementById(this.id + 'gradient'));


      if (this.modelValue)
        if (typeof this.modelValue === 'string') {
          setTimeout(() => {
            this.parseValue(this.modelValue);
          }, 600);
        } else Object.assign(this.form, Object.assign({}, this.modelValue));
    },
    data() {
      return {
        goGet: _get,
        goSet: _set,
        gradientOn: true,
        mousedown: -1,
        idx: 0,
        startValue: '#e35927',
        form: {
          deg: 270,
          type: 'linear-gradient',
          sections: [
            { color: 'rgba(227,39,96,1)', stop: 0 },
            { color: 'rgba(0,170,232,1)', stop: 100 }
          ]
        }
      };
    },
    watch: {
      noGradient: {
        immediate: true,
        handler(newVal) {
          this.gradientOn = !newVal;
        }
      },
      idx: {
        immediate: true,
        handler(newVal) {
          this.startValue = _get(this.form, ['sections', newVal, 'color']);
        }
      },
    },
    computed: {
      useGradient() {
        if (_get(this.form, ['sections', 1])) {
          let str = 'linear-gradient(90deg, ';
          this.form.sections.forEach((section, i) => {
            str += (section.color + ' ' + section.stop + '%' + (i < this.form.sections.length - 1 ? ', ' : ')'));
          });
          return str;
        } else return _get(this.form, ['sections', 0, 'color'], 'red');
      },
      gradient() {
        if (_get(this.form, ['sections', 1])) {
          let str = `${this.form.type}(${this.form.deg}deg, `;
          if (this.form.type === 'radial-gradient') {
            str = 'radial-gradient(circle, ';
          }
          this.form.sections.forEach((section, i) => {
            str += (section.color + ' ' + section.stop + '%' + (i < this.form.sections.length - 1 ? ', ' : ')'));
          });
          return str;
        } else return _get(this.form, ['sections', 0, 'color'], 'red');
      }
    },
    methods: {
      colorClick(e) {
        if (!(this.mousedown > -1)) {
          e.preventDefault();
          console.log('clicked', e.clientX);
          let rect = document.getElementById(this.id + 'gradient').getBoundingClientRect();
          console.log('rect', rect);
          let left = rect.left;
          let eLeft = e.clientX;
          let mLeft = eLeft - left;
          let wide = rect.width;
          let percent = (mLeft / wide) * 100;
          let sectionStops = _get(this.form, 'sections', []).map(a => a.stop);
          console.log('section stops', sectionStops, percent);
          let priorIdx = -1;
          let nextIdx = -1;
          for (let i = 0; i < 1; i++) {
            console.log('for loop', i);
            if (!(sectionStops[i] > percent)) {
              priorIdx = i;
              nextIdx = i + 1;
              break;
            }
          }
          console.log('colors', priorIdx, nextIdx);
          let color = _get(this.form, ['sections', 0, 'color']);
          let color1;
          let color2;
          if (priorIdx > -1) {
            color1 = _get(this.form, ['sections', priorIdx, 'color']);
            if (nextIdx > -1) color2 = _get(this.form, ['sections', nextIdx, 'color']);
          }
          if (color1 && color2) {
            color = combineColors(colors.textToRgb(color1), colors.textToRgb(color2));
          }
          this.form.sections.splice(priorIdx + 1, 0, { color: color, stop: percent });
          setTimeout(() => {
            this.idx = priorIdx + 1;
            this.emitChange();
          }, 200);
        }
      },
      emitChange(test) {
        if (this.autoSave || test) {
          if (this.emitString) {
            this.$emit('input', this.gradient);
          } else this.$emit('input', this.form);
        }
      },
      moveSlider(e) {
        e.preventDefault();
        if (this.mousedown > -1) {
          let rect = document.getElementById(this.id + 'gradient').getBoundingClientRect();
          let left = rect.left;
          let right = rect.right;
          let eLeft = e.clientX;
          let mLeft = eLeft - left;
          if (mLeft > 0 && eLeft <= right) {
            let wide = rect.width;
            let percent = (mLeft / wide) * 100;
            _set(this.form, ['sections', this.mousedown, 'stop'], Math.floor(percent));
            let obj = Object.assign({}, _get(this.form, ['sections', this.mousedown], 0));
            let prior = this.mousedown > 0 ? _get(this.form, ['sections', this.mousedown - 1, 'stop']) : 0;
            let next = this.mousedown < this.form.sections.length - 1 ? _get(this.form, ['sections', this.mousedown + 1, 'stop']) : 100;
            if (percent < prior) {
              // obj.stop -= 1;
              this.mousedown = -1;
              this.form.sections = arrMove(this.form.sections, this.mousedown, this.mousedown - 1, obj);
            } else if (percent > next) {
              // obj.stop += 1;
              this.mousedown = -1;
              this.form.sections = arrMove(this.form.sections, this.mousedown, this.mousedown + 1, obj);
            }
            this.emitChange();
          }
        }
      },
      limitStr(string, limit) {
        let stringLength = (string && typeof string === 'string') ? string.length : 0;
        if (limit && stringLength && stringLength > limit) {
          return string.substring(0, limit);
        } else return string;
      },
      parseValue(newVal) {

        const parseStop = str => {
          let n = 0;
          let stopIndex = str.indexOf('%');
          console.log('parse stop', str, stopIndex);
          if (stopIndex > -1) {
            let front = str?.split('%')[0];
            let chunks = front?.split(' ') || [];
            let percent = chunks[chunks.length - 1];
            let num = parseInt(percent);
            console.log('parts', num);
            if (num && typeof num === 'number') n = num;
          }
          return n;
        };

        const parseColor = (str) => {
          let s = '';
          let n = parseStop(str);
          console.log('parsing color', str, n);
          if (str.indexOf('rgba') > -1) {
            let nums = str?.split('(')[1];
            let iso = nums?.split(')')[0];
            let pref = str?.split('(')[0];
            if (nums && iso && pref) {
              s = pref + '(' + iso + ')';
              console.log('assigning s', s, n);
            }
          } else if (str.indexOf('#') > -1) {
            let nums = str?.split('#')[1];
            let iso = nums?.split(' ')[0];
            if (nums && iso) {
              s = '#' + iso;
            }
          }
          console.log('returning color', s);
          return { color: s, stop: n };
        };

        let type = newVal?.split('(')[0];
        let one = newVal?.split('(')[1];
        let deg = one?.split(',')[0]; //90deg

        if (deg?.indexOf('deg') > -1) {
          this.form.deg = parseInt(deg);
        }
        this.type = type;

        let sections = [];

        //TODO: won't handle 'rgb' modelValues only 'rgba'
        newVal.split('rgba').forEach((str, i) => {
          if (i > 0) {
            let nums = str.split('(')[1];
            let iso = nums.split(')')[0];
            let chunk = nums.split(')')[1];
            let percent = chunk.split(',')[0];
            let p = percent ? ' ' + percent : ' ' + (100 - (100 / (i + 1))) + '%';
            let c = parseColor('rgba(' + iso + ')' + p);
            if (c.color) sections.push(c);
            console.log('pushed to section', i, sections);
          }
        });
        newVal.split('#').forEach((str, i) => {
          if (i > 0) {
            let limit = this.limitStr(str, 6);
            let p = (100 - (100 / (i + 1))) + '%';
            let per = str.split('%');
            if (_get(per, [1])) {
              let q = per.split(' ')[1];
              if (q) p = q + '%';
            }
            let c = parseColor('#' + parseInt(limit.trim()) + p);
            if (c.color) sections.push(c);
          }
        });
        if (_get(sections, [0])) {
          this.form.sections = JSON.parse(JSON.stringify(sections));
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .__gradient_bar {
    position: relative;
    width: 100%;
    height: 50px;
    //box-shadow: 0 0 8px rgba(0, 0, 0, .2);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 1);
    border-radius: 6px;
    border: solid 2px white;

    .__handle {
      height: 120%;
      position: absolute;
      top: -10%;
      cursor: pointer;
      width: 10px;
      border-radius: 5px;
      border: solid 3px white;
      box-shadow: 0 0 0 1px black;
      transform: translate(-50%, 0);
      overflow: visible;

      .__counter {
        position: absolute;
        bottom: 0;
        left: 0;
        background: white;
        transform: translate(-50%, 130%);
        padding: 4px 8px;
        border-radius: 3px;
        border: solid 1px rgba(66, 66, 66, .5);
        box-shadow: 0 0 4px rgba(66, 66, 66, .2);
      }
    }
  }

  .__display_bar {
    height: 200px;
    width: 100%;
    border-radius: 10px;
    z-index: 0;
    position: absolute;
    top: 0;
    left: 0;
  }

  .__options {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: 80% minmax(100px, 20%);
  }
</style>
