<template>
  <div>
    <div class="__rd">
      <div class="font-1r text-weight-bold">{{title}}</div>
      <div class="font-7-8r">{{message}}</div>

      <div class="row justify-end q-pt-md">
        <q-btn size="sm" flat icon="mdi-close" color="black" label="Cancel" @click="on = false, $emit('remove', false)"></q-btn>
        <q-btn size="sm" flat icon="mdi-delete" color="negative" label="Delete" @click="remove"></q-btn>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { Notify } from 'quasar';
  import {ref} from 'vue';

  const props = defineProps({
    title: { default: 'Are you sure you want to delete this?'},
    message: { type: String },
    store: { required: true },
    item: { required: true },
    modelValue: Boolean,
    removedMessage: { default: 'Deleted' }
  })
  const emit = defineEmits(['remove']);

  const on = ref(false);

  const remove = async () => {
    const removed = await props.store.remove(props.item?._id || props.item)
        .catch(err => {
          Notify.create({
            message: err.message,
            color: 'negative',
            position: 'top',
            icon: 'mdi-alert-circle',
            timeout: 10000,
            actions: [{icon: 'mdi-close', color: 'white'}],
          });
        });

    if(removed){
      emit('remove', removed);
      Notify.create({
        message: props.removedMessage,
        color: 'positive',
        position: 'bottom',
        icon: 'mdi-alert-circle',
        timeout: 2000,
        actions: [{icon: 'mdi-close', color: 'white'}],
      });
    }
    on.value = false;
  }
</script>

<style lang="scss" scoped>
  .__rd {
    width: 100%;
    max-width: 400px;
    padding: 18px;
    border-radius: 6px;
    background: white;
    //box-shadow: 0 0 10px rgba(0,0,0,.6);
  }
</style>
