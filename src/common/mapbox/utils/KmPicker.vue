<template>
  <q-chip
      v-bind="{
    class: 'tw-six',
    clickable: true,
    color: 'white',
    label: `${kmToMi(modelValue)} mi`,
    iconRight: 'mdi-menu-down',
    ...chipAttrs
      }"
  >
    <q-menu>
      <q-list dense separator>
        <q-item v-for="i in miles" :key="`mile-${i}`" clickable @click="emitUp(i)">
          <q-item-label>{{i}}</q-item-label>
        </q-item>
      </q-list>
    </q-menu>
  </q-chip>
</template>

<script setup>
  import {kmToMi, miToKm} from 'src/utils/geo-utils';
  import {ref} from 'vue';

  const emit = defineEmits(['update:model-value', 'update:km', 'update:miles'])
  const props = defineProps({
    chipAttrs: Object,
    modelValue: Number,
    limit: { type: Number, default: 1000 }
  })
  const ms = () => {
    let list = [1,2,3,4,5,10,15,20,25];
    for(let i = 11; i < 70; i++){
      if(i < 29) list.push((i - 10) * 10 + 20);
      else list.push((i - 29) * 50 + 250)
    }
    return list.filter(a => a <= props.limit);
  };
  const miles = ref(ms());

  const emitUp = (mi) => {
    emit('update:miles', mi);
    emit('update:model-value', miToKm(mi));
  }
</script>

<style lang="scss" scoped>

</style>
