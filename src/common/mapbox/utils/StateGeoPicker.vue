<template>
  <state-picker v-bind="{modelValue: multiple ? modelValue?.length ? modelValue : undefined : modelValue, ...attrs}"
                @update:model-value="setState">
    <template v-slot:append>
      <q-spinner v-if="loading" color="primary"></q-spinner>
    </template>
  </state-picker>
</template>

<script setup>
  import StatePicker from 'components/common/geo/pickers/StatePicker.vue';

  import {useJunkDrawers} from 'stores/junk-drawers';
  import {useAttrs, ref} from 'vue';

  const attrs = useAttrs();
  const store = useJunkDrawers();

  const emit = defineEmits(['update:model-value', 'update:geo']);
  const props = defineProps({
    modelValue: { required: true },
    multiple: Boolean
  })

  const loading = ref(false);

  const to = ref(false)
  const setState = async (val) => {
    if (!to.value) {
      to.value = true;
      setTimeout(() => {
        to.value = false;
      }, 500)
      loading.value = true;
      const state = (Array.isArray(val) ? val[0] : val).toLowerCase();
      const jd = await store.find({ query: { $limit: 1, itemId: `states|${state}` } })
          .catch(err => {
            console.error(`Error finding state: ${err.message}`);
            loading.value = false;
          })
      loading.value = false;
      if (jd.data) {
        emit('update:model-value', props.multiple ? [...props.modelValue || [], val] : val);
        emit('update:geo', jd.data[0].data.geo);
      }
    }
  }

</script>

<style lang="scss" scoped>

</style>
