<template>
  <div style="position: absolute; top: 0; left: 0; height: 100%; width: 100%; border-radius: inherit; overflow: hidden">
    <div style="width: 100%; height: 100%; position: relative" :id="id" :class="id">
    </div>
  </div>
</template>

<script setup>
  import MapboxDraw from '@mapbox/mapbox-gl-draw';

  import mapboxgl from 'mapbox-gl/dist/mapbox-gl.js';
  import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';
  import {_get} from 'symbol-syntax-utils';
  import {computed, onMounted, ref, watch} from 'vue';

  const emit = defineEmits(['mapTouch', 'poly', 'update:active-item', 'selected', 'ready'])
  const props = defineProps({
    id: {
      type: String,
      default: 'map'
    },
    activeItem: Number,
    fitBounds: Boolean,
    center: {
      type: [Object, Array],
      default: function () {
        return [-111.876183, 40.758701];
      }
    },
    maxZoom: { type: Number, default: 15 },
    boundZoom: { type: Number, default: 9 },
    zoom: {
      type: Number,
      default: 7
    },
    //TODO: make custom marker a config object
    customMarker: { required: false },
    fill_color: {
      type: String,
      default: '#ff4577'
    },
    fill_outline_color: {
      default: '#000000'
    },
    autoZoom: {
      type: Boolean,
      default: true,
    },
    log: Boolean,
    zoomToPoly: Boolean,
    styleId: { type: String, default: 'mapbox://styles/mapbox/streets-v11' },
    markerDrag: Boolean,
    geoIn: Object,
    markersIn: Array,
    polygons: { type: Boolean, default: true },
    drawing: Boolean,
    fullScreen: Boolean,
    initOptions: {
      geoIn: undefined,
      activeItem: undefined,
      flatPoints: undefined,
      center: undefined
    },
    selected: Number,
    hoverOn: {
      type: Boolean,
      default: true
    },
    popupOn: Boolean
  })

  const map = ref({});
  const loaded = ref(false);
  const draw = ref(null);
  const mountTries = ref(0);
  const markers = ref([]);
  const marker = ref(null);
  const hoverId = ref('');
  const layerName = ref('poly');

  const showPolygons = (geo, rmv) => {
    if (rmv && layerName.value && map.value.removeLayer) {
      map.value.removeLayer(layerName.value);
    }
    if (geo) {
      geo.type = 'FeatureCollection';
      geo.features = geo.features?.map((b, i) => {
        return {
          ...b,
          id: `marker-${i}`
        }
      })

      // console.log('show', a);
      layerName.value = _get(geo, 'name', 'poly');

      if (map.value) {
        if (props.log) console.log('map', map.value, geo);
        const srcId = `src-${layerName.value}`;
        const currentSource = map.value.getSource ? map.value.getSource(srcId) : undefined;
        if (rmv) {
          if (currentSource) map.value.removeSource(srcId);
        }
        if ((rmv || !currentSource) && map.value.addSource) {
          try {
            map.value.addSource(srcId, {
              'type': 'geojson',
              'data': geo
            });
          } catch (e) {
            console.log(`Error adding geo source: ${e.message}`)
          } finally {
            if (props.log) console.log('did we add a source?')
          }
        }

        if (map.value.addLayer) {
          try {
            map.value.addLayer({
              'id': layerName.value,
              'type': 'fill',
              'source': `src-${layerName.value}`,
              'layout': {},
              'paint': {
                'fill-color': geo.fillColor || props.fill_color,
                'fill-outline-color': geo.fillOutlineColor || props.fill_outline_color,
                'fill-opacity': [
                  'case',
                  ['boolean', ['feature-state', 'hover'], false],
                  .7,
                  0.5
                ]
              }
            });
          } catch (e) {
            console.log(`Error adding geo layer: ${e.message}`)
          } finally {
            if (props.log) console.log('did we add a layer?')
          }
        }
        addTouchListener(layerName.value);
        // }
      }
    }
  }

  const hoverTimeout = ref(undefined);
  const toggleHover = (srcId, val, elm) => {
    if(elm) {
      elm.style.zIndex = val ? 100 : 1
      elm.style.transform = val ? 'scale(1.5)' : 'none'
    }
    clearTimeout(hoverTimeout.value);
    hoverTimeout.value = undefined;
    if(!val){
      hoverTimeout.value = setTimeout(() => {
        if(hoverId.value === srcId) {
          hoverId.value = -1;
          emit('update:active-item', -1);
        }
      }, 100)
    }
    if (val && map.value.setFeatureState) {
      emit('update:active-item', srcId);
      if (hoverId.value || hoverId.value === 0) {
        map.value.setFeatureState(
            { source: `src-${layerName.value}`, id: `marker-${hoverId.value}` },
            { hover: false }
        );
      }
      hoverId.value = srcId;
      map.value.setFeatureState(
          { source: `src-${layerName.value}`, id: `marker-${hoverId.value}` },
          { hover: true }
      );
    } else {
      map.value.setFeatureState(
          { source: `src-${layerName.value}`, id: `marker-${hoverId.value}` },
          { hover: false }
      );
    }
  }

  const updateArea = () => {
    emit('poly', draw.value.getAll())
  };

  const setMapCenter = (val) => {
    if (map.value && typeof map.value !== 'undefined') {
      if (map.value.setCenter) map.value.setCenter(val);
    } else {
      setTimeout(() => setMapCenter(val), 200);
    }
  };

  const markerDragEnd = (marker) => {
    emit('pin', marker.getLngLat());
  };

  const addTouchListener = (id) => {
    if (map.value?.on) {
      const addListener = (listenerOn, listenerOff, srcId) => {
        map.value.on(listenerOn, srcId, e => {
          const el = document.getElementById(srcId);
          if(el) el.style.zIndex = 100;
          if (e.features.length > 0) {
            const sendId = String((_get(e, `features[${0}].id`, 'marker-0') || 'marker-0'))?.split('marker-')[0];
            hoverId.value = sendId;
            emit('update:active-item', sendId)
            // console.log('hover id', sendId);
            toggleHover(sendId, true);
          }
        });

        map.value.on(listenerOff, srcId, () => {
          const el = document.getElementById(srcId);
          if(el) el.style.zIndex = undefined;
          if (hoverId.value || hoverId.value === 0) {
            toggleHover(hoverId.value, false);
          }
          hoverId.value = null;
        });
      };
      addListener('touchstart', 'touchend', id);
      addListener('mousemove', 'mouseleave', id);
      map.value.on('click', id, e => {
        // console.log('map click', e);
        emit('mapClick', e);
      });
    }
  }

  const setMapMarkers = async (val) => {
    let mp = map.value
    if (mp && typeof mp !== 'undefined') {
      const rmv = async (v) => {
        if(v?.remove) await v.remove()
      }
      await Promise.all((markers.value || []).map(a => rmv(a)))
      let list = [];
      val.forEach((a, i) => {
        if(Array.isArray(a)) a = { lngLat: a }
        // eslint-disable-next-line no-console
        // console.log('setting marker', a, map);
        let obj = { color: 'var(--q-primary)', id: a.id || `marker-${i}`, draggable: props.markerDrag };
        if(a.el) {
          let el = document.createElement('div');
          el.draggable = props.markerDrag;
          obj = a.el(el, a);
        } else if (props.customMarker) {
          let el = document.createElement('div');
          el.draggable = props.markerDrag;
          obj = props.customMarker({ el, ...a })
        }
        let marker = new mapboxgl.Marker(obj)
            .setLngLat(a.lngLat)
            .addTo(map.value);
        list.push(marker);
        marker.on('dragend', () => {
          markerDragEnd(marker);
        });
        const el = marker.getElement();
        if (el) {
          el.addEventListener('pointerenter', () => toggleHover(i, true, el))
          el.addEventListener('pointerleave', () => toggleHover(i, false, el))
          el.addEventListener('click', (e) => {
            e.preventDefault();
            emit('update:selected', i)
          })
        }
      });
      markers.value = list;
      // if (list && list.length > 1) {
      //   this.setBoundingView(val)
      // }
    } else {
      mountTries.value++;
      if (mountTries.value < 10) {
        setTimeout(() => setMapMarkers(val), 200);
      } else console.error('Unable to display map');
    }
  }

  const setBoundingView = (val) => {
    // eslint-disable-next-line no-console
    if (props.log) console.log('fitting bounds', val);
    // Pass the first coordinates in the LineString to `lngLatBounds` & wrap each coordinate pair in `extend` to include them in the bounds
    const bounds = val.reduce(function (bounds, coord) {
      return bounds.extend(coord);
    }, new mapboxgl.LngLatBounds(val[0], val[0]));

    setTimeout(() => {
      if (!val || val.length === 0) {
        console.error('Invalid bounds input: val must be a non-empty array of coordinates');
      } else if (map.value?.fitBounds) map.value.fitBounds(bounds, {
        padding: 100,
        maxZoom: props.boundZoom || props.maxZoom
      });
    }, 50);
  };

  const pointList = computed(() => {
    const ftrs = _get(props.geoIn, 'features', []);
    if (props.markersIn) return props.markersIn.map((a, i) => {
      if(typeof a === 'object') return a;
      else return {
        lngLat: a,
        id: `marker-${i}`
      }
    });
    else {
      const geo = ftrs.map(a => a.geometry || a).filter(b => b.type === 'Point');
      return geo;
    }
  })

  const flatPoints = computed(() => {
    const ftrs = _get(props.geoIn, 'features', []);
    if (props.markersIn && (!props.zoomToPoly || !ftrs?.length)) return props.markersIn.map(a => Array.isArray(a) ? a : a?.lngLat);
    else {
      const geo = _get(props.geoIn, 'features', []).map(a => a.geometry || a);
      let list = [];
      geo.forEach(a => {
        if (a?.type === 'Point') {
          list.push(a.coordinates);
        } else {
          // eslint-disable-next-line no-console
          // console.log('pushing coord', a);
          (a.coordinates || [])[0]?.forEach(coord => {
            list.push(coord);
          });
        }
      });
      // return [].concat.apply([], geo)c
      return list;
    }
  })

  const setView = () => {
    setTimeout(() => {
      if (props.autoZoom) setBoundingView(flatPoints.value);
    }, 300);
    if (pointList.value?.length) setMapMarkers(pointList.value);
  }

  watch(() => props.drawing, (nv, ov) => {
    if (nv !== ov && ov !== undefined) initMap(0)
  })

  watch(() => props.geoIn, (nv, ov) => {
    if (loaded.value) {
      if (!map.value) initMap(0);
      const rmv = !!map.value.getLayer(layerName.value);
      showPolygons(nv, rmv);
    }
  }, { immediate: true })

  watch(() => props.activeItem, (nv, ov) => {
    if ((nv || nv === 0) && nv > -1) {
      hoverId.value = `marker-${nv}`;
      toggleHover(nv, true);
    }
    if ((ov || ov === 0) && ov > -1) {
      toggleHover(ov, false);
    }
  }, { immediate: true })

  watch(flatPoints, (nv, ov) => {
    if (nv?.length !== ov?.length && _get(nv, [0])) {
      setTimeout(() => {
        if (!map.value) initMap(0);
        else {
          setView()
        }
      }, 200)
    }
  }, { immediate: true })

  watch(() => props.center, (nv) => {
    if (nv && typeof nv !== 'undefined') {
      setTimeout(() => {
        if (!map.value) initMap(0);
        else {
          setMapCenter(nv)
        }
      }, 200)
    }
  }, { immediate: true })

  const initiating = ref(false);
  const initMap = (tries = 0) => {
    mapboxgl.accessToken = 'pk.eyJ1IjoidHlsZXJoYWxsc2dsb3J5IiwiYSI6ImNsM2xuNHAyaDAwMm8zam83NXNwN29yajUifQ.kJdXkWNOnZ3jFBzoOwXs0w';
    const container = document.getElementById(props.id)
    if (!initiating.value && container) {
      initiating.value = true
      try {
        map.value = new mapboxgl.Map({
          container,
          style: props.styleId,
          center: props.center,
          zoom: props.zoom,
          maxZoom: props.maxZoom
        })
      } catch (err) {
        initiating.value = false
        console.log('error initing map', err);

      } finally {
        initiating.value = false
        if (map.value?.on) {
          if (flatPoints.value && flatPoints.value[0]) setView();
          if (props.center) setMapCenter(props.center);
          map.value.on('click', (e) => {
            if (props.log) console.log('map click');
            emit('mapTouch', e);
          });

          map.value.on('load', () => {
            loaded.value = true;
            showPolygons(props.geoIn);
            if (props.fullScreen) {
              map.value.addControl(new mapboxgl.FullscreenControl());
            }
            if (props.drawing) {
              draw.value = new MapboxDraw({
                displayControlsDefault: false,
                controls: {
                  polygon: true,
                  trash: true
                }
              });
              map.value.addControl(draw.value);
              map.value.on('draw.create', updateArea);
              map.value.on('draw.delete', updateArea);
              map.value.on('draw.update', updateArea);
            }
            emit('ready', map.value)
          })
        } else setTimeout(() => {
          if (tries < 10) initMap(tries + 1)
        }, 2000)
      }
    }

  }

  onMounted(() => {
    initMap(0);
  })
</script>

<style lang="scss" scoped>
  @import url("https://api.mapbox.com/mapbox-gl-js/v1.12.0/mapbox-gl.css");
  @import url("https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v2.2.0/mapbox-gl-geocoder.css");

  .map {
    height: 100%;
    width: 100%;
  }

  .mapbox-ctrl {
    color: #1c1c1c;
  }

  .mapboxgl-marker {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    border: 1px solid gray;
    background-color: var(--q-secondary);
    transform: none !important;
  }

  .mapbox-gl-draw_polygon {
    background: #1c1c1c !important;
  }

  /*marker {*/
  /*  background: rgba(0, 0, 0, 0.5);*/
  /*  color: #fff;*/
  /*  position: absolute;*/
  /*  bottom: 40px;*/
  /*  left: 10px;*/
  /*  padding: 5px 10px;*/
  /*  margin: 0;*/
  /*  font-size: 11px;*/
  /*  line-height: 18px;*/
  /*  border-radius: 3px;*/
  /*  display: none;*/
  /*}*/
</style>
