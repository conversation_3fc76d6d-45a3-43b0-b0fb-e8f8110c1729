<template>
  <q-layout>
    <template v-if="$route.name === 'policy'">
      <div class="__policy">
        <div class="__cent">
          <div class="row justify-center">
            <div class="col-6 col-md-4 q-pa-md">
              <q-card bordered @click="go('terms-of-use')" class="flex flex-center q-pa-lg text-center cursor-pointer">
                <div>Terms of Use</div>
              </q-card>
            </div>
            <div class="col-6 col-md-4 q-pa-md">
              <q-card bordered @click="go('privacy-policy')" class="flex flex-center q-pa-lg text-center cursor-pointer">
                <div>Privacy Policy</div>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </template>
    <router-view></router-view>
  </q-layout>
</template>

<script>
  export default {
    name: 'PolicyPage',
    props: {
      business: Object
    },
    methods: {
      go(name){
        let { query } = this.$route;
        this.$router.push({ name, query });
      }
    }
  };
</script>

<style scoped>
  .__policy {
    width: 100%;
    display: flex;
    justify-content: center;
    min-height: 80vh;
    align-items: center;
  }

  .__cent {
    width: 800px;
    max-width: 100%;
    padding: 1rem;
  }
</style>
