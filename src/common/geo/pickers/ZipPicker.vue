<template>
  <q-chip v-bind="{ clickable: true, iconRight: 'mdi-menu-down', color: 'ir-bg2', ...$attrs, label: undefined }">
    <slot name="left"></slot>
    <slot name="default">
      <span>{{ label || modelValue || 'Choose Zip Code' }}</span>
    </slot>
    <slot name="right"></slot>
    <q-menu>
      <div class="w300 bg-white">
        <div class="q-pa-sm">
          <q-input @keyup.enter="checkZip" :model-value="zipSearch" @update:model-value="setZipSearch"
                   placeholder="Enter Zip...">
            <template v-slot:prepend>
              <q-spinner color="primary" v-if="zipLoading"></q-spinner>
              <q-icon v-else name="mdi-magnify"></q-icon>
            </template>
            <template v-slot:append v-if="zipSearch?.length === 5 && zipSearch !== modelValue">
              <q-btn dense round push icon="mdi-check" color="green" @click="checkZip"></q-btn>
            </template>
          </q-input>
        </div>
        <q-list separator dense>
          <q-item clickable @click="setZip(zip)" v-for="(zip, i) in useZips || []" :key="`zip-${i}`">
            <q-item-section>
              <q-item-label>{{ zip }}</q-item-label>
            </q-item-section>
          </q-item>
          <q-item v-if="moreZips" clickable @click="zipNext">
            <q-item-section>
              <q-item-label>Load More...</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-menu>
  </q-chip>
</template>

<script setup>

  import {geoPickerUtils} from 'components/common/geo/pickers/utils';
  import {computed, watch} from 'vue';

  const emit = defineEmits(['update:model-value', 'zip', 'zip-data']);
  const props = defineProps({
    state: { required: false },
    modelValue: String,
    label: String
  })

  const emitUp = (evt, ...args) => {
    const emits = {
      'zip': () => emit('update:model-value', ...args),
      'zip-data': () => emit('zip-data', ...args),
      'five': () => emit('five', ...args)
    }
    if (emits[evt]) emits[evt]();
  }

  const {
    junkStore,
    checkZip,
    zipSearch,
    zipData,
    setZipSearch,
    moreZips,
    zipNext,
    zipLoading,
    zipDrawer,
    useZips,
    setZip
  } = geoPickerUtils({ emit: emitUp, state: computed(() => props.state) })

  watch(() => props.modelValue, async (nv) => {
    // console.log('saw zip change', nv, zipData.value?.zip);
    if (nv && typeof nv === 'string') {
      if (nv !== zipData.value?.zip) {
        let drawer;
        const query = { itemId: `zips|${nv.substring(0, 3)}`, $limit: 1 }
        const inStore = junkStore.findInStore({ query })
        drawer = inStore.data[0]
        if (!drawer) {
          const jds = await junkStore.find({ query })
              .catch(err => {
                console.error(`Error finding zip in zip picker: ${err.message}`)
                return { data: [] }
              })
          // console.log('got zip drawer back', jds);
          drawer = jds.data[0]
        }
        if (drawer) {
          zipDrawer.value = drawer
          setZip(nv, 0, true)
        }
      }
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
