<template>

  <q-select
      v-bind="{
        modelValue,
        label: 'Choose State',
        emitValue: true,
        optionValue: 'value',
        optionLabel: 'text',
        options: searchAndMatch(states, searchFilter, ['text', 'value']),
        useChips: true,
        useInput: true,
        multiple,
        ...$attrs
      }"
      @input-value="searchFilter = $event"
      @update:model-value="$emit('update:model-value', $event)"
  >
    <template v-slot:selected-item="scope">
      <q-chip color="ir-grey-2">
        <q-avatar>
          <img :src="getStateByKey(scope.opt)?.icon">
        </q-avatar>
        <span class="q-ml-xs">{{getStateByKey(scope.opt)?.text}}</span>
      </q-chip>
    </template>
    <template v-slot:option="scope">
      <q-item clickable @click="scope.toggleOption(scope.opt)">
        <q-item-section avatar>
          <q-avatar>
            <img :src="getState<PERSON><PERSON><PERSON><PERSON>(scope.opt.value)?.icon">
          </q-avatar>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{scope.opt.text}}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
    <template v-slot:append>
      <slot name="append"></slot>
    </template>
    <template v-slot:after>
      <slot name="after"></slot>
    </template>
  </q-select>

</template>

<script>
  import { getStatesList, getStateByKey as gsk } from '../data/states';

  export default {
    name: 'StatePicker',
    props: {
      multiple: Boolean,
      modelValue: { required: true },
      filterOut: Array
    },
    data() {
      return {
        searchFilter: undefined
      };
    },
    methods: {
      getStateByKey(key) {
        return gsk(key)
      },
      searchAndMatch(item_list, search_query, keys_list) {
        const list = this.filterOut ? item_list.filter(a => !this.filterOut.includes(a)) : item_list

        if (!search_query) return list;

        let low_search_list = search_query.toLowerCase().trim().split(/[ ,]+/);
        let searchRegex = '(?=.*' + low_search_list.join(')(?=.*') + ')';
        let keys = keys_list;

        return list.filter(function (item) {
          if (keys) {
            return keys.some(key =>
                String((item[key] || '')).toLowerCase().match(searchRegex),
            );
          } else {
            return item.toLowerCase().match(searchRegex);
          }
        });
      },
    },
    computed: {
      states() {
        return getStatesList();
      }
    }
  };
</script>

<style scoped>

</style>
