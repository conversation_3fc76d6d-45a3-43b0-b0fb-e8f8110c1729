<template>
  <q-chip v-bind="{ color: 'ir-grey-2', clickable: true, ...attrs}">
    <q-spinner class="q-mr-xs" v-if="stateLoading" color="primary"></q-spinner>
    <span>{{ modelValue ? modelValue : 'Choose City' }}</span>
    <slot name="right">
      <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
    </slot>
    <q-menu>
      <div class="w300 bg-white">
        <div class="q-pa-sm">
          <q-input filled v-model="citySearch" dense>
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>
        <q-list separator>
          <q-item clickable @click="setCity(city)" v-for="(city, i) in useCities" :key="`city-${i}`">
            <q-item-section>
              <q-item-label>{{ city }}</q-item-label>
            </q-item-section>
          </q-item>
          <q-item v-if="moreCities" clickable @click="cityNext">
            <q-item-section>
              <q-item-label class="text-blue-10">Load More...</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-menu>
  </q-chip>
</template>

<script setup>

  import {computed, useAttrs} from 'vue';
  import {geoPickerUtils} from 'components/common/geo/pickers/utils';

  const attrs = useAttrs();
  const emit = defineEmits(['update:model-value', 'city', 'state', 'zip-data']);
  const props = defineProps({
    state: { required: true },
    modelValue: String,
    countyFilter: Array
  })

  const emitUp = (evt, ...args) => {
    const emits = {
      'city': () => emit('update:model-value', ...args),
      'city-data': () => emit('zip-data', ...args),
      'state': () => emit('state', ...args)
    }
    if (emits[evt]) emits[evt]();
  }

  const {
    setCity,
    citySearch,
    stateLoading,
    useCities,
    cityNext,
    moreCities
  } = geoPickerUtils({
    countyFilter: computed(() => props.countyFilter),
    emit: emitUp,
    state: computed(() => props.state)
  })
</script>

<style lang="scss" scoped>

</style>
