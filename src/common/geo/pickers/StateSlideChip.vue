<template>
  <div>
    <q-chip
        @click="open = !open"
        v-bind="{
    clickable: true,
    ...$attrs
      }"
    >
      <slot name="icon"></slot>
      <slot name="label">
        <span class="q-mx-xs">{{ !multiple ? getByCode(modelValue, 'name') || 'Choose State' : 'Choose State' }}</span>
      </slot>
      <q-icon :class="`_flip_off ${open ? '_flop' : ''}`" v-if="picker" name="mdi-menu-down"></q-icon>
    </q-chip>
   <q-tab-panels animated transition-next="slide-down" transition-prev="slide-up" :model-value="open ? 'open' : 'close'" class="_panel">
     <q-tab-panel class="_panel" v-show="false" name="close"></q-tab-panel>
     <q-tab-panel class="_panel" name="open">
       <div class="w200 bg-white">
         <div class="q-pa-sm">
           <q-input @focus="searchFocus = true" @blur="searchFocus = false" filled dense v-model="searchFilter" placeholder="Search...">
             <template v-slot:prepend>
               <q-icon name="mdi-magnify"></q-icon>
             </template>
           </q-input>
         </div>
         <q-slide-transition>
         <q-list dense separator v-if="searchFocus">
           <q-item
               v-for="(st, i) in useList.slice(0, 3)"
               :key="`st-${i}`"
               clickable
               @click="emitUp(st)"
           >
             <q-item-section>
               <q-item-label>{{ st.text }}</q-item-label>
             </q-item-section>
           </q-item>
         </q-list>
         </q-slide-transition>
       </div>
     </q-tab-panel>
   </q-tab-panels>
  </div>
</template>

<script setup>
  import {getByCode} from '../data/states';
  import {statePicker} from '../data/state-picker';
  import {ref} from 'vue'

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    multiple: Boolean,
    modelValue: { required: true },
    picker: Boolean
  })

  const searchFocus = ref(false);

  const { useList, searchFilter } = statePicker()

  const open = ref(false);

  const emitUp = (st) => {
    emit('update:model-value', st.value)
  }

</script>

<style scoped>

</style>
