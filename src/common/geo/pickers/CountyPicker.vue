<template>
  <q-chip v-bind="{iconRight: 'mdi-menu-down', clickable: true, ...$attrs}">
    <q-spinner class="q-mr-xs" v-if="loading" color="primary"></q-spinner>
    <span>{{ modelValue ? modelValue : 'Choose County' }}</span>
    <q-menu>
      <div class="w300 bg-white">
        <div class="q-pa-sm">
          <q-input filled v-model="searchFilter" dense>
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>
        <div class="row justify-end">
          <q-chip v-if="st && tab === 'county'" size="sm" color="transparent">
            <q-img class="h20 w20" fit="contain" :src="getByCode(st)?.icon"></q-img>
            <span class="q-mx-xs">{{ getStateName(st) }}</span>
            <q-btn size="xs" color="red" dense flat icon="mdi-close" @click="setState('')"></q-btn>
          </q-chip>
        </div>
        <q-tab-panels class="_panel" v-model="tab" animated>
          <q-tab-panel class="_panel" name="state">
            <q-list separator>
              <q-item
                  v-for="(sT, i) in useList"
                  :key="`st-${i}`"
                  clickable
                  @click="setState(sT.value)"
              >
                <q-item-section avatar>
                  <q-img class="h30 w30" fit="contain" :src="sT.icon"></q-img>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ sT.text }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="county">
            <q-list separator>
              <q-item clickable @click="emitUp(county)" v-for="(county, i) in useCounties" :key="`county-${i}`">
                <q-item-section>
                  <q-item-label>{{ county }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item v-if="more" clickable @click="next">
                <q-item-section>
                  <q-item-label class="text-blue-10">Load More...</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>
        </q-tab-panels>

      </div>
    </q-menu>
  </q-chip>
</template>

<script setup>
  import {computed, ref, watch} from 'vue';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {getByCode, getStateCode, getStateName} from 'components/common/geo/data/states';
  import {statePicker} from 'components/common/geo/data/state-picker';

  const store = useJunkDrawers();

  const emit = defineEmits(['update:model-value', 'update:zips', 'update:state']);
  const props = defineProps({
    state: { required: true },
    modelValue: String,
    zips: { required: false }
  })

  const loading = ref(false);
  const stateDrawer = ref({});

  const limit = ref(20);
  const tab = ref('state');

  const sTate = ref('')
  const st = computed(() => sTate.value || props.state)

  const { useList, searchFilter } = statePicker()

  const setState = (val) => {
    emit('update:state', getStateCode(val))
    searchFilter.value = ''
    sTate.value = val;
    if(val) tab.value = 'county'
    else tab.value = 'state'
  }

  const countyList = computed(() => {
    return Object.keys(stateDrawer.value?._fastjoin?.counties || {});
  })

  const useCounties = computed(() => {
    return countyList.value.filter(a => a.toLowerCase().includes(searchFilter.value.toLowerCase())).slice(0, limit.value);
  })

  const more = computed(() => {
    return countyList.value?.length > limit.value;
  });

  const next = () => {
    if (more.value) limit.value += 20;
  }

  const emitUp = (val) => {
    emit('update:model-value', val);
    const zips = stateDrawer.value._fastjoin.counties[val]?.zips || [];
    emit('update:zips', zips);
  }

  watch(st, async (nv, ov) => {
    if (nv && nv !== ov) {
      tab.value = 'county'
      if(getStateCode(nv)?.toLowerCase() !== stateDrawer.value?.itemName) {
        loading.value = true;
        const d = await store.find({
          query: { $limit: 1, itemId: `states|${getStateCode(nv).toLowerCase()}` },
          runJoin: { stateCounties: true }
        })
            .catch(err => {
              console.error(`Could not retrieve state drawer: ${err.message}`)
              return { data: [] }
            })
        stateDrawer.value = d.data[0]
        loading.value = false;
      }
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
