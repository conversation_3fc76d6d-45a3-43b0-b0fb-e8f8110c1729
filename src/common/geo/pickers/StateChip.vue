<template>
  <q-chip
      v-bind="{
    clickable: true,
    color: 'ir-grey-2',
    ...$attrs
      }"
  >
    <slot name="icon">
      <q-avatar v-if="!multiple && !!modelValue">
        <img :src="mv.icon">
      </q-avatar>
    </slot>
    <slot name="label">
      <span class="q-mx-xs">{{!multiple ? code ? mv.code : mv.name?.toLowerCase().split(' ').map(a => $capitalizeFirstLetter(a)).join(' ') || 'Choose State' : 'Choose State'}}</span>
    </slot>
    <q-icon v-if="picker" name="mdi-menu-down"></q-icon>
    <q-popup-proxy v-model="open" v-if="picker">
      <div class="w300 bg-white">
        <div class="q-pa-sm">
          <q-input v-model="searchFilter" placeholder="Search...">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>
        <q-virtual-scroll
            v-if="open"
            style="max-height: 300px"
            :items="useList"
            v-slot="{ item, index }"
        >
          <q-item
              :key="`st-${index}`"
              clickable
              @click="select(item.value)"
          >
            <q-item-section>
              <q-item-label>{{item.text}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-virtual-scroll>
      </div>
    </q-popup-proxy>
  </q-chip>

</template>

<script setup>
  import {memoizedGetByCode} from '../data/states';
  import {statePicker} from '../data/state-picker';
  import {computed, ref} from 'vue'
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    multiple: Boolean,
    modelValue: { required: true },
    picker: Boolean,
    code: Boolean
  })

  const mv = computed(() => props.modelValue ? memoizedGetByCode(props.modelValue) : {});

  const { useList, searchFilter } = statePicker()

  const open = ref(false);

  const select = (v) => {
    emit('update:model-value', v);
    open.value = false;
  }

</script>

<style scoped>

</style>
