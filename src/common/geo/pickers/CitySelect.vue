<template>
  <q-select
      v-bind="{placeholder: modelValue ? '' : 'Search City...', useInput: true, modelValue: modelValue || undefined, options: useCities, ...$attrs}"
      @update:model-value="setCity" @input-value="citySearch = $event">
    <template v-slot:prepend>
      <q-spinner class="q-mr-xs" v-if="stateLoading" color="primary"></q-spinner>
    </template>
    <template v-slot:after-options>
      <q-item v-if="moreCities" clickable @click="cityNext">
        <q-item-section>
          <q-item-label>Load More...</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </q-select>
</template>

<script setup>
  import {computed} from 'vue';
  import {geoPickerUtils} from 'components/common/geo/pickers/utils';

  const emit = defineEmits(['update:model-value', 'city', 'state']);
  const props = defineProps({
    state: { required: true },
    modelValue: String
  })

  const emitUp = (evt, ...args) => {
    const emits = {
      'city': () => emit('update:model-value', ...args),
      'city-data': () => emit('city-data', ...args),
      'state': () => emit('state', ...args)
    }
    if (emits[evt]) emits[evt]();
  }

  const {
    setCity,
    citySearch,
    stateLoading,
    useCities,
    cityNext,
    moreCities
  } = geoPickerUtils({ emit: emitUp, state: computed(() => props.state) })

</script>

<style lang="scss" scoped>

</style>
