import {computed, ComputedRef, Ref, ref, watch} from 'vue';
import {useJunkDrawers} from 'stores/junk-drawers.js';
import {AnyRef} from 'src/utils/types';
import {getStateCode} from 'components/common/geo/data/states';

type Options = {
    emit: (evt: string, ...args: any) => void,
    zipLimit?: Ref<number>,
    zipSkip?: Ref<number>,
    cityLimit?: Ref<number>,
    citySkip?: Ref<number>,
    infinite?: boolean,
    countyFilter?: AnyRef<Array<string>>,
    state?: Ref<any> | ComputedRef<any>
}
export const geoPickerUtils = (
    {
        emit,
        state,
        zipLimit = ref(25),
        zipSkip = ref(0),
        infinite = true,
        cityLimit = ref(25),
        citySkip = ref(0),
        countyFilter
    }: Options) => {
    const store = useJunkDrawers();

    // STATES
    const citySearch = ref('');
    const stateData: Ref<any> = ref({});
    const stateLoading = ref(false);
    // ZIPS
    const zipSearch: Ref<string> = ref('');
    const zipDrawer: Ref<any> = ref(undefined);
    const zipLoading = ref(false);

    const zipObj = computed(() => {
        if (zipDrawer.value) return zipDrawer.value.data;
        else if (stateData.value) return stateData.value?.zip_codes;
        else return {};
    })

    const setZipSearch = async (val: string) => {
        zipSearch.value = val;
        if (val.length === 3) {
            if (zipDrawer.value?.itemName !== val) {
                zipLoading.value = true;
                const drawers = await store.find({query: {itemId: `zips|${val}`}});
                if (drawers.total) zipDrawer.value = drawers.data[0];
                zipLoading.value = false;
            }
        } else if(val.length === 5){
            emit('five', val)
        }
    }

    const zipData = ref({})
    const setZip = (val: string, tries = 0, passive?:boolean) => {
        // console.log('set zip', val, zipDrawer.value);
        const zip = zipDrawer.value?.data[val];
        if(!tries){
            emit('zip', val, passive);
            if(val) emit('five', val);
        }
        if (zip) {
            // console.log('emit zip data',  zip)
            zipData.value = {...zip, zip: val}
            emit('zip-data', zipData.value, passive);
        } else if(tries < 5) {
            setTimeout(() => {
                setZip(val, tries+1, passive)
            }, 1000)
        }
    }
    const checkZip = () => {
        if (zipSearch.value?.length === 5) setZip(zipSearch.value)
    }

    const zipTotal = computed(() => Object.keys(zipObj.value || {}).length);

    const moreZips = computed(() => zipTotal.value > zipLimit.value)

    const zipNext = () => zipSkip.value + zipLimit.value < zipTotal.value ? zipSkip.value += zipLimit.value : undefined;
    const zipPrev = () => zipSkip.value = Math.max(0, zipSkip.value - zipLimit.value);

    const useZips = computed(() => {
        const list = Object.keys(zipObj.value || {});
        return list.filter(a => a.toLowerCase().includes(zipSearch.value.toLowerCase())).slice(infinite ? 0 : zipSkip.value, zipLimit.value + zipSkip.value)
    })

    //STATE FUNCTIONS
    const setCity = (val: string) => {
        const city = stateData.value?.cities[val];
        if (city) {
            emit('city', val);
            emit('city-data', Array.isArray(city) ? { zip: city } : city);
        }
    }
    const filterCities = <T = any>(o:T) => {
        const obj:T = {} as any;
        const keys = Object.keys(o || {}) as Array<keyof T>;
        for(let i = 0; i < keys.length; i++){
            if(/[a-zA-Z]/.test(String(keys[i]))) obj[keys[i]] = o[keys[i]]
        }
        return obj;
    }
    const cities = computed(() => {
        if(countyFilter?.value?.length){
            const obj:any = {};
            const cties = stateData.value.cities || {}
            const keys = Object.keys(cties).filter(a => /[a-zA-Z]/.test(String(a)));
            for(let co = 0; co < countyFilter.value.length; co++) {
                for (let i = 0; i < keys.length; i++) {
                    if ((cties[keys[i]].counties || []).includes(countyFilter.value[co])) {
                        obj[keys[i]] = cties[keys[i]];
                    }
                }
            }
            return obj
        } else return filterCities(stateData.value?.cities || {})
    });
    const cityTotal = computed(() => Object.keys(cities.value || {}).length);

    const moreCities = computed(() => cityTotal.value > cityLimit.value)
    const cityNext = () => {
        if (citySkip.value + cityLimit.value < cityTotal.value) {
            citySkip.value += cityLimit.value;
        }
    }
    const cityPrev = () => citySkip.value = Math.max(0, citySkip.value - cityLimit.value);

    const useCities = computed(() => {
        const start = infinite ? 0 : citySkip.value;
        const list = Object.keys(cities.value || {}) || [];
        return list.filter(a => a.toLowerCase().includes(citySearch.value.toLowerCase())).slice(start, cityLimit.value + citySkip.value)

    })


    watch(state as any, async (nv: any, ov: any) => {
        if (nv) {
            const isStr = typeof nv === 'string';
            if (isStr && nv !== ov) {
                stateLoading.value = true;
                const drawer = await store.find({query: {$limit: 1, itemId: `states|${getStateCode(nv)?.toLowerCase()}`}})
                stateLoading.value = false;
                if (drawer.total) {
                    stateData.value = drawer.data[0].data;
                    emit('state', stateData.value);
                }
            } else if (!isStr) {
                stateData.value = nv || {};
            }
        }
    }, {immediate: true})

    return {
        moreZips,
        zipNext,
        zipPrev,
        checkZip,
        setZip,
        zipObj,
        zipTotal,
        setZipSearch,
        useZips,
        zipSearch,
        zipDrawer,
        stateData,
        zipLoading,
        zipData,
        //state/city
        setCity,
        citySearch,
        cities,
        useCities,
        moreCities,
        cityTotal,
        cityNext,
        cityPrev,
        citySkip,
        cityLimit,

        junkStore:store
    }
}
