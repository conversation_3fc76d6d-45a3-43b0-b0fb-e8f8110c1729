const stateObj = {
    AL: {
        text: 'Alabama',
        value: 'AL',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_al.svg?alt=media&token=0cd4b38d-fa8a-4763-83b3-716ad03ae878'
    },
    AK: {
        text: 'Alaska',
        value: 'AK',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ak.svg?alt=media&token=b9565746-89d8-44a9-b721-3725d2d0bb90'
    },
    AZ: {
        text: 'Arizona',
        value: 'AZ',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_az.svg?alt=media&token=53e75743-dcbc-4ad5-b912-271df841e0e8'
    },
    AR: {
        text: 'Arkansas',
        value: 'AR',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ar.svg?alt=media&token=a5e61b85-86d9-4135-8eb8-edb0a72b79c2'
    },
    CA: {
        text: 'California',
        value: 'CA',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ca.svg?alt=media&token=0a09afc9-0dc9-413b-8c2a-209d8607f530'
    },
    CO: {
        text: 'Colorado',
        value: 'CO',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_co.svg?alt=media&token=04e3e296-e3dd-4749-b846-cd567264c994'
    },
    CT: {
        text: 'Connecticut',
        value: 'CT',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ct.svg?alt=media&token=1f399d06-af4e-4311-b094-820cc03baef3'
    },
    DE: {
        text: 'Delaware',
        value: 'DE',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_de.svg?alt=media&token=d49e07c2-d802-4519-be50-fa4714997634'
    },
    DC: {text: 'District of Columbia', value: 'DC', icon: ''},
    FL: {
        text: 'Florida',
        value: 'FL',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_fl.svg?alt=media&token=0b9f635a-2552-4b7a-a515-31ab466e6750'
    },
    GA: {
        text: 'Georgia',
        value: 'GA',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ga.svg?alt=media&token=31626e9b-5379-4dc5-913d-6729e768e739'
    },
    HI: {
        text: 'Hawaii',
        value: 'HI',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_hi.svg?alt=media&token=53204c2d-7f5b-468f-a791-5044d1539551'
    },
    ID: {
        text: 'Idaho',
        value: 'ID',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_id.svg?alt=media&token=dc7ea301-7e11-4e05-bf19-261d1f3b499d'
    },
    IL: {
        text: 'Illinois',
        value: 'IL',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_il.svg?alt=media&token=a016f7d6-27b8-4fa2-b823-a5fbc26a1390'
    },
    IN: {
        text: 'Indiana',
        value: 'IN',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_in.svg?alt=media&token=580a0ad9-7e10-443c-a2b2-c5cfeb11151e'
    },
    IA: {
        text: 'Iowa',
        value: 'IA',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ia.svg?alt=media&token=df3ca903-326d-44a5-923a-0cf72c8a11f6'
    },
    KS: {
        text: 'Kansas',
        value: 'KS',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ks.svg?alt=media&token=3207b9a0-2563-4cf8-be0b-d36b2c20b077'
    },
    KY: {
        text: 'Kentucky',
        value: 'KY',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ky.svg?alt=media&token=9870d6e1-82ed-4708-b47a-39ebec99c395'
    },
    LA: {
        text: 'Louisiana',
        value: 'LA',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_la.svg?alt=media&token=524abb6f-c7ac-471d-b996-244adb894b47'
    },
    ME: {
        text: 'Maine',
        value: 'ME',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_me.svg?alt=media&token=ff35ddae-a1c0-4159-8032-31c89ff15327'
    },
    MD: {
        text: 'Maryland',
        value: 'MD',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_md.svg?alt=media&token=74c4d06e-1f1c-484f-8ab8-8e805523e25c'
    },
    MA: {
        text: 'Massachusetts',
        value: 'MA',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ma.svg?alt=media&token=c7a02f75-27ff-4423-ba5f-d42ae63fc9ae'
    },
    MI: {
        text: 'Michigan',
        value: 'MI',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_mi.svg?alt=media&token=60c8231b-94ac-4a93-ae5d-82a4e909cc76'
    },
    MN: {
        text: 'Minnesota',
        value: 'MN',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_mn.svg?alt=media&token=33777312-4f40-4451-bd8e-638fc47b14f1'
    },
    MS: {
        text: 'Mississippi',
        value: 'MS',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ms.svg?alt=media&token=8b235dff-5522-4207-95d6-9ade39050ab2'
    },
    MO: {
        text: 'Missouri',
        value: 'MO',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_mo.svg?alt=media&token=67fd5899-03a8-49b8-93ba-af937543ed49'
    },
    MT: {
        text: 'Montana',
        value: 'MT',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_mt.svg?alt=media&token=f7566739-7132-4ecc-9c6f-3506559a448a'
    },
    NE: {
        text: 'Nebraska',
        value: 'NE',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ne.svg?alt=media&token=d99475e8-2a35-42b1-a580-8187eb1b4587'
    },
    NV: {text: 'Nevada', value: 'NV', icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_nv.svg?alt=media&token=203557ca-2520-4b1a-b4ba-e9d9eae934e8'},
    NH: {
        text: 'New Hampshire',
        value: 'NH',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_nh.svg?alt=media&token=a683dd63-7180-444a-89da-98437cd497c9'
    },
    NJ: {
        text: 'New Jersey',
        value: 'NJ',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_nj.svg?alt=media&token=2e595180-904d-4d8e-9531-38c2f86cee38'
    },
    NM: {
        text: 'New Mexico',
        value: 'NM',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_nm.svg?alt=media&token=01090616-49e2-4b28-80bc-2fce52a1cbe6'
    },
    NY: {
        text: 'New York',
        value: 'NY',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ny.svg?alt=media&token=1afe7b99-f48c-4037-878a-5d5ae910eb87'
    },
    NC: {
        text: 'North Carolina',
        value: 'NC',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_nc.svg?alt=media&token=b7452c6c-6feb-4996-9063-22d670b0ab35'
    },
    ND: {
        text: 'North Dakota',
        value: 'ND',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_nd.svg?alt=media&token=334e6a62-ea64-43d1-a94e-80ec80f0cbf0'
    },
    OH: {
        text: 'Ohio',
        value: 'OH',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_oh.svg?alt=media&token=44e6c6b3-af3b-4647-ad90-49a823205f62'
    },
    OK: {
        text: 'Oklahoma',
        value: 'OK',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ok.svg?alt=media&token=ba723b0a-c2f8-4e88-b82a-91f3aa7a1d66'
    },
    OR: {
        text: 'Oregon',
        value: 'OR',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_or.svg?alt=media&token=443c7016-17cc-473b-ae35-92cd796cf364'
    },
    PA: {
        text: 'Pennsylvania',
        value: 'PA',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_pa.svg?alt=media&token=8ff3dfac-3442-419b-88c1-db887c22f862'
    },
    RI: {
        text: 'Rhode Island',
        value: 'RI',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ri.svg?alt=media&token=2f7121b7-051f-4e28-bd39-3bafec97714a'
    },
    SC: {
        text: 'South Carolina',
        value: 'SC',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_sc.svg?alt=media&token=d54abbb5-b78b-4c88-ae1c-2d500ab33064'
    },
    SD: {
        text: 'South Dakota',
        value: 'SD',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_sd.svg?alt=media&token=d6064d36-53c8-40cd-bb2c-deeea48843f1'
    },
    TN: {
        text: 'Tennessee',
        value: 'TN',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_tn.svg?alt=media&token=dcdfae11-f4b0-47fc-ac98-b63b21dfc130'
    },
    TX: {
        text: 'Texas',
        value: 'TX',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_tx.svg?alt=media&token=9d95e218-48e6-4b35-ac05-d22f1ed77f98'
    },
    UT: {
        text: 'Utah',
        value: 'UT',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_ut.svg?alt=media&token=bb7cef2b-69a9-4fe7-a99e-10191eb88f2e'
    },
    VT: {
        text: 'Vermont',
        value: 'VT',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_vt.svg?alt=media&token=e24e8c6e-1475-45da-a19c-6dc9c910d54f'
    },
    VA: {
        text: 'Virginia',
        value: 'VA',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_va.svg?alt=media&token=f565b4e6-755d-460a-87e3-aefd4f8dba6b'
    },
    WA: {
        text: 'Washington',
        value: 'WA',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_wa.svg?alt=media&token=bca1b451-8168-420a-ab8b-ecf088460ad7'
    },
    WV: {
        text: 'West Virginia',
        value: 'WV',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_wv.svg?alt=media&token=db5746bd-85a5-4134-93ab-78c84a468da3'
    },
    WI: {
        text: 'Wisconsin',
        value: 'WI',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_wi.svg?alt=media&token=1ed251e8-7eb4-42b2-9e6e-a048a95397d9'
    },
    WY: {
        text: 'Wyoming',
        value: 'WY',
        icon: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/states%2F_wy.svg?alt=media&token=ab1a93aa-99bb-4fce-91b7-823bfe5bb89a'
    }
}

export const stateList = [
    {value: 'AL', text: 'Alabama'},
    {value: 'AK', text: 'Alaska'},
    {value: 'AZ', text: 'Arizona'},
    {value: 'AR', text: 'Arkansas'},
    {value: 'CA', text: 'California'},
    {value: 'CO', text: 'Colorado'},
    {value: 'CT', text: 'Connecticut'},
    {value: 'DE', text: 'Delaware'},
    {value: 'DC', text: 'District of Columbia'},
    {value: 'FL', text: 'Florida'},
    {value: 'GA', text: 'Georgia'},
    {value: 'HI', text: 'Hawaii'},
    {value: 'ID', text: 'Idaho'},
    {value: 'IL', text: 'Illinois'},
    {value: 'IN', text: 'Indiana'},
    {value: 'IA', text: 'Iowa'},
    {value: 'KS', text: 'Kansas'},
    {value: 'KY', text: 'Kentucky'},
    {value: 'LA', text: 'Louisiana'},
    {value: 'ME', text: 'Maine'},
    {value: 'MD', text: 'Maryland'},
    {value: 'MA', text: 'Massachusetts'},
    {value: 'MI', text: 'Michigan'},
    {value: 'MN', text: 'Minnesota'},
    {value: 'MS', text: 'Mississippi'},
    {value: 'MO', text: 'Missouri'},
    {value: 'MT', text: 'Montana'},
    {value: 'NE', text: 'Nebraska'},
    {value: 'NV', text: 'Nevada'},
    {value: 'NH', text: 'New Hampshire'},
    {value: 'NJ', text: 'New Jersey'},
    {value: 'NM', text: 'New Mexico'},
    {value: 'NY', text: 'New York'},
    {value: 'NC', text: 'North Carolina'},
    {value: 'ND', text: 'North Dakota'},
    {value: 'OH', text: 'Ohio'},
    {value: 'OK', text: 'Oklahoma'},
    {value: 'OR', text: 'Oregon'},
    {value: 'PA', text: 'Pennsylvania'},
    {value: 'RI', text: 'Rhode Island'},
    {value: 'SC', text: 'South Carolina'},
    {value: 'SD', text: 'South Dakota'},
    {value: 'TN', text: 'Tennessee'},
    {value: 'TX', text: 'Texas'},
    {value: 'UT', text: 'Utah'},
    {value: 'VT', text: 'Vermont'},
    {value: 'VA', text: 'Virginia'},
    {value: 'WA', text: 'Washington'},
    {value: 'WV', text: 'West Virginia'},
    {value: 'WI', text: 'Wisconsin'},
    {value: 'WY', text: 'Wyoming'}
]


const states = {
    'AL': {'name': 'ALABAMA', 'fips': '01'},
    'AK': {'name': 'ALASKA', 'fips': '02'},
    'AZ': {'name': 'ARIZONA', 'fips': '04'},
    'AR': {'name': 'ARKANSAS', 'fips': '05'},
    'CA': {'name': 'CALIFORNIA', 'fips': '06'},
    'CO': {'name': 'COLORADO', 'fips': '08'},
    'CT': {'name': 'CONNECTICUT', 'fips': '09'},
    'DE': {'name': 'DELAWARE', 'fips': '10'},
    'DC': {'name': 'DISTRICT OF COLUMBIA', 'fips': '11'},
    'FL': {'name': 'FLORIDA', 'fips': '12'},
    'GA': {'name': 'GEORGIA', 'fips': '13'},
    'HI': {'name': 'HAWAII', 'fips': '15'},
    'ID': {'name': 'IDAHO', 'fips': '16'},
    'IL': {'name': 'ILLINOIS', 'fips': '17'},
    'IN': {'name': 'INDIANA', 'fips': '18'},
    'IA': {'name': 'IOWA', 'fips': '19'},
    'KS': {'name': 'KANSAS', 'fips': '20'},
    'KY': {'name': 'KENTUCKY', 'fips': '21'},
    'LA': {'name': 'LOUISIANA', 'fips': '22'},
    'ME': {'name': 'MAINE', 'fips': '23'},
    'MD': {'name': 'MARYLAND', 'fips': '24'},
    'MA': {'name': 'MASSACHUSETTS', 'fips': '25'},
    'MI': {'name': 'MICHIGAN', 'fips': '26'},
    'MN': {'name': 'MINNESOTA', 'fips': '27'},
    'MS': {'name': 'MISSISSIPPI', 'fips': '28'},
    'MO': {'name': 'MISSOURI', 'fips': '29'},
    'MT': {'name': 'MONTANA', 'fips': '30'},
    'NE': {'name': 'NEBRASKA', 'fips': '31'},
    'NV': {'name': 'NEVADA', 'fips': '32'},
    'NH': {'name': 'NEW HAMPSHIRE', 'fips': '33'},
    'NJ': {'name': 'NEW JERSEY', 'fips': '34'},
    'NM': {'name': 'NEW MEXICO', 'fips': '35'},
    'NY': {'name': 'NEW YORK', 'fips': '36'},
    'NC': {'name': 'NORTH CAROLINA', 'fips': '37'},
    'ND': {'name': 'NORTH DAKOTA', 'fips': '38'},
    'OH': {'name': 'OHIO', 'fips': '39'},
    'OK': {'name': 'OKLAHOMA', 'fips': '40'},
    'OR': {'name': 'OREGON', 'fips': '41'},
    'PA': {'name': 'PENNSYLVANIA', 'fips': '42'},
    'RI': {'name': 'RHODE ISLAND', 'fips': '44'},
    'SC': {'name': 'SOUTH CAROLINA', 'fips': '45'},
    'SD': {'name': 'SOUTH DAKOTA', 'fips': '46'},
    'TN': {'name': 'TENNESSEE', 'fips': '47'},
    'TX': {'name': 'TEXAS', 'fips': '48'},
    'UT': {'name': 'UTAH', 'fips': '49'},
    'VT': {'name': 'VERMONT', 'fips': '50'},
    'VA': {'name': 'VIRGINIA', 'fips': '51'},
    'WA': {'name': 'WASHINGTON', 'fips': '53'},
    'WV': {'name': 'WEST VIRGINIA', 'fips': '54'},
    'WI': {'name': 'WISCONSIN', 'fips': '55'},
    'WY': {'name': 'WYOMING', 'fips': '56'}
}

export const stateFips = {
    '01': {'name': 'ALABAMA', 'code': 'AL'},
    '02': {'name': 'ALASKA', 'code': 'AK'},
    '04': {'name': 'ARIZONA', 'code': 'AZ'},
    '05': {'name': 'ARKANSAS', 'code': 'AR'},
    '06': {'name': 'CALIFORNIA', 'code': 'CA'},
    '08': {'name': 'COLORADO', 'code': 'CO'},
    '09': {'name': 'CONNECTICUT', 'code': 'CT'},
    '10': {'name': 'DELAWARE', 'code': 'DE'},
    '11': {'name': 'DISTRICT OF COLUMBIA', 'code': 'DC'},
    '12': {'name': 'FLORIDA', 'code': 'FL'},
    '13': {'name': 'GEORGIA', 'code': 'GA'},
    '15': {'name': 'HAWAII', 'code': 'HI'},
    '16': {'name': 'IDAHO', 'code': 'ID'},
    '17': {'name': 'ILLINOIS', 'code': 'IL'},
    '18': {'name': 'INDIANA', 'code': 'IN'},
    '19': {'name': 'IOWA', 'code': 'IA'},
    '20': {'name': 'KANSAS', 'code': 'KS'},
    '21': {'name': 'KENTUCKY', 'code': 'KY'},
    '22': {'name': 'LOUISIANA', 'code': 'LA'},
    '23': {'name': 'MAINE', 'code': 'ME'},
    '24': {'name': 'MARYLAND', 'code': 'MD'},
    '25': {'name': 'MASSACHUSETTS', 'code': 'MA'},
    '26': {'name': 'MICHIGAN', 'code': 'MI'},
    '27': {'name': 'MINNESOTA', 'code': 'MN'},
    '28': {'name': 'MISSISSIPPI', 'code': 'MS'},
    '29': {'name': 'MISSOURI', 'code': 'MO'},
    '30': {'name': 'MONTANA', 'code': 'MT'},
    '31': {'name': 'NEBRASKA', 'code': 'NE'},
    '32': {'name': 'NEVADA', 'code': 'NV'},
    '33': {'name': 'NEW HAMPSHIRE', 'code': 'NH'},
    '34': {'name': 'NEW JERSEY', 'code': 'NJ'},
    '35': {'name': 'NEW MEXICO', 'code': 'NM'},
    '36': {'name': 'NEW YORK', 'code': 'NY'},
    '37': {'name': 'NORTH CAROLINA', 'code': 'NC'},
    '38': {'name': 'NORTH DAKOTA', 'code': 'ND'},
    '39': {'name': 'OHIO', 'code': 'OH'},
    '40': {'name': 'OKLAHOMA', 'code': 'OK'},
    '41': {'name': 'OREGON', 'code': 'OR'},
    '42': {'name': 'PENNSYLVANIA', 'code': 'PA'},
    '44': {'name': 'RHODE ISLAND', 'code': 'RI'},
    '45': {'name': 'SOUTH CAROLINA', 'code': 'SC'},
    '46': {'name': 'SOUTH DAKOTA', 'code': 'SD'},
    '47': {'name': 'TENNESSEE', 'code': 'TN'},
    '48': {'name': 'TEXAS', 'code': 'TX'},
    '49': {'name': 'UTAH', 'code': 'UT'},
    '50': {'name': 'VERMONT', 'code': 'VT'},
    '51': {'name': 'VIRGINIA', 'code': 'VA'},
    '53': {'name': 'WASHINGTON', 'code': 'WA'},
    '54': {'name': 'WEST VIRGINIA', 'code': 'WV'},
    '55': {'name': 'WISCONSIN', 'code': 'WI'},
    '56': {'name': 'WYOMING', 'code': 'WY'}
}

const byName = {
    'ALABAMA': {'code': 'AL', 'fips': '01'},
    'ALASKA': {'code': 'AK', 'fips': '02'},
    'ARIZONA': {'code': 'AZ', 'fips': '04'},
    'ARKANSAS': {'code': 'AR', 'fips': '05'},
    'CALIFORNIA': {'code': 'CA', 'fips': '06'},
    'COLORADO': {'code': 'CO', 'fips': '08'},
    'CONNECTICUT': {'code': 'CT', 'fips': '09'},
    'DELAWARE': {'code': 'DE', 'fips': '10'},
    'DISTRICT OF COLUMBIA': {'code': 'DC', 'fips': '11'},
    'FLORIDA': {'code': 'FL', 'fips': '12'},
    'GEORGIA': {'code': 'GA', 'fips': '13'},
    'HAWAII': {'code': 'HI', 'fips': '15'},
    'IDAHO': {'code': 'ID', 'fips': '16'},
    'ILLINOIS': {'code': 'IL', 'fips': '17'},
    'INDIANA': {'code': 'IN', 'fips': '18'},
    'IOWA': {'code': 'IA', 'fips': '19'},
    'KANSAS': {'code': 'KS', 'fips': '20'},
    'KENTUCKY': {'code': 'KY', 'fips': '21'},
    'LOUISIANA': {'code': 'LA', 'fips': '22'},
    'MAINE': {'code': 'ME', 'fips': '23'},
    'MARYLAND': {'code': 'MD', 'fips': '24'},
    'MASSACHUSETTS': {'code': 'MA', 'fips': '25'},
    'MICHIGAN': {'code': 'MI', 'fips': '26'},
    'MINNESOTA': {'code': 'MN', 'fips': '27'},
    'MISSISSIPPI': {'code': 'MS', 'fips': '28'},
    'MISSOURI': {'code': 'MO', 'fips': '29'},
    'MONTANA': {'code': 'MT', 'fips': '30'},
    'NEBRASKA': {'code': 'NE', 'fips': '31'},
    'NEVADA': {'code': 'NV', 'fips': '32'},
    'NEW HAMPSHIRE': {'code': 'NH', 'fips': '33'},
    'NEW JERSEY': {'code': 'NJ', 'fips': '34'},
    'NEW MEXICO': {'code': 'NM', 'fips': '35'},
    'NEW YORK': {'code': 'NY', 'fips': '36'},
    'NORTH CAROLINA': {'code': 'NC', 'fips': '37'},
    'NORTH DAKOTA': {'code': 'ND', 'fips': '38'},
    'OHIO': {'code': 'OH', 'fips': '39'},
    'OKLAHOMA': {'code': 'OK', 'fips': '40'},
    'OREGON': {'code': 'OR', 'fips': '41'},
    'PENNSYLVANIA': {'code': 'PA', 'fips': '42'},
    'RHODE ISLAND': {'code': 'RI', 'fips': '44'},
    'SOUTH CAROLINA': {'code': 'SC', 'fips': '45'},
    'SOUTH DAKOTA': {'code': 'SD', 'fips': '46'},
    'TENNESSEE': {'code': 'TN', 'fips': '47'},
    'TEXAS': {'code': 'TX', 'fips': '48'},
    'UTAH': {'code': 'UT', 'fips': '49'},
    'VERMONT': {'code': 'VT', 'fips': '50'},
    'VIRGINIA': {'code': 'VA', 'fips': '51'},
    'WASHINGTON': {'code': 'WA', 'fips': '53'},
    'WEST VIRGINIA': {'code': 'WV', 'fips': '54'},
    'WISCONSIN': {'code': 'WI', 'fips': '55'},
    'WYOMING': {'code': 'WY', 'fips': '56'}
}

const stateLngLat = {
    'AL': [-86.7911, 32.8067],
    'AK': [-152.4044, 61.3707],
    'AZ': [-111.4312, 33.7298],
    'AR': [-92.3731, 34.9697],
    'CA': [-119.6816, 36.1162],
    'CO': [-105.3111, 39.0598],
    'CT': [-72.7554, 41.5978],
    'DE': [-75.5071, 39.3185],
    'FL': [-81.6868, 27.7663],
    'GA': [-83.6431, 33.0406],
    'HI': [-157.4983, 21.0943],
    'ID': [-114.4788, 44.2405],
    'IL': [-88.9861, 40.3495],
    'IN': [-86.2583, 39.8494],
    'IA': [-93.2105, 42.0115],
    'KS': [-96.7265, 38.5266],
    'KY': [-84.6701, 37.6681],
    'LA': [-91.8678, 31.1695],
    'ME': [-69.3819, 44.6939],
    'MD': [-76.8021, 39.0639],
    'MA': [-71.5301, 42.2302],
    'MI': [-84.5361, 43.3266],
    'MN': [-93.9002, 45.6945],
    'MS': [-89.6787, 32.7416],
    'MO': [-92.2884, 38.4561],
    'MT': [-110.4544, 46.9219],
    'NE': [-98.2681, 41.1254],
    'NV': [-117.0554, 38.3135],
    'NH': [-71.5639, 43.4525],
    'NJ': [-74.5210, 40.2989],
    'NM': [-106.2485, 34.8405],
    'NY': [-74.9481, 42.1657],
    'NC': [-79.8064, 35.6301],
    'ND': [-99.7840, 47.5289],
    'OH': [-82.7649, 40.3888],
    'OK': [-96.9289, 35.5653],
    'OR': [-122.0709, 44.5720],
    'PA': [-77.2098, 40.5908],
    'RI': [-71.5118, 41.6809],
    'SC': [-80.9450, 33.8569],
    'SD': [-99.4388, 44.2998],
    'TN': [-86.6923, 35.7478],
    'TX': [-97.5635, 31.0545],
    'UT': [-111.8624, 40.1500],
    'VT': [-72.7107, 44.0459],
    'VA': [-78.1700, 37.7693],
    'WA': [-121.4905, 47.4009],
    'WV': [-80.9545, 38.4912],
    'WI': [-89.6165, 44.2685],
    'WY': [-107.3025, 42.7559]
}


export const getByName = (stName: string, path: string) => {
    const obj = byName[stName?.toUpperCase() as keyof typeof byName];
    return obj && path ? obj[path as keyof typeof obj] : obj;
}

export const getStateCode = (val: string) => {
    if (!val || typeof val !== 'string') return '';
    if (val.length === 2) return val;
    else return byName[val?.toUpperCase() as keyof typeof byName]?.code
}


export const getByCode = (stCode: string, path?: string) => {
    const code = getStateCode(stCode) as keyof typeof states
    const obj = { ...states[code], icon: stateObj[code]?.icon, code };
    return obj && path ? obj[path as keyof typeof obj] : obj;
}

export const memoizedGetByCode = (() => {
    const cache = new Map();
    return (code, path) => {
        const key = `${code}:${path || 'full'}`;
        if (!cache.has(key)) {
            cache.set(key, getByCode(code, path));
        }
        return cache.get(key);
    };
})();

export const getStateLngLat = (st: string) => {
    const key = getStateCode(st);
    return key ? stateLngLat[key as keyof typeof stateLngLat] : undefined;
}

export const getStateName = (val: string) => {
    if (!val) return undefined;
    if (val.length === 2) return stateObj[val.toUpperCase() as keyof typeof stateObj]?.text;
    else return val;
}

export const getStatesList = () => {
    return stateList;
}

export const getStateByKey = (key: string) => {
    return stateObj[key?.toUpperCase() as keyof typeof stateObj];
}

export const keyByValue = () => {
    const obj: { [key: string]: any } = {};
    getStatesList().forEach(state => obj[state.value.toLowerCase()] = state.text);
    return obj;
}

export const keyByText = () => {
    const obj: { [key: string]: any } = {};
    getStatesList().forEach(state => obj[state.text.toLowerCase()] = state.value);
    return obj;
}



