import {computed, ref, watch} from 'vue';
import {getStatesList, getByCode} from 'components/common/geo/data/states.js';
import { debounce } from 'quasar';

export const statePicker = () => {
    const useFilter = ref('');
    const statesList = getStatesList();
    const searchFilter = ref('');

    const updateSearch = debounce((val) => {
        useFilter.value = val;
    }, 300);

    watch(searchFilter, (val) => {
        updateSearch(val);
    });
    // Pre-compute icons for all states
    const statesWithIcons = statesList.map(state => ({
        ...state,
        icon: getByCode(state.value, 'icon')
    }));

    const useList = computed(() => {
        if (!searchFilter.value) return statesWithIcons;

        const filter = useFilter.value.toLowerCase();
        return statesWithIcons.filter(state =>
            state.text.toLowerCase().includes(filter) ||
            state.value.toLowerCase().includes(filter)
        );
    });

    return {
        useList,
        searchFilter
    }
}
