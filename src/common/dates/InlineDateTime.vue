<template>
  <div class="flex items-center">
    <div class="q-px-sm">
      <inline-date :label="dateLabel" :model-value="modelValue" @update:model-value="handleDate"></inline-date>
    </div>

    <div class="q-px-sm">
      <inline-time label="@ (time)" :model-value="modelValue" @update:model-value="handleDate"></inline-time>
    </div>
    <q-btn dense flat size="sm" color="red" icon="mdi-close" @click="$emit('remove')"></q-btn>
  </div>
</template>

<script>
  import InlineDate from './InlineDate.vue';
  import InlineTime from './InlineTime.vue';
  export default {
    name: 'InlineDateTime',
    components: { InlineTime, InlineDate },
    props: {
      dateLabel: { type: String, default: 'From (date)'},
      modelValue: [Date, String]
    },
    methods: {
      handleDate(val){
        this.$emit('update:model-value', typeof val === 'string' ? val : val.toString());
      }
    }
  };
</script>

<style scoped>

</style>
