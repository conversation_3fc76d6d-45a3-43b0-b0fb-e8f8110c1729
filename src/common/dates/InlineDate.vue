<template>
  <q-card flat :dark="dark">
    <q-input
      v-bind="{
        disable,
        dark,
        outlined,
        placeholder,
        modelValue: useValue,
        inputClass,
        ...inputAttrs
      }"
    >
      <template v-slot:prepend>
        <slot name="prepend"></slot>
      </template>
      <q-popup-proxy>
        <q-card>
        <q-date
          :mask="displayFormat"
          :model-value="useValue"
          @update:model-value="handleInput"
          :dark="dark"
          :color="color"
        ></q-date>
        </q-card>
      </q-popup-proxy>
      <template v-slot:append>
        <slot name="append">
          <q-btn v-if="clearable && useValue" dense flat size="xs" color="red" icon="mdi-close" @click="handleInput(undefined)"></q-btn>
        </slot>
      </template>
      <template v-slot:after>
        <slot name="after"></slot>
      </template>
    </q-input>
  </q-card>
</template>

<script>
  import { formatDate, extractDate} from 'src/utils/date-utils';

  export default {
    name: 'InlineDate',
    components: { },
    props: {
      clearable: Boolean,
      inputAttrs: Object,
      disable: <PERSON>olean,
      dense: { type: Boolean, default: true },
      inputClass: String,
      outlined: Boolean,
      placeholder: { type: String, default: 'Choose Date' },
      inputSize: String,
      dark: Boolean,
      color: String,
      buttonColor: String,
      noButton: Boolean,
      right: Boolean,
      noButtonNow: Boolean,
      overlay: Boolean,
      autoClose: Boolean,
      noHeader: Boolean,
      textClass: { type: String, default: 'text-xs text-mb-xs' },
      borderColor: String,
      displayOff: Boolean,
      inline: Boolean,
      format: String,
      keepTime: Boolean,
      displayFormat: {type: String, default: 'DD MMM, YYYY'},
      modelValue: [Date, String]
    },
    data(){
      return {
        timeRecord: undefined
      };
    },
    watch: {
      modelValue: {
        immediate: true,
        handler(newVal){
          if(newVal){
            this.timeRecord = formatDate(newVal, 'H:mm');
          }
        }
      }
    },
    computed: {
      useValue() {
        let date = this.modelValue;
        return formatDate(date, this.displayFormat);
      }
    },
    methods: {
      handleInput(val){
        if(val) {
          let payload = new Date(val);
          if (this.timeRecord && this.keepTime) {
            let d = formatDate(payload, 'MM/DD/YYYY');
            let dt = extractDate(d + ' ' + this.timeRecord, 'MM/DD/YYYY h:m');
            payload = new Date(dt);
          }
          this.$emit('update:model-value', payload);
        } else this.$emit('update:model-value', undefined);
      }
    }
  };
</script>

<style scoped lang="scss">

</style>
