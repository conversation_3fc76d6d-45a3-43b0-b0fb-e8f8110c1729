<template>
  <q-card flat :dark="dark" style="cursor:pointer" class="q-pa-xs">
    <div class="text-xxxs text-mb-xxxs text-weight-light">{{ label }}</div>
    <q-chip
      :disable="disable"
      icon-right="mdi-menu-down"
      icon="mdi-clock"
      :square="square"
      :dark="dark"
      :label="useValue ? useValue : 'No Time...'"
      :color="endErrors ? 'negative' : color ? color : dark ? 'dark' : 'light'"
      :size="dense ? 'md' : 'lg'"
      :outline="outline"
      :removable="!!useValue && removable"
      @remove="handleInput(null)"
    >
      <q-popup-proxy v-if="!disable">
        <q-list separator>
          <q-item>
            <q-input label="Enter Time..." dense v-model="searchInput" @keyup.enter="addTime(searchInput)">
            </q-input>
          </q-item>
          <q-item clickable @click="handleInput(opt)" v-for="(opt, i) in times" :key="`time-opt-${i}`">
            <q-item-section>
              <q-item-label>{{ parseDateHour(opt, displayFormat) }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-popup-proxy>
    </q-chip>

  </q-card>
</template>

<script>
  import {extractDate, formatDate, genDateHour, parseDateHour} from 'src/utils/date-utils';

  export default {
    name: 'InlineTime',
    components: {},
    props: {
      removable: Boolean,
      disable: Boolean,
      square: Boolean,
      borderless: Boolean,
      dense: { type: Boolean, default: true },
      endErrors: Boolean,
      outline: Boolean,
      label: { type: String, default: 'Choose Time' },
      minuteInterval: { type: Number, default: 5 },
      inputSize: String,
      dark: Boolean,
      color: String,
      buttonColor: String,
      noButton: Boolean,
      right: Boolean,
      noButtonNow: Boolean,
      overlay: Boolean,
      autoClose: Boolean,
      noHeader: Boolean,
      textClass: { type: String, default: 'text-xs text-mb-xs' },
      borderColor: String,
      displayOff: Boolean,
      inline: Boolean,
      format: String,
      dateFormat: String,
      displayFormat: { type: String, default: 'h:mm a' },
      modelValue: [Date, String]
    },
    data() {
      return {
        parseDateHour,
        interval: 15,
        searchInput: '',
        addedTimes: []
      };
    },
    computed: {
      endRules() {
        return [
          !this.endErrors || 'Cannot be before start time'
        ];
      },
      useValue() {
        let date = this.modelValue;
        if (this.dateFormat) date = extractDate(this.modelValue, this.dateFormat);
        return formatDate(date, this.displayFormat);
      },
      intervals() {
        //before changing these, see that pushTime function is using these fixed intervals for logic
        return [60, 30, 15];
      },
      times() {
        let list = [];
        for (let i = 0; i < 24; i++) {
          let intervals = this.interval ? 60 / this.interval : 1;
          for (let int = 0; int < intervals; int++) {
            let minutes = int * this.interval;
            i < 20 ? list.push(parseInt(formatDate(genDateHour((i + 5), minutes), 'Hmm'))) : list.push(parseInt(formatDate(genDateHour((i - 19), minutes), 'Hmm')));
          }
        }
        let times = list;
        if (this.searchInput && times) times = list.filter(a => {
          let str = this.searchInput.toString().replace(/\s|:/g,'');
          return a.toString().toLowerCase().indexOf(str.toLowerCase().trim()) > -1;
        });
        return this.addedTimes ? [...times, ...this.addedTimes] : times;
      },
    },
    methods: {
      handleInput(val) {
        if (val || val === 0) {
          let date = formatDate(this.modelValue, 'YY-MM-DD');
          const payload = extractDate(date + ' ' + String(val), 'YY-MM-DD Hmm');
          this.$emit('update:model-value', new Date(payload));
        } else this.$emit('update:model-value', null);
      },
      checkValidTime(val) {
        let t = val.toString().toLowerCase();
        let r = null;
        let valid = false;
        let format = 'h:mm a';
        let list = ['h', 'h m', 'hm', 'hmm', 'h:m', 'hh', 'hhmm', 'hhm', 'h:mm', 'hh:mm', 'h:mm a', 'h:mm A', 'hh:mm a', 'hh:mm A', 'hmm a', 'hmm A', 'hhmm a', 'hhmm A', 'h:mma', 'hh:mma', 'h:ma', 'h:m a'];
        for (let i = 0; i < list.length; i++) {
          let extract = extractDate(t.toString(), list[i]);
          if (formatDate(extract, list[i]) === t.toString()) {
            valid = true;
            r = extract;
            format = list[i];
          }
        }
        return { valid: valid, date: r, format: format };
      },
      addTime(val) {
        let valid = this.checkValidTime(val);
        if (valid.valid) {
          let timeVal = parseInt(formatDate(valid.date, 'Hmm'));
          this.handleInput(timeVal);
        } else this.$q.notify({
          message: 'Time format not recognized - try h:mm a',
          color: 'info'
        });
      }
    }
  };
</script>

<style scoped lang="scss">

</style>
