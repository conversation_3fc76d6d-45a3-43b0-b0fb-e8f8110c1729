<template>
  <div class="_fw">
    <tomtom-autocomplete @update:model-value="addAddress" :model-value="undefined" v-bind="{...$attrs}">
      <template v-slot:append="scope">
        <q-spinner size="30px" v-if="scope?.loading"></q-spinner>
        <slot name="append" v-if="!scope?.loading" v-bind="scope"></slot>
      </template>
      <template v-slot:prepend>
        <slot name="prepend">
          <q-icon name="mdi-map-marker" color="primary"></q-icon>
        </slot>
      </template>
      <template v-slot:no-option="scope">
        <q-item clickable @click="scope.setAdding">
          <q-item-section>
            <q-item-label class="text-weight-bold font-3-4r">Address not popping up? Add Custom Address</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-icon color="primary" name="mdi-plus"></q-icon>
          </q-item-section>
        </q-item>
      </template>
    </tomtom-autocomplete>
    <q-list separator>
      <q-item v-for="(address, i) in modelValue || []" :key="`address-${i}`">
        <q-item-section avatar>
          <q-btn v-if="primaryIndex === i" dense flat color="primary" icon="mdi-home" size="sm">
            <q-tooltip>Is Primary</q-tooltip>
          </q-btn>
          <q-btn v-else dense flat color="grey-3" size="sm" icon="mdi-home" @click="emit('update:primary', address)">
            <q-tooltip>Make Primary</q-tooltip>
          </q-btn>
        </q-item-section>
        <q-item-section>

          <q-item-label class="font-3-4r">
            {{
              address.formatted || `${address.address1} ${address.address2 || ''} ${address.city || ''}, ${address.region}`
            }}
          </q-item-label>
        </q-item-section>
        <q-item-section side>
          <remove-proxy-btn name="Address" size="xs" icon="mdi-close" @remove="remove(i)"></remove-proxy-btn>
        </q-item-section>
      </q-item>
    </q-list>
  </div>
</template>

<script setup>
  import TomtomAutocomplete from 'src/components/common/address/tomtom/TomtomAutocomplete.vue';
  import {computed} from 'vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  const emit = defineEmits(['update:model-value', 'update:valid', 'update:primary']);
  const props = defineProps({
    modelValue: Array,
    valid: Boolean,
    primary: Object
  })

  const getIndex = (address) => {
    const list = (props.modelValue || []).map(a => JSON.stringify(a));
    return list.indexOf(JSON.stringify(address));

  }
  const addAddress = (address) => {
    if (address) {
      if (!props.modelValue?.length) emit('update:primary', address);
      const idx = getIndex(address);
      if (idx === -1) {
        emit('update:model-value', [...(props.modelValue || []), address]);
      }
      emit('update:valid', true);
    }
  }

  const primaryIndex = computed(() => {
    if (!props.primary) return -1;
    return getIndex(props.primary);
  })

  const remove = (idx) => {
    const arr = [...props.modelValue || []];
    arr.splice(idx, 1);
    emit('update:model-value', arr);
    if (primaryIndex.value === idx) {
      if (arr.length > 0) emit('update:primary', arr[0]);
      else emit('update:primary', undefined);
    }
  }
</script>

<style lang="scss" scoped>

</style>
