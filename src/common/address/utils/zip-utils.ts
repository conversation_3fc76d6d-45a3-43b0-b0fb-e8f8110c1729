import {_get} from 'symbol-syntax-utils';

export const getByItemId = async (itemId:string, useJunkDrawers:any):Promise<any> => {
    const { data } = await useJunkDrawers().find({ query: { itemId }})
    return data[0];
}

type ZipObj = { city: string, state: string, country: string };
export const searchZip = async (zip:string|number, useJunkDrawers:Function):Promise<Array<ZipObj>> => {
    if(typeof zip === 'number') zip = String(zip);
    return await getByItemId(`zips|${zip.slice(0, 3)}`, useJunkDrawers);
}

//Two letter state iso2
export const getState = async (state:string, useJunkDrawers:Function):Promise<any> => {
    return await getByItemId(`states|${state.toLowerCase()}`, useJunkDrawers);
}

export const citiesByState = async (state:string, useJunkDrawers:Function) => {
    const st = await getState(state, useJunkDrawers);
    return _get(st, 'cities');
};


