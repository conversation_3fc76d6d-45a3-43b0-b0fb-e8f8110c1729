import {_get} from 'symbol-syntax-utils';
import { useJunkDrawers } from 'src/stores/junk-drawers';

export const getByItemId = async (itemId:string):Promise<any> => {
    const { data } = await useJunkDrawers().find({ query: { itemId }})
    return data[0];
}

type ZipObj = { city: string, state: string, country: string };
export const searchZip = async (zip:string|number):Promise<Array<ZipObj>> => {
    if(typeof zip === 'number') zip = String(zip);
    return await getByItemId(`zips|${zip.slice(0, 3)}`);
}

//Two letter state iso2
export const getState = async (state:string):Promise<any> => {
    return await getByItemId(`states|${state.toLowerCase()}`);
}

export const citiesByState = async (state:string) => {
    const st = await getState(state);
    return _get(st, 'cities');
};


