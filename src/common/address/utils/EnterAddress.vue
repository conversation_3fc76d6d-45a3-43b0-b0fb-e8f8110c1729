<template>
  <div class="_fw">

    <q-input
        v-bind="{
      ...iAttrs,
      label: 'Address Line 1'
    }"
       v-model="form.address1"
    ></q-input>
    <q-input
        v-bind="{
      ...iAttrs,
      label: 'Address Line 2'
    }"
        v-model="form.address2"
    ></q-input>
    <q-input
        v-bind="{
      ...iAttrs,
      label: 'Zip Code'
    }"
        v-model="form.postal"
        @update:model-value="checkZip"

    ></q-input>
    <state-picker
        v-model="form.region"
        @update:model-value="addState"
        label="State"
        dense
    ></state-picker>
    <q-select
        v-bind="{
      ...iAttrs,
      label: 'City',
      options: cities.filter(a => a.toLowerCase().includes(citySearch.toLowerCase())),
      useInput: true,
      newValueMode: 'add-unique'
        }"
        @input-value="citySearch = $event"
        @update:model-value="addCity"
        v-model="form.city"
    ></q-select>
    <q-select
        v-bind="{
      ...iAttrs,
      label: 'Country',
      useInput: true,
      useChips: true,
      options: countries,
        }"
        v-model="form.country"
        @input-value="setSearch"
    >
      <template v-slot:selected-item="scope">
        <q-chip color="white">
          <span style="font-size:10px">{{ getFlag(scope.opt, 'emoji') }}</span>
          <span class="q-ml-xs">{{ scope.opt }}</span>
        </q-chip>
      </template>
      <template v-slot:option="scope">
        <q-item clickable @click="scope.toggleOption(scope.opt)">
          <q-item-section avatar>
            <span style="font-size: 20px">{{ getFlag(scope.opt, 'emoji') }}</span>
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ scope.opt }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>
    <div class="row justify-end q-py-sm">
      <q-btn size="sm" label="Add Address" icon-right="mdi-plus" @click="add" color="primary"></q-btn>
    </div>
  </div>
</template>

<script setup>

  import {computed, ref} from 'vue';
  import {countriesObj} from 'src/components/common/phone/all-countries';
  import {flag} from 'src/components/common/phone/flag-emojis';
  import {_get} from 'symbol-syntax-utils';
  import StatePicker from 'src/components/common/geo/pickers/StatePicker.vue';
  import { searchZip, getState } from './zip-utils';
  import {Notify} from 'quasar';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    inputAttrs: Object
  });

  const search = ref('');

  const setSearch = val => {
    console.log(val);
    search.value = val;
  }

  const countries = computed(() => {
    if (search.value) {
      return Object.keys(countriesObj).filter(a => {
        return a.toLowerCase().indexOf(search.value.toLowerCase()) > -1;
      }).slice(0, 20);
    } else return Object.keys(countriesObj).slice(0, 25);
  });

  const iAttrs = computed(() => {
    return {
      dense: true,
      ...props.inputAttrs
    }
  })

  const form = ref({
    address1: '',
    address2: '',
    formatted: '',
    postal: '',
    city: '',
    region: '',
    country: 'United States'
  });

  const getFlag = (co, path) => {
    return flag(_get(countriesObj, [co, 'iso2']), path);
  }

  const zipData = ref({});
  const stateData = ref({});

  const cities = ref([]);
  const citySearch = ref('');

  const checkZip = async (zip) => {
    if(zip.length === 5){
      const z = await searchZip(zip);
      zipData.value = z.data[zip];
      if(zipData.value.city && !form.value.city){
        const { city, state } = zipData.value;
        form.value.city = city;
        form.value.region = state;
      }
    }
  }

  const addState = async (st) => {
    if(!stateData.value?.cities){
      const state = await getState(st);
      if(state?.data) {
        stateData.value = state.data;
        cities.value = Array.isArray(state.data.cities) ? { zip: state.data.cities[0] } : Object.keys(state.data.cities)
      }
    }
  };

  const addCity = (city) => {
    if(stateData.value?.cities){
      console.log('add city', city, stateData.value.cities[city]);
      const zip = stateData.value.cities[city][0];
      if(zip) form.value.postal = zip;
    }
  }

  const add = () => {
    const n = (message) => Notify.create({
      message,
      color: 'red',
      timeout: 3000,
      actions: [{ icon: 'mdi-close', color: 'white' }]
    });
    if(!form.value.address1) n('Add street address');
    else if(!form.value.postal) n('Add Zip Code');
    else if(!form.value.region) n('Add State');
    else if(!form.value.city) n('Add City');
    else if(!form.value.country) n('Add Country');
    else {
      form.value.formatted = `${form.value.address1} ${form.value.city}, ${form.value.region} ${form.value.country}`;
      emit('update:model-value', form.value);
    }
  };

</script>

<style lang="scss" scoped>

</style>
