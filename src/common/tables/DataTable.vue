<template>
  <div class="_fw">
    <div class="row justify-center q-pa-sm">
      <div class="_cent" v-if="!searchOff">
        <div class="row items-center">
          <div class="col-10">
            <slot name="search">
              <q-input class="w600 mw100" v-model="search.text" v-bind="searchAttrs">
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
            </slot>
          </div>
          <div class="col-2" v-if="!hideAddBtn">
            <slot name="add-button" :dialog="addDialog">
              <div class="row justify-end">
                <q-btn v-bind="{ flat: true, ...addBtnAttrs}" @click="addDialog = true">
                  <q-icon class="q-mr-sm" color="primary" name="mdi-plus"></q-icon>
                  <span v-if="$q.screen.gt.sm">{{ addLabel }}</span>
                </q-btn>
              </div>
            </slot>
          </div>

        </div>
        <slot name="above"></slot>
      </div>
    </div>
    <q-table
        :rows-per-page-options="[0]"
        flat
        :columns="cols"
        :rows="h$.data"
        hide-no-data
        hide-bottom
        hide-pagination
    >
      <template v-slot:header="scope">
        <q-th auto-width v-if="preMenu"></q-th>
        <q-th
            v-for="col in scope.cols"
            :key="col.name"
            :props="scope"
        >
          {{ col.label }}
        </q-th>
        <q-th v-if="!preMenu"></q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope" @dblclick="useOpenItem(scope.row)">
          <q-td auto-width v-if="preMenu">
            <data-table-menu-btn @edit="editItem = scope.row" @open="useOpenItem(scope.row)">
              <template v-slot:menu>
                <slot name="menu" v-bind="{...scope, setEditing, toggleDialog, editing:editItem, addDialog }"></slot>
              </template>
              <template v-slot:addlItems>
                <slot name="addlItems" v-bind="scope"></slot>
              </template>
            </data-table-menu-btn>
          </q-td>
          <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
            <component v-if="col?.component" :is="col.component" v-bind="col.attrs(scope.row, col)" v-on="col.listeners ? col.listeners(scope.row) : {}"></component>
            <div v-else>{{ col.value }}</div>
          </q-td>
          <q-td v-if="!noMenu && !preMenu">
            <data-table-menu-btn @edit="editItem = scope.row" @open="useOpenItem(scope.row)">
              <template v-slot:menu>
                <slot name="menu" v-bind="{...scope, setEditing, toggleDialog, editing:editItem, addDialog }"></slot>
              </template>
              <template v-slot:addlItems>
                <slot name="addlItems" v-bind="scope"></slot>
              </template>
            </data-table-menu-btn>
          </q-td>
        </q-tr>
      </template>
    </q-table>
    <div class="row justify-end q-pt-sm" v-if="paginate">
      <q-pagination
          :max-pages="maxPages"
          @update:model-value="h$.toPage($event)"
          :model-value="pagination.currentPage"
          :min="1"
          :max="pagination.pageCount"
          direction-links
          boundary-numbers
      ></q-pagination>
    </div>

    <common-dialog
        :model-value="!!editItem || addDialog"
        @update:model-value="toggleDialog"
        :setting="dialogSetting"
        v-bind="dialogAttrs"
    >
      <div class="_fw q-pa-md">
        <slot name="form" v-bind="{toggleDialog, editing:editItem}"></slot>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import DataTableMenuBtn from 'components/common/tables/DataTableMenuBtn.vue';

  import {computed, ref, watch} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';

  const emit = defineEmits(['update:editing']);
  const props = defineProps({
    maxPages: { type: Number, default: 5 },
    preMenu: Boolean,
    noMenu: Boolean,
    addLabel: { default: 'Add New' },
    searchAttrs: Object,
    hideAddBtn: Boolean,
    query: Object,
    searchOff: Boolean,
    qid: String,
    columns: Array,
    columnSettings: Object,
    searchKeys: Array,
    openItem: Function,
    dialogSetting: { default: 'right' },
    dialogAttrs: Object,
    paginate: { default: true },
    store: { required: true },
    params: Object,
    limit: Number,
    editing: { required: false },
    addBtnAttrs: Object
  })


  const useOpenItem = (val) => {
    (props.openItem || (() => console.log('no open fn passed to data table')))(val)
  }

  const addDialog = ref(false);
  const editItem = ref(null);
  const setEditing = (val) => {
    editItem.value = val;
    emit('update:editing', val);
  }
  watch(() => props.editing, (nv) => {
    if(nv) setEditing(nv);
    else editItem.value = null;
  }, { immediate: true })


  const p = computed(() => {
    return {
      query: props.query || {}
    }
  })

  const { searchQ, search } = HQuery({
    store: props.store,
    keys: props.searchKeys
  })
  const { h$, pagination } = HFind({
    store: props.store,
    limit: computed(() => props.limit),
    delay: 1000,
    qid: props.qid || 'data_table',
    params: computed(() => {
      return { ...p.value, query: { ...searchQ.value, ...props.query }, ...props.params }
    })
  })

  const toggleDialog = (val) => {
    if (val) addDialog.value = true;
    else {
      addDialog.value = false;
      editItem.value = null;
    }
  }

  const cols = computed(() => {
    return (props.columns || []).map(a => {
      return {
        label: a.name,
        name: a.name || a.label,
        sortable: true,
        align: 'left',
        field: a.name,
        ...props.columnSettings,
        ...a
      };
    })
  });
</script>

<style lang="scss" scoped>

</style>
