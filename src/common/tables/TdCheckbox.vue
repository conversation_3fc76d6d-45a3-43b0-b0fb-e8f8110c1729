<template>
  <q-checkbox
      :model-value="!!modelValue"
      @update:model-value="emitUp"
      v-bind="$attrs"
  >
  </q-checkbox>
</template>

<script setup>
  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: { required: true }
  })

  const emitUp = () => {
    emit('update:model-value', !props.modelValue)
  }
</script>

<style lang="scss" scoped>

</style>
