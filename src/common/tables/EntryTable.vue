<template>
  <div class="_fw">
    <table class="_fw">
      <slot name="header">
        <tr class="font-3-4r">
          <td v-for="(h, i) in columns" :key="`header-${i}`">
            <slot name="th" :col="h">
              {{ h.label }}
            </slot>
          </td>
        </tr>
      </slot>
      <tr
          class="_fw __row"
          v-for="(row, i) in form || []"
          :key="`r-${i}`"
      >
        <slot name="row" :row="row">
          <td v-for="(d, idx) in columns" :key="`d-${idx}`">
            <slot name="td" :row="form[row]" :col="d">
              <div class="flex items-center _fw relative-position">
                <div class="__col _fw">
                  <q-field
                      :disable="d.disable"
                      :id="`row-${i}:col-${idx}`"
                      dense
                      borderless
                      @update:model-value="setVal(i, idx, $event)"
                      :model-value="getVal(i, idx)"
                      class="text-right _fw"
                      @focus="handleFocus(i, idx)"
                      @blur="setBlur"
                  >
                    <template v-slot:control="{ focused }">
                      <input
                          @paste="handlePaste($event, i, idx)"
                          class="q-field__input _fw text-right"
                          :value="row[idx]"
                          @input="e => setVal(i, idx, e.target.value)"
                          v-show="focused"
                      >
                      <div v-show="!focused" class="text-weight-medium text-right _fw font-3-4r">
                        <div>{{
                            d.format ? d.format(row[idx]) : dollarString(row[idx], d.prefix || '', d.decimal || 0, '')
                          }}
                        </div>
                      </div>
                    </template>
                  </q-field>
                </div>
              </div>
            </slot>
          </td>
        </slot>
      </tr>
    </table>
    <q-btn v-if="addRows" flat icon="mdi-plus" color="primary" @click="addRow"></q-btn>
  </div>
</template>

<script setup>
  import {onBeforeUnmount, onMounted, ref, watch} from 'vue';
  import {_get} from 'symbol-syntax-utils';
  import {dollarString} from 'src/utils/global-methods';
  import {multiCellArray} from 'src/components/common/input/csv-paste';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true, type: Array },
    defs: { default: () => [] },
    addRows: Boolean,
    columns: Array,
    formFn: {
      type: Function,
      default: (defs = []) => {
        return [...(defs || [])];
      }
    }
  });

  const hasFocused = ref(false);
  const focusR = ref(0);
  const focusC = ref(0);

  const handleFocus = (row, col) => {
    hasFocused.value = true;
    focusR.value = Number(row);
    focusC.value = Number(col);
  };

  const formFun = (defs) => {
    return props.formFn(defs || props.defs || []);
  };

  const form = ref(formFun());

  const addRow = () => {
    const arr = [];
    for (const i in props.columns) {
      arr.push(0);
    }
    form.value[`${Object.keys(form.value || {}).length}`] = arr;
  }

  const getVal = (row, column) => {
    return _get(form.value, `[${row}][${column}]`, 0);
  };

  const setVal = (row, column, val) => {
    const v = Number(val);
    form.value[row][column] = v;
  };

  const setBlur = () => {
    emit('update:model-value', form.value);
  }

  watch(() => props.modelValue, (nv, ov) => {
    if (nv) {
      form.value = formFun(nv);
    }
  }, { immediate: true });

  let tabListener;
  const senseTab = () => {
    tabListener = document.addEventListener('keydown', (e) => {
      if (hasFocused.value && e.keyCode === 9) {
        const adder = focusC.value === props.columns?.length - 1 || 0;
        const nextId = `row-${focusR.value + (adder ? 1 : 0)}:col-${focusC.value}`;
        const el = document.getElementById(nextId);
        if (el) el.focus();
      }
    });
  };

  const handlePaste = (evt, r, c) => {
    evt.preventDefault();
    let nr = Number(r);
    const arrays = multiCellArray(evt.clipboardData.getData('text').trim());
    console.log('array', arrays);
    const cols = (props.columns || []).filter(a => !a.disable);
    for (let i = 0; i + nr < form.value.length && i < arrays.length; i++) {
      const column = i === 0 ? c : 0;
      console.log('paste row?', i, column, cols);
      for (let idx = 0; column + idx < cols.length; idx++) {
        console.log('paste column', idx, cols[idx].disable);

        console.log('pasting', i, c + idx, arrays[i][idx]);
        form.value[i][c + idx] = Number(arrays[i][idx] || 0);

      }
    }
    setBlur();
  };

  onMounted(() => {
    senseTab();
  });

  onBeforeUnmount(() => {
    if (tabListener) document.removeEventListener(tabListener);
  });

</script>

<style scoped lang="scss">
  .__col {
    height: 30px;
    display: flex;
    align-items: center;
    position: relative;
  }

  table {
    width: 100%;
    text-align: right;
    border-collapse: collapse;

    .__row {
      width: 1%;
      white-space: nowrap;

      td {
        padding: 2px 5px;
        border: solid .2px #999;
      }
    }
  }

</style>
