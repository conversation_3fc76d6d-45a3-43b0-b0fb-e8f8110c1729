<template>
  <div class="_fw">
  <q-chip
      clickable
      @click="open = true"
      v-bind="{
        label: recurrenceString(modelValue),
        iconRight: 'mdi-menu-down',
        ...$attrs
      }"
  >

  </q-chip>
    <q-slide-transition>
      <div class="_fw" v-if="open">
        <r-rule-form :model-value="modelValue" @update:model-value="emitUp"></r-rule-form>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import {recurrenceString} from 'src/utils/recurrence';
  import {ref} from 'vue';
  import RRuleForm from 'src/components/common/recurrance/RRuleForm.vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object
  })

  const open = ref(false);

  const emitUp = (val) => {
    emit('update:model-value', val);
    open.value = false;
  }
</script>

<style lang="scss" scoped>

</style>
