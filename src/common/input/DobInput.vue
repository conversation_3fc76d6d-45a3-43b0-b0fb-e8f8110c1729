<template>
  <q-input
      v-bind="{
    mask: '##/##/####',
    modelValue: stringVal,
    error: !!error,
    errorMessage: error,
    placeholder: 'MM/DD/YYYY',
    label: 'Birth date MM/DD/YYYY',
    ...$attrs
      }"
      @update:model-value="checkDate"
  >
  </q-input>
</template>

<script setup>
  import {date} from 'quasar';
  import {computed, ref} from 'vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true }
  });

  const error = ref('');

  const checkDate = (val) => {
    if (val.length === 10) {
      const arr = val.split('/');
      if (arr[0] > 12) error.value = 'Invalid Month - must be between 01 and 12';
      else if (arr[2] < (new Date().getFullYear() - 100)) error.value = 'Invalid year - you\'re not that old';
      else if (arr[2] > (new Date().getFullYear())) error.value = 'Invalid year - unless you\'re from the future';
      else {
        const valid = date.isValid(val);
        if (!valid) error.value = 'Invalid date';
        else {
          error.value = '';
          emit('update:model-value', new Date(val).toString());
        }
      }

    }
  };

  const stringVal = computed(() => {
    if (date.isValid(props.modelValue)) return date.formatDate(props.modelValue, 'MM/DD/YYYY');
    else return '';
  })

</script>

<style lang="scss" scoped>

</style>
