<template>
  <div class="_fw">
    <template v-if="show">

      <phone-input
          v-if="type === 'phone'"
          :input-attrs="{ dense: true, class: 'font-3-4r', ...fieldAttrs }"
          @focus="emit('edit')"
          @blur="save"
          @keyup.enter="save"
          @update:model-value="add"
          :model-value="(form.plural || [])[editIndex]"
      ></phone-input>

      <email-field
          v-if="type === 'email'"
          @focus="edit"
          @blur="save(editIndex > -2 ? 'plural': 'single')"
          @keyup.enter="save(editIndex > -2 ? 'plural': 'single', true)"
          :model-value="(form.plural || [])[editIndex]"
          @update:model-value="add"
          v-bind="{
            icon: null,
            dense: true,
            hideBottomSpace: true,
            ...fieldAttrs
          }"
      ></email-field>
      <q-slide-transition>
        <div v-if="validateError" class="text-red-8 font-3-4r">{{ vErrorText }}</div>
      </q-slide-transition>
    </template>


    <div class="flex items-center">

      <q-chip
          v-for="(item, i) in list || []"
          :key="`${type}-${i}`"
          :id="`Go_${type}_${i}`"
          v-bind="{
                ...chip,
                removable: true,
                label: getLabel(item.value),
                icon: item.primary ? 'mdi-star' : undefined
              }"
          @remove="remove(i)"
          @click.stop="$copyTextToClipboard(getLabel(item.value), `${type} copied`)"
          @touch="edit(i)"
          @dblclick="edit(i)"
      >
        <div :id="`Menu_${i}`" class="__menu_wrap">
          <q-menu
              :model-value="cmId === i"
              @before-hide="cmId = ''"
          >
            <q-card>
              <q-item dense clickable @click="makePrimary(item.value)">
                <q-item-section>
                  <q-item-label>Make Primary</q-item-label>
                </q-item-section>
              </q-item>
            </q-card>
          </q-menu>
        </div>
      </q-chip>
      <q-btn v-if="!show" size="sm" round icon="mdi-plus" flat color="primary"
             @click="edit"></q-btn>
    </div>
  </div>
</template>

<script setup>

  import {computed, onMounted, ref, watch} from 'vue';
  import {_flatten, ContextMenu} from 'symbol-syntax-utils';
  import {$copyTextToClipboard} from '../../utils/global-methods';
  import PhoneInput from '../phone/PhoneInput.vue';
  import EmailField from '../input/EmailField.vue';
  import {isEmailRule} from '../../utils/validators/validators';


  const emit = defineEmits(['update:plural', 'update:single', 'edit']);
  const props = defineProps({
    show: Boolean,
    plural: Array,
    single: { required: true },
    type: { type: String, required: true },
    chip: Object,
    fieldAttrs: Object,
    delay: { type: Number, default: 3000 }
  });


  const form = ref({
    single: '',
    plural: []
  });

  const model = computed(() => {
    return {
      single: props.single,
      plural: props.plural
    };
  });

  const validateError = ref(false);
  let to;
  let saveArgs;
  const add = (val, done) => {
    if (editIndex.value > -2) {
      form.value.plural?.splice(editIndex.value, 1, val);
      saveArgs = ['plural', true];
    } else {
      const arr = form.value.plural ? [...form.value.plural.map(a => a).filter(a => getVal(a) !== getVal(val)), val] : [val];
      editIndex.value = arr.length - 1;
      form.value.plural = arr;
      saveArgs = ['plural', true];
    }
    if (done) {
      editIndex.value = -2;
      save(...saveArgs);
      saveArgs = [];

      // console.log('adding phone', editPhoneIndex, val);
    } else {
      window.clearTimeout(to);
      to = window.setTimeout(() => {
        save(...saveArgs);
      }, props.delay);
    }
  };

  const save = (path, editing) => {

    window.clearTimeout(to);
    const emitVal = (p, val) => {
      // console.log('emit val', val, p);
      if (p === 'single') {
        let invalid = '';
        const arr = typeof val === 'string' ? val.split(',') : [val];
        arr.map((a, i) => {
          const trimmed = typeof a === 'string' ? a.trim() : a;
          const v = validate(trimmed);
          if (!v){
            validateError.value = true;
            invalid += `${getVal(trimmed)}${i < arr.length - 1 ? ', ' : ''}`;
          }
          else {
            emit('update:single', trimmed);
            if (!form.value.plural) {
              form.value.plural = [trimmed];
              // console.log('emitting plural from single');
              emit('update:plural', form.value.plural);
            }
            editIndex.value = -2;
          }
        });
        form.value.single = invalid;

      } else {
        if (!Array.isArray(val)) val = [val];
        let invalid = '';
        const validated = val.map(b => {
          const arr = typeof b === 'string' ? b.split(',') : [b];
          return arr.map((a, i) => {
            const trimmed = typeof a === 'string' ? a.trim() : a;
            const v = validate(trimmed);
            if (!v){
              validateError.value = true;
              invalid += `${getVal(trimmed)}${i < arr.length - 1 ? ', ' : ''}`;
              return null;
            } else {
              if(!validate(form.value.single)){
                form.value.single = trimmed;
                emit('update:single', trimmed);
              }
              editIndex.value = -2;
              return trimmed;
            }
          });
        });
        const flat = _flatten(validated).filter(a => !!a);
        if(form.value.plural?.length) form.value.plural = [...form.value.plural.filter(a => !flat.map(b => getVal(b)).includes(getVal(a))), ...flat];
        else form.value.plural = flat;
        emit('update:plural', form.value.plural);
      }
    };


    if (path) {
      emitVal(path, form.value[path]);
    } else {
      emitVal('single', form.value.single);
      emitVal('plural', form.value.plural);
    }
    if (editing) emit('edit');
  };

  watch(model, (nv) => {
    form.value = Object.assign({}, nv);
  }, { immediate: true });

  onMounted(() => {
    form.value[props.type] = props.single;
  });

  const cmId = ref('');
  let debounceRelease;
  const openCm = (position, e) => {
    debounceRelease = true;
    const { rect, id } = position;
    cmId.value = id;
    const { top, left } = rect;
    const y = e.clientY - top;
    const x = e.clientX - left;
    const menuEl = document.getElementById(`Menu_${id}`);
    if (menuEl) {
      Object.assign(menuEl.style, {
        left: `${x}px`,
        top: `${y}px`
      });
    }
    setTimeout(() => {
      debounceRelease = false;
    }, 1000);
  };

  const makePrimary = (val) => {
    form.value.single = val;
  };

  const getLabel = (val) => {
    const types = {
      'phone': val?.number?.national,
      'email': val
    };
    return types[props.type];
  };

  const getVal = (val) => {
    const types = {
      'phone': val?.number?.e164,
      'email': val
    };
    return types[props.type];
  };

  const vErrorText = computed(() => {
    const types = {
      'phone': 'Enter a valid phone number',
      'email': 'Enter a valid email'
    };
    return types[props.type];
  });

  const validate = (val) => {
    const types = {
      'phone': val?.valid,
      'email': isEmailRule(val)
    };
    return types[props.type];
  };

  const editIndex = ref(-2);
  const edit = (idx) => {
    editIndex.value = idx;
    emit('edit');
  };

  const list = computed(() => {
    const single = !!form.value?.single;
    const arr = (form.value.plural || []).filter(a => getVal(a) !== getVal(form.value.single)).map((a, i) => {
      return {
        value: a,
        primary: false,
        id: i + single ? 1 : 0
      };
    });
    if(single) arr.unshift({ value: form.value.single, primary: true, id: 0 })
    return arr;
  });

  let CM;
  watch(list, (nv, ov) => {
    if (nv && nv.length !== ov?.length || 0) {
      if (CM?.cancel) CM.cancel();
      CM = new ContextMenu(nv.map(a => a.id), openCm, `Go_${props.type}_`, { log: false, delay: 1000 });
    }
  }, { immediate: true });


  const remove = idx => {
    const val = form.value.plural[idx];
    if (getVal(form.value.single) === getVal(val)) form.value.single = undefined;
    if (form.value.plural) form.value.plural.splice(idx, 1);
    save();
  };

</script>

<style scoped>
  .__menu_wrap {
    position: absolute;
    top: 0;
    left: 0;
  }
</style>
