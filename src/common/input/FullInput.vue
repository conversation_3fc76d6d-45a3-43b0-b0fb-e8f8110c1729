<template>
  <div class="_fw">
    <q-editor
        v-bind="{
          dense: $q.screen.lt.md,
          modelValue: text || '',
          placeholder,
          minHeight: '30px',
          flat: true,
          contentStyle: { borderRadius: '5px' },
          contentClass: 'bg-ir-grey-2 q-px-md',
          toolbar: [['left', 'right', 'center', 'justify'],['bold', 'italic', 'strike', 'underline', 'code', 'link'], ['unordered', 'ordered'],[{ label: '', icon: 'mdi-format-text-variant', list: 'no-icons', options: ['p','h1','h2','h3','h4','h5','h6','code']}]],
          ...$attrs
        }"
        @update:model-value="handleChange"

    ></q-editor>
    <div class="q-px-md q-pt-sm">
      <emoji-picker @update:model-value="pickEmoji"></emoji-picker>
    </div>
  </div>
</template>

<script setup>

  import EmojiPicker from 'components/common/input/EmojiPicker.vue';
  import {ref, watch} from 'vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    delay: Boolean,
    placeholder: { default: 'Enter value...'}
  })
  const text = ref('');
  const pickEmoji = (val) => {
    text.value = `${text.value || ''}${val}`;
    if(!props.delay) emit('update:model-value', text.value);
  }

  const handleChange = (val) => {
    text.value = val
    if(!props.delay) emit('update:model-value', text.value);
  }

  watch(() => props.modelValue, (nv) => {
    if(nv !== text.value) text.value = nv;
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
