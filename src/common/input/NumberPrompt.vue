<template>
  <div class="__num_grid text-weight-bold z1">
    <div :class="`__number_box ${modelValue === num ? '__active_num' : ''}`" v-for="(num, i) in numbers"
         :key="`num-${i}`" @click="$emit('update:model-value', num)">
      <div>
        {{ currencyIcon ? dollarString(num, currencyIcon, currencyDecimal) : num }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

  const props = defineProps({
    modelValue: Number,
    numbers: Array,
    currencyIcon: String,
    currencyDecimal: { type: Number, default: 0 }
  });

  const dollarString = (val: string | number, symbol = '$', dec?: number, def = 'N/A'): string => {
    const v = typeof val !== 'number' ? parseFloat(val) : val;
    if(isNaN(v)) return def;
    // console.log('dollar string', val, v)
    const decimal = dec || dec === 0 ? dec : 2;
    const valDec = v.toFixed(decimal);
    return (symbol) + valDec.toString().replace(/\B(?=(\d{3})+(?!\d))/g,',');

  }

</script>

<style scoped lang="scss">
  .__num_grid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, 100px);
  }

  .__number_box {
    font-size: .8rem;
    font-weight: 600;
    cursor: pointer;
    border-radius: 4px;
    //border: solid 2px #d6d6d6;
    margin: 3px;
    //box-shadow: 0px 0px 4px -2px #999;
    transition: all .2s ease-in;
    display: flex;
    justify-content: center;
    align-items: center;
    //color: #999;
    color: var(--q-p6);
    background: #f4f4f4;
    padding: 4px 4px;
    z-index: 1;

    &:hover {
      background: var(--q-p0);
    }
  }

  .__active_num {
    color: white !important;
    background: var(--q-p4) !important;
  }

  @media(hover: hover) and (pointer: fine) {
    .__number_box:hover {
      opacity: .8
    }
  }
</style>
