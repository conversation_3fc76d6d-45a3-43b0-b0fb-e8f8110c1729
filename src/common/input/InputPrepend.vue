<template>
  <q-input v-bind="{...inputAttrs}" @update:model-value="handleInput">
    <template v-slot:prepend>
      <slot name="prepend">
        <q-icon v-if="icon" :name="icon"></q-icon>
        <q-avatar v-if="avatar" :src="avatar"></q-avatar>
      </slot>
    </template>

    <template v-slot:append>
      <slot name="append"></slot>
    </template>

    <template v-slot:after>
      <slot name="after"></slot>
    </template>
  </q-input>
</template>

<script setup>

  const emit = defineEmits(['update:model-value']);

  const props = defineProps({
    inputAttrs: Object,
    icon: String,
    avatar: String
  });

  const handleInput = val => {
    emit('update:model-value', val);
  };

</script>

<style scoped>

</style>
