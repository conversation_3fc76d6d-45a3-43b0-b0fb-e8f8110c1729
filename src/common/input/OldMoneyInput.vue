<template>
  <q-field
      :style="{textAlign: 'right', ...inputStyle}"
      v-bind="{stackLabel: true, inputClass: inputClass || 'tw-five num-font', class: 'text-right', ...attrs}"
      :model-value="modelValue"
      @update:model-value="handleInput"
      @focus="emit('focus')"
      @blur="emit('blur')"
  >
    <template v-slot:append>
      <slot name="append"></slot>
    </template>
    <template v-slot:control="{ focused, emitValue }">
      <input
          type="number"
          :style="{width: '100%', ...inputStyle }"
          :class="`q-field__input ${inputClass || 'tw-five num-font'}`"
          :value="modelValue"
          @input="e => emitValue(e.target.value)"
          v-show="focused"
      >
      <span v-show="!focused" :class="inputClass || 'tw-five num-font'">{{ modelValue ? dollarString(modelValue || 0, prefix, decimal, suffix) : def }}</span>
<!--      <span v-show="!focused">{{ modelValue }}</span>-->
      <span v-show="suffix && !focused" :class="inputClass || 'tw-five num-font'">&nbsp;{{ suffix }}</span>
    </template>
    <template v-slot:after>
      <slot name="after"></slot>
    </template>
  </q-field>
</template>

<script setup>
  import {useAttrs} from 'vue';

  const attrs = useAttrs();
  const emit = defineEmits(['update:model-value', 'focus', 'blur']);
  const props = defineProps({
    inputClass: String,
    inputStyle: Object,
    def: { default: 0 },
    modelValue: { required: true },
    prefix: String,
    suffix: String,
    decimal: { type: [String,Number], default: 0 }
  });

  const handleInput = (val) => {
    emit('update:model-value', Number(val));
  };

  const dollarString = (val, symbol = '$', dec) => {
    const v = typeof val !== 'number' ? parseFloat(val) : val;
    const decimal = dec || dec === 0 ? dec : 2;
    const valDec = v.toFixed(Number(decimal));
    return (symbol) + valDec.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };


</script>

<style scoped>

</style>
