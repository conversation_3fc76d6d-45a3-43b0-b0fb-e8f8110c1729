<template>
  <div :id="id || 'PersonEmojiPicker'" class="_fw">
    <div class="flex items-center cursor-pointer">
      <div class="__emoji" v-if="modelValue">
        <!--        {{ modelValue }}-->
        {{String.fromCodePoint(...(emojiArray(modelValue) || []).map(a => parseInt(a)))}}
      </div>
      <q-icon v-else :size="iconSize" name="mdi-face-man" color="ir-grey-6"></q-icon>
      <q-icon v-if="!noIcon" :size="parseFloat(iconSize || 20) + 'px'" name="mdi-menu-down"></q-icon>
    </div>
    <q-popup-proxy>
      <div class="__sel q-pa-sm bg-white">

        <div class="__top">
          <div class="row no-wrap">
            <div class="col-grow">
              <q-input
                  clearable
                  @clear="clearInput"
                  v-model="searchInput"
                  @update:model-value="resetScroll"
                  v-bind="{
                    dense: true,
                    class: '_fw',
                    options: options,
                    ...inputAttrs
                  }"
              >
                <template v-slot:prepend>
                  <q-icon class="cursor-pointer" @click="focus = true" v-if="!tagsChosen?.length" name="mdi-magnify"
                          :color="focus ? 'primary' : 'ir-grey-5'"></q-icon>
                  <div class="flex items-center" v-else>
                    <q-chip v-for="(tag, i) in tagsChosen" :key="`tagC-${i}`" color="black" outline :label="tag"
                            removable
                            @remove="tagsChosen.splice(tagsChosen.indexOf(tag), 1)"></q-chip>
                  </div>
                </template>
              </q-input>
            </div>
            <div class="col-shrink">
              <q-chip clickable icon-right="mdi-menu-down" color="white">
                <div class="q-px-xs">
                  {{ '🖖' + (modifiers[skinTone]?.emoji || '') }}
                </div>
                <div>Skin Color</div>
                <q-menu>
                  <q-list dense separator>
                    <q-item clickable @click="skinTone = ''">
                      <q-item-section avatar style="font-size: 30px">
                        🖖
                      </q-item-section>
                      <q-item-section>
                        <q-item-label caption class="text-uppercase">None</q-item-label>
                      </q-item-section>
                    </q-item>
                    <q-item v-for="(item, i) in Object.keys(modifiers)" :key="`mod-${i}`" clickable
                            @click="skinTone = modifiers[item].label">
                      <q-item-section avatar style="font-size: 30px;">
                        {{ '🖖' + modifiers[item].emoji }}
                      </q-item-section>
                      <q-item-section>
                        <q-item-label caption class="text-uppercase">{{ modifiers[item].label }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </q-chip>
            </div>
          </div>

          <div class="_fw">
            <div class="row q-py-xs __chips">
              <q-chip clickable :label="tag" color="black" text-color="white" class="alt-font text-weight-medium"
                      v-for="(tag, i) in tags" :key="`tag-${i}`" @click.prevent="chooseTag(tag)"></q-chip>
            </div>
          </div>
        </div>

        <div id="__Bottom__" class="__bottom">
          <template v-if="matches?.length">
            <div class="q-pt-sm text-1 alt-font text-weight-bold text-ir-grey-7 q-px-sm">Suggested</div>
            <div class="row items-center">
              <div v-for="(ji, i) in matches" :key="`ji-${i}`" class="q-pa-sm"
                   :style="{ fontSize: emojiSize || '28px' }">
                <div class="__ji" @click="handleInput(ji)">
                  {{String.fromCodePoint(...emojiArray(ji))}}
                </div>
              </div>
            </div>
          </template>
          <div v-if="matches?.length" class="q-pt-sm text-1 alt-font text-weight-bold text-ir-grey-7 q-px-sm">More
            options
          </div>
          <div class="row items-center">
            <div v-for="(ji, i) in options" :key="`ji-${i}`" class="q-pa-sm"
                 :style="{ fontSize: emojiSize || '28px' }">
              <div class="__ji" @click="handleInput(ji)">
                {{String.fromCodePoint(...emojiArray(ji))}}
              </div>
            </div>
          </div>
          <div class="row items-center justify-center">
            <q-btn @click="page+=2" :disable="page > pageCount - 1" size="sm" flat class="q-mx-xs"
                   icon-right="mdi-menu-down" label="more"></q-btn>
          </div>
        </div>
      </div>
    </q-popup-proxy>
  </div>
</template>

<script setup>
  import {computed, ref} from 'vue';
  import {allEmojis} from './all-emojis';
  import {$searchExactMatch, $searchOrMatch} from 'src/utils/global-methods';

  const props = defineProps({
    noIcon: Boolean,
    inputAttrs: Object,
    selectAttrs: Object,
    emojiSize: { type: String },
    modelValue: String,
    id: String,
    groupFilter: String,
    perPage: { type: Number, default: 50 },
    iconSize: { default: '20px' }
  });

  const emit = defineEmits(['update:model-value']);

  const searchInput = ref('');
  const tagsChosen = ref([]);

  const page = ref(1);
  const skinTone = ref('');

  const emojiArray = str => {
    return str.split('#').filter(a => a !== '&').map(a => a.indexOf(';') > -1 ? `0${a.split(';')[0]}` : `0${a}`);
  };

  const resetScroll = () => {
    page.value = 1;
    const el = document.getElementById('__Bottom__');
    el.scroll(0, 0);
  };

  const chooseTag = val => {
    const idx = tagsChosen.value.indexOf(val) || -1;
    if (idx === -1) tagsChosen.value.push(val);
    resetScroll();
  };

  const all = computed(() => {
    return allEmojis(props.groupFilter);
  });

  const modifiers = ref({
    light: { label: 'light', value: '\u{1f3fb}', emoji: '🏻' },
    'medium-light': { label: 'medium-light', value: '\u{1f3fc}', emoji: '🏼' },
    medium: { label: 'medium', value: '\u{1f3fd}', emoji: '🏽' },
    'medium-dark': { label: 'medium-dark', value: '\u{1f3fe}', emoji: '🏾' },
    dark: { label: 'dark', value: '\u{1f3ff}', emoji: '🏿' },
  });


  const matches = computed(() => {
    const list = Object.keys(all.value);
    let filterList = searchInput.value.split(' ') || [];
    let filterFn = (key) => {
      return filterList.map(sv => sv.toLowerCase().trim()).every(v => key.toLowerCase().includes(v));
    };
    if(tagsChosen.value) filterList = [...filterList, ...tagsChosen.value];
    if(skinTone.value) filterList = [...filterList, skinTone.value, 'Skin', 'Tone'];

    return list.filter(a => filterFn(a)).slice(0, 20).map(a => all.value[a]);
  });

  const options = computed(() => {
    const list = Object.keys(all.value).map(a => {
      return { emoji: all.value[a], ...all.value[a], name: a};
    });
    const searchVal = tagsChosen.value ? tagsChosen.value.join(' ') + searchInput.value : searchInput.value;
    return [...$searchExactMatch(list, searchVal, ['name']).map(a => a.emoji), ...$searchOrMatch(list, searchVal, ['name']).map(a => a['emoji'])].slice(page.value - 1, (props.perPage || 50) * page.value).filter(a =>
        !matches.value.includes(a));
  });

  const pageCount = computed(() => {
    return Object.keys(all.value).length / (props.perPage || 30);
  });

  const tags = computed(() => {
    const conj = ['with', 'in', 'on', 'of', 'a'];
    return [
      'woman',
      'man',
      'child',
      'curly',
      'blonde',
      'red',
      'medium',
      'dark',
      'light',
      'old',
      'boy',
      'girl',
      'headscarf',
      'turban',
      'wheelchair',
      ...Array.from(new Set(Object.keys(all.value).map((key) => key.split(' ').filter(a => conj.indexOf(a) === -1)).flat(1)))
      // ]
    ].filter(a => a.indexOf(searchInput.value.toLowerCase()) > -1).slice(0, 30).map(a => a.indexOf(':') > -1 ? a.split(':')[0] : a);
  });


  const handleInput = val => {
    emit('update:model-value', val);
  };

  const clearInput = () => {
    searchInput.value = '';
    tagsChosen.value = [];
  };

  // const clickOutside = evt => {
  //   const boundary = document.getElementById(props.id || 'PersonEmojiPicker').getBoundingClientRect();
  //   const right = evt.clientX > boundary.right;
  //   // console.log('right', right, evt.clientX, boundary.right, boundary.width);
  //   const left = evt.clientX < boundary.left;
  //   // console.log('left', left, evt.clientX, boundary.left);
  //   const top = evt.clientY < boundary.top;
  //   // console.log('top', top, evt.clientY, boundary.top);
  //   const bottom = evt.clientY > boundary.bottom;
  //   // console.log('bottom', bottom, evt.clientY, boundary.bottom, boundary.height);
  //   if(right || left || top || bottom) focus.value = false;
  // }


</script>

<style scoped lang="scss">
  .__emoji {
    font-size: 70px;
  }

  .__sel {
    width: 380px;
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 10px -5px black;
    height: 280px;
    max-height: 100%;
    display: grid;
    grid-template-rows: 90px 1fr;
    grid-template-columns: 100%;
  }

  .__top {
    width: 100%;
    height: 100%;
  }

  .__bottom {
    max-height: 100%;
    overflow-y: scroll;
  }

  .__ji {
    cursor: pointer;
    transform: scale(1);
    transition: all .2s;
  }

  .__ji:hover {
    transform: scale(2);
  }

  .__chips {
    overflow-x: scroll;
    flex-wrap: nowrap;
  }
</style>
