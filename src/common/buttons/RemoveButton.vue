<template>
  <div v-bind="{ class: 'flex items-center cursor-pointer', ...divAttrs}">
    <div class="q-mr-sm">
      {{ approved ? `Confirm Remove ${name}?` : `${label}` }}
    </div>
    <q-btn @click="approved = true" v-if="!approved" v-bind="{dense: true, flat: true, color: 'red', icon: 'mdi-delete', ...base}"></q-btn>
    <template v-else>
      <q-btn size="sm" v-bind="cancel" @click.stop="approved = false"></q-btn>
      <q-btn size="sm" v-bind="confirm" @click.stop="$emit('remove')"></q-btn>
    </template>
  </div>
</template>

<script setup>
  import {ref} from 'vue';

  const emit = defineEmits(['remove']);
  const props = defineProps({
    divAttrs: Object,
    name: { default: '' },
    label: { default: 'Remove' },
    base: Object,
    confirm: {
      default: () => {
        return {
          dense: true,
          color: 'red',
          flat: true,
          icon: 'mdi-delete'
        }
      }
    },
    cancel: {
      default: () => {
        return {
          dense: true,
          color: 'black',
          flat: true,
          icon: 'mdi-cancel'
        }
      }
    }
  })

  const approved = ref(false);
</script>

<style lang="scss" scoped>

</style>
