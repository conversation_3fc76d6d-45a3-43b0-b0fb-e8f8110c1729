<template>
  <q-item  v-bind="{ clickable: true, ...$attrs}">
    <q-item-section v-if="!iconAfter" avatar>
      <q-icon v-bind="{ name: 'mdi-plus', color: 'primary', ...icon }"></q-icon>
    </q-item-section>
    <q-item-section>
      <q-item-label v-bind="labelAttrs">{{label}}</q-item-label>
      <slot name="caption"></slot>
    </q-item-section>
    <slot name="menu"></slot>
  </q-item>
</template>

<script setup>
  const props = defineProps({
    iconAfter: Boolean,
    icon: Object,
    labelAttrs: Object,
    label: { default: 'Add New' }
  })
</script>

<style lang="scss" scoped>

</style>
