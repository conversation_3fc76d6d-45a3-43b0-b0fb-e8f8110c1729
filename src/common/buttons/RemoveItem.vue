<template>
  <q-item v-bind="{class: '_fw', clickable: true, ...$attrs}" @click.stop="approved = true">
    <q-item-section>
      <q-item-label>{{ approved ? confirmLabel || `Confirm remove ${name}?` : `${label}` }}</q-item-label>
    </q-item-section>
    <q-item-section side>
      <q-icon v-if="!approved" v-bind="icon"></q-icon>
      <div v-else class="__s">
        <q-btn size="sm" v-bind="cancel" @click.stop="approved = false"></q-btn>
        <q-btn size="sm" v-bind="confirm" @click.stop="$emit('remove')"></q-btn>
      </div>

    </q-item-section>
  </q-item>
</template>

<script setup>
  import {ref} from 'vue';

  const emit = defineEmits(['remove']);
  const props = defineProps({
    name: { default: 'this' },
    confirmLabel: String,
    label: { default: 'Remove' },
    icon: {
      default: () => {
        return {
          name: 'mdi-delete',
          color: 'red'
        }
      }
    },
    confirm: {
      default: () => {
        return {
          dense: true,
          label: 'Yes',
          color: 'black',
          flat: true,
          icon: 'mdi-delete'
        }
      }
    },
    cancel: {
      default: () => {
        return {
          dense: true,
          label: 'No',
          color: 'black',
          flat: true,
          icon: 'mdi-cancel'
        }
      }
    }
  })

  const approved = ref(false);
</script>

<style lang="scss" scoped>
  .__s {
    display: flex;
    align-items: center;
  }
</style>
