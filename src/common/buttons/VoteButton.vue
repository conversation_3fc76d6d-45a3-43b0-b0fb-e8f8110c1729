<template>
  <div class="flex items-center">
    <q-btn :color="isUpVoted ? 'positive' : 'black'" dense round size="sm" flat @click="vote('up')">
      <q-tooltip>{{isUpVoted ? 'Undo Up Vote' : 'Up Vote'}}</q-tooltip>
      <q-icon :size="size" name="mdi-menu-up"></q-icon>
    </q-btn>
    <div class="text-weight-bolder num-font" :style="{ fontSize: parseInt(size)/2 + 'px' }">
      {{dollarString(modelValue?.voteCount || 0, '', 0)}}
    </div>
    <q-btn :color="isDownVoted ? 'negative' : 'black'" dense round size="sm" flat @click="vote('down')">
      <q-tooltip>{{isDownVoted ? 'Undo Down Vote' : 'Down Vote'}}</q-tooltip>
      <q-icon :size="size" name="mdi-menu-down"></q-icon>
    </q-btn>
  </div>
</template>

<script setup>
  import {computed, ref} from 'vue';
  import { dollarString } from 'src/utils/global-methods';

  const props = defineProps({
    modelValue: { required: true },
    store: { required: true },
    userId: { type: String, required: true },
    up: { default: 'userUpVotes' },
    down: { default: 'userDownVotes' },
    size: { type: String, default: '30px' }
  });
  const emit = defineEmits(['update:model-value']);

  const saving = ref(false);

  const isUpVoted = computed(() => props.modelValue[props['up']])
  const isDownVoted = computed(() => props.modelValue[props['down']])

  const vote = async (direction) => {
    if (props.userId) {
      const existing = props.modelValue[props[direction]];
      saving.value = true;
      const patchObj = { [props[direction]]: !existing };
      const voted = await props.store.patch(props.modelValue._id, patchObj, { voting: true })
          .catch(err => console.error(err));
      console.log('voted', voted);
      saving.value = false;
      emit('update:model-value', voted);
    }
  }
</script>

<style lang="scss" scoped>

</style>
