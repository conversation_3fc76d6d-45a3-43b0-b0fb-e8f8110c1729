<template>
  <q-btn v-bind="{ dense: true, flat: true, ...$attrs}" @click="dialog = true">
    <slot name="label">
      <span v-if="label" class="q-mr-sm text-ir-text">{{ label }}</span>
    </slot>
    <slot name="icon">
      <q-icon v-bind="{ color, name: icon, ...iconAttrs }"></q-icon>
    </slot>
    <slot name="default" v-bind="{ dialog }"></slot>

    <q-popup-proxy v-if="dialog" v-model="dialog" :breakpoint="twoStep ? 500000 : undefined">
      <div class="q-pa-md bg-ir-bg1 text-ir-text">
        <div class="tw-five">
          <span v-if="removeLabel" v-html="removeLabel"></span>
          <span v-else>Remove {{ name || 'this' }}?</span>
        </div>

        <div v-if="twoStep" class="_fw q-py-md">
          <div class="_fw text-center q-pb-sm">
            <div class="font-3-4r">Enter phrase to confirm</div>
            <div class="font-1r tw-six">{{ phrase }}</div>
          </div>
          <q-input dense filled placeholder="Enter Phrase Here" v-model="confirm"></q-input>
        </div>

        <div class="row justify-end q-pt-sm">
          <q-btn no-caps flat size="sm" @click="dialog = false">
            <span>Cancel</span>
            <q-icon class="q-ml-sm" color="red" name="mdi-close"></q-icon>
          </q-btn>
          <q-btn :disable="twoStep && confirm !== phrase" no-caps flat size="sm" @click="remove">
            <span>Yes</span>
            <q-icon class="q-ml-sm" color="green" name="mdi-check"></q-icon>
          </q-btn>
        </div>
      </div>
    </q-popup-proxy>
  </q-btn>
</template>

<script setup>
  import {onMounted, ref} from 'vue';

  const emit = defineEmits(['remove']);
  const props = defineProps({
    name: String,
    removeLabel: String,
    twoStep: Boolean,
    label: { default: '' },
    color: { default: 'red' },
    icon: { default: 'mdi-delete' },
    iconAttrs: Object
  })

  const letters = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
  const dialog = ref(false);
  const confirm = ref('');
  const phrase = ref('ekqo');

  onMounted(() => {
    if (props.twoStep) {
      let ph = '';
      for (let i = 0; i < 4; i++) {
        ph += letters[Math.round(Math.random() * letters.length)];
      }
      phrase.value = ph;
    }
  })

  const remove = () => {
    emit('remove');
    dialog.value = false;
  }
</script>

<style lang="scss" scoped>

</style>
