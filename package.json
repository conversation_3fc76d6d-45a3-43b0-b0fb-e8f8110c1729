{"name": "@ha6755ad/common-client", "version": "0.1.0", "publishConfig": {"registry": "https://npm.pkg.github.com"}, "description": "CommonCare common client app utilities and components", "source": "src/index.ts", "unpkg": "lib/index.umd.js", "main": "lib/index.js", "module": "lib/index.module.js", "types": "lib/index.d.ts", "type": "module", "exports": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "default": "./lib/index.modern.js"}, "scripts": {"install:local": "dotenv npm install", "test": "test", "prebuild": "rimraf lib dist && node scripts/gen-version.js", "build": "rm -rf lib && microbundle --tsconfig tsconfig.json", "dev": "microbundle --watch --tsconfig tsconfig.json --no-sourcemap"}, "files": ["lib"], "repository": {"type": "git", "url": "git+https://gitlab.com/symbolsyntax/symbol-client.git"}, "author": "CommonCare LLC", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "eslint": "^8.10.0", "eslint-config-prettier": "^8.1.0", "microbundle": "^0.15.1", "prettier": "^2.5.1", "typescript": "^4.5.4"}, "dependencies": {"@feathersjs/authentication": "^5.0.11", "@turf/turf": "^7.2.0", "@ucans/ucans": "^0.12.0", "feathers-pinia": "^4.5.4", "long-timeout": "^0.1.1", "parse-domain": "^8.2.2", "radash": "^11.0.0", "rrule": "^2.8.1", "sanitize-html": "^2.16.0", "symbol-syntax-utils": "^0.0.22", "symbol-ucan": "^0.0.6"}, "peerDependencies": {"quasar": "^2.0.0", "vue": "^3.0.0", "vue-router": "^4.0.0"}}